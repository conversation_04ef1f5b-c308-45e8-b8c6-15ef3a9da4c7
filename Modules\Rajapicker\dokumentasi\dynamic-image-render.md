# Dokumentasi: Dynamic Image Render – GambarController

## <PERSON><PERSON><PERSON>
`GambarController` memungkinkan Anda merender file gambar dari database Media Library secara dinamis melalui satu endpoint, tanpa mengungkap struktur folder fisik.

Fitur utama:
* Mendukung pencarian berdasarkan **id**, **uuid**, atau **nama file**.
* Mendukung query `debug=1` untuk menampilkan JSON debug.
* Mengirim header cache 7 hari agar efisien (browser & CDN).
* Fallback otomatis ke `/public/noimage.jpg` jika gambar tidak ditemukan.

## Endpoint
```
GET /gambar
```

### Query Parameter
| Parameter | Wajib | Deskripsi |
|-----------|-------|-----------|
| `id`      | Opsional | ID numerik media (kolom `id`). |
| `uuid`    | Opsional | UUID media (kolom `uuid`). |
| `nama`    | Opsional | Nama file lengkap, contoh `foto.png`. |
| `debug`   | Opsional | `1` untuk menampilkan JSON debug alih-alih mengirim gambar. |

» Setidaknya **satu** dari `id`, `uuid`, atau `nama` harus diberikan.

### Contoh Penggunaan
```html
<!-- Berdasarkan ID -->
<img src="/gambar?id=10" alt="Foto produk" />

<!-- Berdasarkan UUID -->
<img src="/gambar?uuid=01HABCDEF1234567XYZ" alt="Foto profil" />

<!-- Berdasarkan nama file -->
<img src="/gambar?nama=produk123.png" alt="Banner" />
```

### Mode Debug
Tambahkan `debug=1` untuk menampilkan proses penyelesaian URL.
```
/gambar?id=10&debug=1
```
Contoh respons:
```json
{
  "request": {"id": "10", "debug": "1"},
  "found_media": true,
  "media_id": 10,
  "file_name": "01JWTHF2R52BGZJF2QKDD3BW8P.png",
  "disk": "public",
  "collection": "default",
  "path": "E:/www/hotel/storage/uploads/default/01JWTHF2R52BGZJF2QKDD3BW8P.png",
  "status": "file_exists",
  "mime": "image/png"
}
```

## Alur Kerja Controller
1. Cari record `Media` berdasarkan parameter yang diberikan.
2. Dapatkan path fisik (`getPath()`) – jika gagal, gunakan `Storage::disk()->path()`.
3. Apabila file ada ⇒ kirim *BinaryFileResponse* dengan header:
   * `Content-Type: image/*`
   * `Cache-Control: public, max-age=604800, immutable`
4. Apabila file hilang di disk ⇒ redirect (302) ke URL publik (`getUrlAttribute()`).
5. Jika record tidak ditemukan ⇒ kirim fallback `/noimage.jpg`.

## Tips & Catatan
* Pastikan symlink `public/storage` dibuat: `php artisan storage:link`.
* Pastikan prefix upload di `config/media-library.php` (default: `uploads`).
* Gambar-gambar yang di-upload melalui RajaPicker otomatis masuk tabel `media`.
* Anda boleh memodifikasi cache header di `GambarController` bila diperlukan.

---
*Terakhir diperbarui: {{date('Y-m-d')}}* 
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdminPanelPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permission for admin panel access
        $permission = Permission::firstOrCreate([
            'name' => 'access_admin_panel',
            'guard_name' => 'web'
        ]);

        $this->command->info("Permission 'access_admin_panel' created/updated successfully.");

        // Get super admin role name from config
        $superAdminRoleName = config('filament-shield.super_admin.name', 'super_admin');
        
        // Create super admin role if it doesn't exist
        $superAdminRole = Role::firstOrCreate([
            'name' => $superAdminRoleName,
            'guard_name' => 'web'
        ]);

        // Give super admin role the admin panel access permission
        if (!$superAdminRole->hasPermissionTo('access_admin_panel')) {
            $superAdminRole->givePermissionTo('access_admin_panel');
            $this->command->info("Permission 'access_admin_panel' assigned to role '{$superAdminRoleName}'.");
        } else {
            $this->command->info("Role '{$superAdminRoleName}' already has 'access_admin_panel' permission.");
        }

        // Create panel_user role if enabled in config
        if (config('filament-shield.panel_user.enabled', true)) {
            $panelUserRoleName = config('filament-shield.panel_user.name', 'panel_user');
            
            $panelUserRole = Role::firstOrCreate([
                'name' => $panelUserRoleName,
                'guard_name' => 'web'
            ]);

            // Give panel_user role the admin panel access permission
            if (!$panelUserRole->hasPermissionTo('access_admin_panel')) {
                $panelUserRole->givePermissionTo('access_admin_panel');
                $this->command->info("Permission 'access_admin_panel' assigned to role '{$panelUserRoleName}'.");
            } else {
                $this->command->info("Role '{$panelUserRoleName}' already has 'access_admin_panel' permission.");
            }
        }

        $this->command->info('Admin panel permissions setup completed successfully!');
    }
}

<?php

namespace App\Filament\Resources;




use App\Filament\Resources\KonfigResource\Pages;

use App\Models\Konfig;
use Filament\Forms;
use Filament\Forms\Components\Grid;

use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;


class KonfigResource extends Resource
{
    protected static ?string $model = Konfig::class;

    // Konfigurasi navigasi
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationLabel = 'Semua Konfigurasi';
    protected static ?string $navigationGroup = 'System';
    protected static ?int $navigationSort = 100;
    protected static ?string $slug = 'system/konfig';

    protected static bool $shouldRegisterNavigation = true;


    public static function form(Form $form): Form
    {

        // $contoh_custom_value = '[
        //                             {   "type": "textinput", "name": "contoh_nama", "tooltip": "contoh tooltips"  },
        //                             { "type": "textinput","name": "contoh_dua"  },
        //                              { "type": "select",   "name": "contoh_select",   "options" : "bca,bni,mandiri"  }
        //                                     ]';

        return $form
            ->schema([
                // Section untuk informasi umum konfigurasi
                Forms\Components\Section::make('Informasi Konfigurasi')
                    ->columns(3)
                    ->schema([

                        Select::make('jenis')
                            ->label('Jenis konfig')
                            ->options([
                                'KASIR' => 'untuk Kasir (KASIR)',
                                'HOTEL' => 'untuk Hotel (HOTEL)',
                                'ADMIN' => 'untuk panel ADMIN',
                                'GLOBAL' => 'untuk semua (GLOBAL)',
                                'DEV' => 'Developer (DEV)',
                            ])
                            ->default('KASIR')
                            ->disabled(!Auth::user()->hasRole('superadmin')),

                        TextInput::make('nama')
                            ->required()
                            ->maxLength(255)
                            ->disabled(!Auth::user()->hasRole('superadmin'))
                            ->label('Nama Konfigurasi')
                            ->placeholder('Masukkan nama konfigurasi')
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn(Set $set, ?string $state) => $set('slug', Str::slug($state))),

                        TextInput::make('slug')
                            ->label('Slug')
                            ->required()
                            ->maxLength(50)
                            ->disabled(!Auth::user()->hasRole('superadmin'))
                            ->placeholder('Masukkan slug konfigurasi')
                            ->unique(Konfig::class, 'slug', ignoreRecord: true),

                        // Forms\Components\TextInput::make('kode')
                        //     ->maxLength(100)
                        //     ->label('Kode Konfigurasi')
                        //     ->placeholder('Masukkan kode konfigurasi'),


                        Select::make('inijson')
                            ->label('Format Data')
                            ->options([
                                0 => '0. Teks Biasa',
                                1 => '1. 1D Tags input', // TagsInput
                                2 => '2. 2D Key-Value', // KeyValue
                                3 => '3. 3D Repeater', // Repeater Kompleks
                                4 => '4. Field builder', // Repeater Kompleks
                                5 => '5. Custom field : isi ',
                                6 => '6. Form builder',
                            ])
                            ->default(0)
                            ->reactive()
                            ->disabled(!Auth::user()->hasRole('superadmin'))
                            ->afterStateUpdated(function ($state, $set) {
                                // Reset isi_json saat format berubah
                                if ($state == 0) {
                                    $set('isi_json', null);
                                }
                            }),

                        Select::make('rajajson_id')
                            ->label('pilih form')
                            ->visible(fn(Get $get) => $get('inijson') == 6)
                            ->options(\Modules\RajaForm\Models\FormGen::all()->pluck('key', 'id')),
                    ]),

                Section::make('PENGATURAN')
                    ->columns(3)
                    ->columnSpanFull()
                    ->schema([


                        Forms\Components\Textarea::make('isi')
                            ->label('masukkan teks')
                            ->placeholder('Masukkan nilai konfigurasi')
                            ->rows(5)
                            ->columnSpan(2)
                            ->hidden(fn($get, $record) => $get('inijson') != 0),






                        // ----- OPSI 1: JSON Array Sederhana dengan TagsInput -----
                        TagsInput::make('jcol.isi')
                            ->label('masukan pilihan')
                            ->placeholder('Masukkan nilai dan tekan Enter')
                            ->helperText('Masukkan nilai satu per satu, tekan Enter setelah setiap nilai.')
                            // ->separator(',')  
                            ->hidden(fn($get) => $get('inijson') != 1),



                        // OPSI 6
                        Grid::make(3)
                            ->columnSpan(3)
                            ->schema(function (Get $get) {
                                $rajajsonId = $get('rajajson_id');

                                if (!$rajajsonId) {
                                    return [
                                        \Filament\Forms\Components\Placeholder::make('no_form_selected')
                                            ->content('Pilih Rajajson terlebih dahulu untuk menampilkan form builder')
                                            ->extraAttributes(['class' => 'text-amber-600 italic'])
                                    ];
                                }

                                return \Modules\RajaForm\Filament\Forms\Components\FormBuilder::make($rajajsonId);
                            })
                            ->hidden(fn(Get $get) => $get('inijson') != 6),

                        // TAMPILAN PREVIEW JSON
                        // Grid::make(1)
                        // ->columnSpan(3)
                        // ->schema([
                        //     \App\Filament\Forms\Components\KonfigPreview::make(),
                        // ])
                        // ->visible(Auth::user()->hasRole('superadmin'))
                        // ->hidden(fn(Get $get) => in_array($get('inijson'), [0, 5])),


                        // ----- OPSI 1: JSON Array Sederhana dengan TagsInput -----

                        // Grid::make(1)
                        //     ->columnSpan(2)
                        //     ->schema([
                        //         // Editor JSON array sederhana menggunakan TagsInput
                        //         Forms\Components\TagsInput::make('json_tags')
                        //             ->label('Daftar Item')
                        //             ->placeholder('Masukkan nilai dan tekan Enter')
                        //             ->helperText('Masukkan nilai satu per satu, tekan Enter setelah setiap nilai.')
                        //             ->separator(',') // Gunakan koma sebagai pemisah
                        //             ->afterStateHydrated(function ($state, $record, callable $set) {
                        //                 // Konversi dari JSON array ke format tags
                        //                 if ($record && $record->isi_json && $record->inijson == 1) {
                        //                     $jsonData = json_decode($record->isi_json, true);
                        //                     if (is_array($jsonData)) {
                        //                         // Jika array indeks, ambil nilai langsung
                        //                         $tagValues = array_values($jsonData);
                        //                         $set('json_tags', $tagValues);
                        //                     }
                        //                 }
                        //             })
                        //             ->live()
                        //             ->afterStateUpdated(function ($state, callable $set) {
                        //                 // Konversi dari format tags ke JSON array
                        //                 if (is_array($state)) {
                        //                     $jsonArray = array_values($state); // Pastikan ini array indeks
                        //                     $set('isi_json', json_encode($jsonArray));
                        //                 }
                        //             }),
                        //     ])
                        //     ->hidden(fn($get) => $get('inijson') != 1),

                        // ----- OPSI 2: JSON Key-Value Sederhana dengan KeyValue -----

                        // Grid::make(1)
                        //     ->columnSpan(2)
                        //     ->schema([
                        //         // Editor JSON Key-Value sederhana
                        //         Forms\Components\KeyValue::make('json_keyvalue')
                        //             ->label('Pasangan Kunci-Nilai')
                        //             ->addActionLabel('Tambah Item Baru')
                        //             ->reorderable()
                        //             ->keyLabel('Kunci')
                        //             ->valueLabel('Nilai')
                        //             ->keyPlaceholder('Masukkan kunci...')
                        //             ->valuePlaceholder('Masukkan nilai...')
                        //             ->afterStateHydrated(function ($state, $record, callable $set) {
                        //                 // Konversi dari JSON object ke format key-value
                        //                 if ($record && $record->isi_json && $record->inijson == 2) {
                        //                     $jsonData = json_decode($record->isi_json, true);
                        //                     if (is_array($jsonData)) {
                        //                         $set('json_keyvalue', $jsonData);
                        //                     }
                        //                 }
                        //             })

                        //             ->afterStateUpdated(function ($state, callable $set) {
                        //                 // Konversi dari format key-value ke JSON object
                        //                 if (is_array($state)) {
                        //                     $set('isi_json', json_encode($state));
                        //                 }
                        //             }),
                        //     ])
                        //     ->hidden(fn($get) => $get('inijson') != 2),

                        // ----- OPSI 3: JSON Array of Objects dengan Repeater Kompleks -----
                        // Grid::make(3)
                        //     ->columnSpan(2)
                        //     ->schema([
                        //         // Editor JSON Array of Objects kompleks dengan Repeater yang berisi KeyValue
                        //         \App\Filament\Forms\Components\KonfigOpsi3::make(),
                        //     ])
                        //     ->hidden(fn($get) => $get('inijson') != 3),


                        // OPSI 4
                        // Grid::make(1)
                        //     ->columnSpan(3)
                        //     ->schema([
                        //         \App\Filament\Forms\Components\KonfigOpsi4::make(),
                        //     ])
                        //     ->hidden(fn(Get $get) => $get('inijson') != 4),

                        // ----- OPSI 5: Custom field builder dari Konfig.custom_value -----
                        // Grid::make(1)
                        //     ->columnSpan(3)
                        //     ->schema([
                        //         \App\Filament\Forms\Components\KonfigOpsi5::make(),
                        //     ])
                        //     ->hidden(fn(Get $get) => $get('inijson') != 5),







                        // Field tersembunyi untuk menyimpan JSON dalam format yang sesuai
                        // Forms\Components\Hidden::make('isi_json'),
                    ]),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            ->columns([
                // Kolom untuk menampilkan ID konfigurasi
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                TextColumn::make('jenis')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'KASIR' => 'success',
                        'HOTEL' => 'info',
                        'DEV' => 'warning',
                        'GLOBAL' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                // Kolom untuk menampilkan nama konfigurasi
                TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                    TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->sortable(),


                // Kolom untuk menampilkan kode konfigurasi
                // Tables\Columns\TextColumn::make('kode')
                //     ->label('Kode')
                //     ->searchable()
                //     ->sortable(),

                // Kolom untuk menampilkan format data dengan label yang sesuai
                Tables\Columns\TextColumn::make('inijson')
                    ->label('Format Data')
                    ->badge()
                    ->formatStateUsing(fn($state) => match ($state) {
                        0 => '0. Teks Biasa',
                        1 => '1. 1D (Array)',
                        2 => '2. 2D (Key-Value)',
                        3 => '3. 3D (Objects)',
                        4 => '4. Field builder',
                        5 => '5. custom isi + isi_json',
                        6 => '6. Form builder',
                        default => 'Lainnya',
                    })
                    ->color(fn($state) => match ($state) {
                        0 => 'gray',
                        1 => 'info',
                        2 => 'success',
                        3 => 'warning',
                        4 => 'info',
                        5 => 'rose',
                        6 => 'info',
                        default => 'gray',
                    })
                    ->sortable(),

                TextColumn::make('isi')
                    ->tooltip(function ($record) {
                        // Untuk format Teks Biasa (inijson = 0), tampilkan tooltip dari kolom 'isi'
                        if ($record->inijson == 0) {
                            return $record->isi;
                        }

                        // Untuk format JSON (inijson > 0), tampilkan tooltip dari isi_json
                        if (empty($record->isi_json)) {
                            return null;
                        }

                        $jsonData = json_decode($record->isi_json, true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            return $record->isi_json;
                        }

                        return json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    }),


            ])
            ->filters([
                // Filter untuk memfilter berdasarkan jenis aplikasi
                Tables\Filters\SelectFilter::make('jenis')
                    ->options([
                        'KASIR' => 'Kasir',
                        'HOTEL' => 'Hotel',
                        'DEV' => 'Development',
                    ])
                    ->label('Jenis Aplikasi'),

                // Filter untuk memfilter berdasarkan format nilai
                Tables\Filters\SelectFilter::make('inijson')
                    ->options([
                        0 => 'Teks Biasa',
                        1 => '1D (Array)',
                        2 => '2D (Key-Value)',
                        3 => '3D (Objects)',
                    ])
                    ->label('Format Data'),
            ])
            ->actions([
                // Aksi untuk melihat detail konfigurasi
                Tables\Actions\ViewAction::make()
                    ->label('Lihat'),
                // Aksi untuk mengedit konfigurasi
                Tables\Actions\EditAction::make()
                    ->label('Edit'),
                // Aksi untuk menghapus konfigurasi
                // Tables\Actions\DeleteAction::make()
                //     ->label('Hapus'),
            ])
            ->bulkActions([
                // Grup aksi massal
                Tables\Actions\BulkActionGroup::make([
                    // Aksi untuk menghapus beberapa konfigurasi sekaligus
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih'),
                ]),
            ])
            ->emptyStateActions([
                // Aksi yang ditampilkan saat tabel kosong
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Konfigurasi'),
            ]);
    }

    // Definisi relasi (kosong untuk saat ini)
    public static function getRelations(): array
    {
        return [
            //
        ];
    }



    // Definisi halaman untuk resource ini
    public static function getPages(): array
    {
        return [
            'index' => KonfigResource\Pages\ListKonfigs::route('/'),
            'create' => KonfigResource\Pages\CreateKonfig::route('/create'),
            'view' => KonfigResource\Pages\ViewKonfig::route('/{record}'),
            'edit' => KonfigResource\Pages\EditKonfig::route('/{record}/edit'),
        ];
    }


    protected function cekAkses($permission)
    {
        return fn() => Auth::check() && (Auth::user()->hasRole('superadmin') || Auth::user()->can($permission));
    }
}

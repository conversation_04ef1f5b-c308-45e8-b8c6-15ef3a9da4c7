<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel;
use App\Aplikasi\Hotel\Models\Tamu;

use App\Aplikasi\Hotel\Resources\TamuResource\Pages;
use App\Aplikasi\Hotel\Resources\TamuResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class TamuResource extends Resource
{
    protected static ?string $navigationLabel = 'tamu';
 
    protected static ?string $slug = 'tamu';
    protected static ?string $title = 'tamu';
    protected static ?string $navigationGroup = 'Reservasi';
  
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $model = Tamu::class;


    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([


                Forms\Components\TextInput::make('nama')->required(),
                Forms\Components\TextInput::make('email'),
                Forms\Components\TextInput::make('telpon'),
                Forms\Components\TextInput::make('jenis_identitas')->required(),
                FileUpload::make('foto_identitas')->directory('tamu'),
                Forms\Components\TextInput::make('no_identitas')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('nama')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('telpon')->sortable()->searchable(),
                ImageColumn::make('foto_identitas'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ])->visible(Auth::user()->can('adminhotel.tamu.semua')),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTamus::route('/'),
            'create' => Pages\CreateTamu::route('/create'),
            'view' => Pages\ViewTamu::route('/{record}'),
            'edit' => Pages\EditTamu::route('/{record}/edit'),
        ];
    }
}

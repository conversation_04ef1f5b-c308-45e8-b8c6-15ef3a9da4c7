<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KategoriResource\Pages;
use App\Filament\Resources\KategoriResource\RelationManagers;
use App\Models\Kategori;
use App\Models\Konfig;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class KategoriResource extends Resource
{
    protected static ?string $model = Kategori::class;
    protected static ?string $navigationGroup = 'Pengaturan';
    // protected static ?string $navigationParentItem = 'Data Management'; // Parent item
    protected static ?string $navigationLabel = 'Semua Kategori';
    protected static ?string $pluralModelLabel = 'Seluruh kategori';
    protected static bool $shouldRegisterNavigation = true; // Aktifkan navigation
    protected static ?string $slug = 'pengaturan/kategori';
    protected static ?string $modelLabel = 'Kategori';
    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('jenis')
                    ->visible(fn() => filament()->getCurrentPanel()->getId() === 'admin')
                    ->options(Konfig::jcolCollection('jenis-kategori', 'isi')->pluck('value', 'value')),
                TextInput::make('nama')
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn(Set $set, ?string $state) => $set('slug', Str::slug($state))),

                TextInput::make('slug'),
                // Forms\Components\TextInput::make('sub')->required(),
                FileUpload::make('gambar')
                    ->image()
                    ->imageResizeMode('cover')
                    ->imageCropAspectRatio('16:9')
                    ->imageResizeTargetWidth('1920')
                    ->imageResizeTargetHeight('1080')
                    ->directory('kategori'),
                Textarea::make('ket')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                Tables\Columns\TextColumn::make('jenis')->sortable()->searchable()->visible(fn() => filament()->getCurrentPanel()->getId() === 'admin'),
                Tables\Columns\TextColumn::make('nama')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('slug')->sortable()->searchable(),
                // Tables\Columns\TextColumn::make('sub')->sortable()->searchable(),
                ImageColumn::make('gambar')
                    ->width(200),
                // Tables\Columns\TextColumn::make('ket')->sortable()->searchable()
            ])


            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageKategoris::route('/'),
        ];
    }
}

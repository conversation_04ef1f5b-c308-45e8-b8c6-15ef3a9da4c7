<x-filament::page>
  <div wire:poll.10s x-data="{
      init() {
          // Event listener untuk refresh halaman
          Livewire.on('refreshPage', () => {
              // Paksa render ulang komponen Livewire
              $wire.$refresh();
          });
      }
  }">
    <!-- Navigation Tabs -->
    <div class="mb-6 border-b border-gray-200">
      <nav aria-label="Tabs" class="flex space-x-4">
        <button
          class="{{ $activeTab === 'tables' ? 'border-primary-500 text-primary-600 border-b-2' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium"
          wire:click="$set('activeTab', 'tables')">

          Table
        </button>
        <button
          class="{{ $activeTab === 'seeders' ? 'border-primary-500 text-primary-600 border-b-2' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium"
          wire:click="$set('activeTab', 'seeders')">

          DB Seed
        </button>
        <button
          class="{{ $activeTab === 'reset' ? 'border-primary-500 text-primary-600 border-b-2' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium"
          wire:click="$set('activeTab', 'reset')">

          Reset
        </button>
      </nav>
    </div>

    <!-- Tables Tab Content -->
    @if ($activeTab === 'tables')
      <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th class="px-6 py-3" scope="col">Nama Tabel</th>
              <th class="px-6 py-3" scope="col">Jumlah Data</th>
              <th class="px-6 py-3" scope="col">Aksi</th>
            </tr>
          </thead>
          <tbody>
            @forelse($this->getTables() as $table)
              <tr class="border-b bg-white">
                <td class="px-6 py-4">{{ $table['nama'] }}</td>
                <td class="px-6 py-4">{{ $table['jumlah'] }}</td>
                <td class="px-6 py-4">
                  <x-filament::button color="danger" size="sm"
                    wire:click="kosongkanTabel('{{ $table['nama'] }}')"
                    wire:confirm="Apakah Anda yakin ingin mengosongkan tabel {{ $table['nama'] }}? Semua data akan hilang dan tidak dapat dikembalikan."
                    wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                    wire:target="kosongkanTabel('{{ $table['nama'] }}')">
                    <span wire:loading.remove wire:target="kosongkanTabel('{{ $table['nama'] }}')">
                      Kosongkan
                    </span>
                    <span wire:loading wire:target="kosongkanTabel('{{ $table['nama'] }}')">
                      <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <circle class="opacity-25" cx="12" cy="12" r="10"
                          stroke-width="4" stroke="currentColor"></circle>
                        <path class="opacity-75"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          fill="currentColor">
                        </path>
                      </svg>
                      Proses...
                    </span>
                  </x-filament::button>
                </td>
              </tr>
            @empty
              <tr class="border-b bg-white">
                <td class="px-6 py-4 text-center" colspan="3">Tidak ada data tabel</td>
              </tr>
            @endforelse
          </tbody>
        </table>
      </div>

      <!-- Terminal Output -->
      <div class="mt-8">
        <div class="mb-2 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Terminal Output</h3>
          <div>
            <x-filament::button color="{{ $showConsole ? 'danger' : 'success' }}" size="sm"
              wire:click="toggleConsole">
              {{ $showConsole ? 'Sembunyikan Terminal' : 'Tampilkan Terminal' }}
            </x-filament::button>
            <x-filament::button color="warning" size="sm" wire:click="clearConsoleLog">
              Bersihkan Log
            </x-filament::button>
          </div>
        </div>

        @if ($showConsole)
          <div class="overflow-hidden rounded-md bg-gray-900 text-white" wire:poll.1s
            x-data="{
                init() {
                        this.$nextTick(() => {
                            const terminal = this.$refs.terminalOutput;
                            terminal.scrollTop = terminal.scrollHeight;
                        });
                    },
                    updateScroll() {
                        const terminal = this.$refs.terminalOutput;
                        terminal.scrollTop = terminal.scrollHeight;
                    }
            }" x-init="init()"
            x-on:console-updated.window="updateScroll()">
            <div class="flex items-center justify-between border-b border-gray-700 bg-gray-800 p-2">
              <div class="flex space-x-2">
                <div class="h-3 w-3 rounded-full bg-red-500"></div>
                <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                <div class="h-3 w-3 rounded-full bg-green-500"></div>
              </div>
              <div class="text-xs text-gray-400">Terminal Seeder</div>
              <div></div>
            </div>
            <div class="h-64 overflow-y-auto p-4 font-mono text-sm" x-ref="terminalOutput">
              @forelse($consoleLog as $log)
                <div
                  class="@if ($log['type'] === 'error') text-red-400 @elseif($log['type'] === 'success') text-green-400 @elseif($log['type'] === 'warning') text-yellow-400 @else text-gray-300 @endif mb-1">
                  <span class="text-gray-500">[{{ $log['timestamp'] }}]</span>
                  {{ $log['message'] }}
                </div>
              @empty
                <div class="text-gray-500">Terminal siap menerima perintah...</div>
              @endforelse
            </div>
          </div>
        @endif
      </div>
    @endif

    <!-- Seeders Tab Content -->
    @if ($activeTab === 'seeders')
      <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th class="px-6 py-3" scope="col">Nama Seeder</th>
              <th class="px-6 py-3" scope="col">Konfigurasi Data</th>
              <th class="px-6 py-3" scope="col">Aksi</th>
            </tr>
          </thead>
          <tbody>
            @forelse($this->getSeeders() as $seeder)
              <tr class="border-b bg-white hover:bg-gray-50">
                <td class="px-6 py-4">{{ $seeder['nama'] }}</td>
                <td class="px-6 py-4">
                  <div class="flex flex-wrap items-center gap-2">
                    <!-- Input Jumlah -->
                    <div class="flex items-center">
                      <span class="mr-1 text-xs text-gray-500">Jumlah:</span>
                      <input
                        class="focus:border-primary-500 focus:ring-primary-500 w-16 rounded-md border-gray-300 shadow-sm"
                        min="0" type="number"
                        wire:model="seederJumlah.{{ $seeder['nama'] }}">
                    </div>

                    <!-- Selector Bulan (Baru) -->
                    <div class="flex items-center">
                      <span class="mr-1 text-xs text-gray-500">Bulan:</span>
                      <select
                        class="focus:border-primary-500 focus:ring-primary-500 w-40 rounded-md border-gray-300 shadow-sm"
                        wire:change="updateBulan($event.target.value)" wire:model="selectedBulan">
                        @foreach ($daftarBulan as $value => $label)
                          <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                      </select>
                    </div>

                    <!-- Selector Tahun (Baru) -->
                    <div class="flex items-center">
                      <span class="mr-1 text-xs text-gray-500">Tahun:</span>
                      <select
                        class="focus:border-primary-500 focus:ring-primary-500 w-40 rounded-md border-gray-300 shadow-sm"
                        wire:change="updateTahun($event.target.value)" wire:model="selectedTahun">
                        @foreach ($daftarTahun as $tahun => $label)
                          <option value="{{ $tahun }}">{{ $label }}</option>
                        @endforeach
                      </select>
                    </div>
                  </div>
                </td>
                <td class="flex space-x-2 px-6 py-4">
                  <!-- Tombol aksi tetap sama -->
                  <x-filament::button color="success" size="sm"
                    wire:click="runSeeder('{{ $seeder['nama'] }}')" wire:loading.attr="disabled"
                    wire:loading.class="opacity-70 cursor-wait"
                    wire:target="runSeeder('{{ $seeder['nama'] }}')">
                    <span wire:loading.remove wire:target="runSeeder('{{ $seeder['nama'] }}')">
                      JALANKAN!
                    </span>
                    <span wire:loading wire:target="runSeeder('{{ $seeder['nama'] }}')">
                      Tunggu bentar bang.....
                    </span>
                  </x-filament::button>
                  <x-filament::button color="danger" size="sm"
                    wire:click="hapusData('{{ $seeder['nama'] }}')" wire:loading.attr="disabled"
                    wire:loading.class="opacity-70 cursor-wait"
                    wire:target="hapusData('{{ $seeder['nama'] }}')">
                    <span wire:loading.remove wire:target="hapusData('{{ $seeder['nama'] }}')">
                      Hapus
                    </span>
                    <span wire:loading wire:target="hapusData('{{ $seeder['nama'] }}')">
                      <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <circle class="opacity-25" cx="12" cy="12" r="10"
                          stroke-width="4" stroke="currentColor"></circle>
                        <path class="opacity-75"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          fill="currentColor">
                        </path>
                      </svg>
                      Proses...
                    </span>
                  </x-filament::button>
                </td>
              </tr>
            @empty
              <tr class="border-b bg-white">
                <td class="px-6 py-4 text-center" colspan="3">Tidak ada seeder yang tersedia
                </td>
              </tr>
            @endforelse
          </tbody>
        </table>
      </div>
      <!-- Terminal Output -->
      <div class="mt-8">
        <div class="mb-2 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Terminal Output</h3>
          <div>
            <x-filament::button color="{{ $showConsole ? 'danger' : 'success' }}" size="sm"
              wire:click="toggleConsole">
              {{ $showConsole ? 'Sembunyikan Terminal' : 'Tampilkan Terminal' }}
            </x-filament::button>
            <x-filament::button color="warning" size="sm" wire:click="clearConsoleLog">
              Bersihkan Log
            </x-filament::button>
          </div>
        </div>

        <div class="overflow-hidden rounded-md bg-gray-900 text-white" wire:poll.1s
          x-data="{
              init() {
                      this.$nextTick(() => {
                          const terminal = this.$refs.terminalOutput;
                          terminal.scrollTop = terminal.scrollHeight;
                      });
                  },
                  updateScroll() {
                      const terminal = this.$refs.terminalOutput;
                      terminal.scrollTop = terminal.scrollHeight;
                  }
          }" x-init="init()"
          x-on:console-updated.window="updateScroll()">
          <!-- Header Terminal -->
          <div class="flex items-center justify-between border-b border-gray-700 bg-gray-800 p-2">
            <div class="flex space-x-2">
              <div class="h-3 w-3 rounded-full bg-red-500"></div>
              <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
              <div class="h-3 w-3 rounded-full bg-green-500"></div>
            </div>
            <div class="text-xs text-gray-400">Terminal Seeder</div>
            <div></div>
          </div>

          <!-- Isi Terminal -->
          <!-- Perbaikan CSS terminal output -->
          <div class="h-64 overflow-y-auto bg-gray-900 p-4 font-mono text-sm text-white"
            x-ref="terminalOutput">
            @forelse($consoleLog as $log)
              <div
                class="@if ($log['type'] === 'error') text-red-400 @elseif($log['type'] === 'success') text-green-400 @elseif($log['type'] === 'warning') text-yellow-400 @else text-white @endif mb-1">
                <span class="text-gray-500">[{{ $log['timestamp'] }}]</span>
                {{ $log['message'] }}
              </div>
            @empty
              <div class="text-white">Terminal siap menerima perintah...</div>
            @endforelse
          </div>
        </div>

      </div>
    @endif

    <!-- Reset Tab Content -->
    @if ($activeTab === 'reset')
      <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th class="px-6 py-3" scope="col">Jenis Reset</th>
              <th class="px-6 py-3" scope="col">Deskripsi</th>
              <th class="px-6 py-3" scope="col">Aksi</th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b bg-white hover:bg-gray-50">
              <td class="px-6 py-4 font-medium">Reset Data Reservasi <span
                  class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{{ $resetDataCounts['reservasi'] ?? 0 }}
                  data</span></td>
              <td class="px-6 py-4">Menghapus semua data reservasi dan transaksi terkait</td>
              <td class="px-6 py-4">
                <x-filament::button color="danger" size="sm" wire:click="resetReservasi"
                  wire:confirm="Apakah Anda yakin ingin menghapus semua data reservasi? Semua data akan hilang dan tidak dapat dikembalikan."
                  wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                  wire:target="resetReservasi">
                  <span wire:loading.remove wire:target="resetReservasi">
                    Reset Reservasi
                  </span>
                  <span wire:loading wire:target="resetReservasi">
                    <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke-width="4" stroke="currentColor"></circle>
                      <path class="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor">
                      </path>
                    </svg>
                    Proses...
                  </span>
                </x-filament::button>
              </td>
            </tr>
            <tr class="border-b bg-white hover:bg-gray-50">
              <td class="px-6 py-4 font-medium">Reset Data Kamar & Tipe <span
                  class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{{ $resetDataCounts['kamar'] ?? 0 }}
                  data</span></td>
              <td class="px-6 py-4">Menghapus semua data kamar dan tipe kamar</td>
              <td class="px-6 py-4">
                <x-filament::button color="danger" size="sm" wire:click="resetKamar"
                  wire:confirm="Apakah Anda yakin ingin menghapus semua data kamar dan tipe kamar? Semua data akan hilang dan tidak dapat dikembalikan."
                  wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                  wire:target="resetKamar">
                  <span wire:loading.remove wire:target="resetKamar">
                    Reset Kamar
                  </span>
                  <span wire:loading wire:target="resetKamar">
                    <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke-width="4" stroke="currentColor"></circle>
                      <path class="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor">
                      </path>
                    </svg>
                    Proses...
                  </span>
                </x-filament::button>
              </td>
            </tr>
            <tr class="border-b bg-white hover:bg-gray-50">
              <td class="px-6 py-4 font-medium">Reset Tamu Hotel <span
                  class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{{ $resetDataCounts['tamu'] ?? 0 }}
                  data</span></td>
              <td class="px-6 py-4">Menghapus semua data tamu hotel</td>
              <td class="px-6 py-4">
                <x-filament::button color="danger" size="sm" wire:click="resetTamu"
                  wire:confirm="Apakah Anda yakin ingin menghapus semua data tamu hotel? Semua data akan hilang dan tidak dapat dikembalikan."
                  wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                  wire:target="resetTamu">
                  <span wire:loading.remove wire:target="resetTamu">
                    Reset Tamu
                  </span>
                  <span wire:loading wire:target="resetTamu">
                    <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke-width="4" stroke="currentColor"></circle>
                      <path class="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor">
                      </path>
                    </svg>
                    Proses...
                  </span>
                </x-filament::button>
              </td>
            </tr>
            <tr class="border-b bg-white hover:bg-gray-50">
              <td class="px-6 py-4 font-medium">Reset Konfigurasi <span
                  class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{{ $resetDataCounts['konfigurasi'] ?? 0 }}
                  data</span></td>
              <td class="px-6 py-4">Mengembalikan konfigurasi ke pengaturan default</td>
              <td class="px-6 py-4">
                <x-filament::button color="danger" size="sm" wire:click="resetKonfigurasi"
                  wire:confirm="Apakah Anda yakin ingin mengembalikan konfigurasi ke pengaturan default? Semua pengaturan kustom akan hilang."
                  wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                  wire:target="resetKonfigurasi">
                  <span wire:loading.remove wire:target="resetKonfigurasi">
                    Reset Konfigurasi
                  </span>
                  <span wire:loading wire:target="resetKonfigurasi">
                    <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke-width="4" stroke="currentColor"></circle>
                      <path class="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor">
                      </path>
                    </svg>
                    Proses...
                  </span>
                </x-filament::button>
              </td>
            </tr>
            <tr class="border-b bg-white hover:bg-gray-50">
              <td class="px-6 py-4 font-medium">Reset Seluruhnya <span
                  class="ml-2 inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">{{ $resetDataCounts['total'] ?? 0 }}
                  data</span></td>
              <td class="px-6 py-4">Menghapus semua data dan mengembalikan aplikasi ke kondisi awal
              </td>
              <td class="px-6 py-4">
                <x-filament::button color="danger" size="sm" wire:click="resetSeluruhnya"
                  wire:confirm="PERINGATAN: Apakah Anda benar-benar yakin ingin menghapus SEMUA DATA? Tindakan ini akan mengembalikan aplikasi ke kondisi awal dan TIDAK DAPAT DIBATALKAN!"
                  wire:loading.attr="disabled" wire:loading.class="opacity-70 cursor-wait"
                  wire:target="resetSeluruhnya">
                  <span wire:loading.remove wire:target="resetSeluruhnya">
                    Reset Seluruhnya
                  </span>
                  <span wire:loading wire:target="resetSeluruhnya">
                    <svg class="-ml-1 mr-1 inline h-4 w-4 animate-spin" fill="none"
                      viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke-width="4" stroke="currentColor"></circle>
                      <path class="opacity-75"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        fill="currentColor">
                      </path>
                    </svg>
                    Proses...
                  </span>
                </x-filament::button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Terminal Output untuk Tab Reset -->
      <div class="mt-8">

        <div class="overflow-hidden rounded-md bg-gray-900 text-white" wire:poll.1s
          x-data="{
              init() {
                      this.$nextTick(() => {
                          const terminal = this.$refs.terminalOutput;
                          terminal.scrollTop = terminal.scrollHeight;
                      });
                  },
                  updateScroll() {
                      const terminal = this.$refs.terminalOutput;
                      terminal.scrollTop = terminal.scrollHeight;
                  }
          }" x-init="init()"
          x-on:console-updated.window="updateScroll()">
          <div class="flex items-center justify-between border-b border-gray-700 bg-gray-800 p-2">
            <div class="flex space-x-2">
              <div class="h-3 w-3 rounded-full bg-red-500"></div>
              <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
              <div class="h-3 w-3 rounded-full bg-green-500"></div>
            </div>
            <div class="text-xs text-gray-400">Terminal Reset</div>
            <div></div>
          </div>
          <div class="h-64 overflow-y-auto p-4 font-mono text-sm" x-ref="terminalOutput">
            @forelse($consoleLog as $log)
              <div
                class="@if ($log['type'] === 'error') text-red-400 @elseif($log['type'] === 'success') text-green-400 @elseif($log['type'] === 'warning') text-yellow-400 @else text-gray-300 @endif mb-1">
                <span class="text-gray-500">[{{ $log['timestamp'] }}]</span>
                {{ $log['message'] }}
              </div>
            @empty
              <div class="text-gray-500">Terminal siap menerima perintah...</div>
            @endforelse
          </div>
        </div>

      </div>
    @endif
  </div>
</x-filament::page>

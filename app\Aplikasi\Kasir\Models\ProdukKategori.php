<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\Kategori;
use Illuminate\Database\Eloquent\Builder;

/**
 * Model ProdukKategori yang menggunakan tabel categories
 * Mempertahankan fungsionalitas yang sama dengan model lama
 *
 * @mixin IdeHelperProdukKategori
 */
class ProdukKategori extends Kategori
{
    protected $fillable = [
        'nama',
        'sub',
        'harga_modal',
        'foto',
        'ket',
        'jenis',
        'toko_id',
        'created_at',
        'updated_at',
    ];

    

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (Builder $builder) {
            $builder->where('jenis', 'PRODUK');
        });

        static::creating(function ($model) { $model->jenis = 'PRODUK'; });
        static::updating(function ($model) { $model->jenis = 'PRODUK'; });
    }

    // Menggunakan metode dari parent class (Category)
    // Tidak perlu mendefinisikan ulang metode-metode yang sudah ada di parent class
}

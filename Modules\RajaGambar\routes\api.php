<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\RajaGambar\Http\Controllers\RajaGambarController;
use <PERSON><PERSON><PERSON>\RajaGambar\Http\Controllers\RajaGambarAdvancedController;
use Mo<PERSON><PERSON>\RajaGambar\Http\Controllers\RajaGambarEffectsController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::prefix('rajagambar')->middleware(['api'])->group(function () {

    // Basic image operations
    Route::post('/resize', [RajaGambarController::class, 'resize'])->name('rajagambar.resize');
    Route::post('/width', [RajaGambarController::class, 'width'])->name('rajagambar.width');
    Route::post('/height', [RajaGambarController::class, 'height'])->name('rajagambar.height');
    Route::post('/fit', [RajaGambarController::class, 'fit'])->name('rajagambar.fit');
    Route::post('/crop', [RajaGambarController::class, 'crop'])->name('rajagambar.crop');

    // Advanced operations
    Route::post('/focal-crop', [RajaGambarAdvancedController::class, 'focalCrop'])->name('rajagambar.focal-crop');
    Route::post('/manual-crop', [RajaGambarAdvancedController::class, 'manualCrop'])->name('rajagambar.manual-crop');
    Route::post('/brightness', [RajaGambarAdvancedController::class, 'brightness'])->name('rajagambar.brightness');
    Route::post('/contrast', [RajaGambarAdvancedController::class, 'contrast'])->name('rajagambar.contrast');
    Route::post('/gamma', [RajaGambarAdvancedController::class, 'gamma'])->name('rajagambar.gamma');
    Route::post('/colorize', [RajaGambarAdvancedController::class, 'colorize'])->name('rajagambar.colorize');

    // Effects and transformations
    Route::post('/background', [RajaGambarEffectsController::class, 'background'])->name('rajagambar.background');
    Route::post('/border', [RajaGambarEffectsController::class, 'border'])->name('rajagambar.border');
    Route::post('/orientation', [RajaGambarEffectsController::class, 'orientation'])->name('rajagambar.orientation');
    Route::post('/flip', [RajaGambarEffectsController::class, 'flip'])->name('rajagambar.flip');
    Route::post('/watermark', [RajaGambarEffectsController::class, 'watermark'])->name('rajagambar.watermark');
    Route::post('/text', [RajaGambarEffectsController::class, 'text'])->name('rajagambar.text');
    Route::post('/optimize', [RajaGambarEffectsController::class, 'optimize'])->name('rajagambar.optimize');

    // Upload and process endpoint for RajaUpload component
    Route::post('/upload-process', [RajaGambarEffectsController::class, 'uploadProcess'])->name('rajagambar.upload-process');

});

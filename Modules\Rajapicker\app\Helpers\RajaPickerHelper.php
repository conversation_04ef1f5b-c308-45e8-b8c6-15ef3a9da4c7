<?php 

if (!function_exists('render_gambar')) {
    function render_gambar($gambar){
    
    
      if(!$gambar) {
          return null;
      }
      
      // Menentukan jenis gambar dan kembalikan URL yang sesuai
      switch (true) {
          // Jika gambar berisi "/" maka ambil nama file saja
          case str_contains($gambar, '/'):
              $gambar = '/' . basename($gambar);
              // Kembalikan URL gambar dinamis
              return url('/gambar?nama=' . ltrim($gambar, '/'));
              
          // Jika gambar berupa integer (ID), gunakan parameter id
          case is_numeric($gambar) && is_int($gambar + 0):
              return url('/gambar?id=' . $gambar);
              
          default:
              return null;
      }
    
    
    }
    }
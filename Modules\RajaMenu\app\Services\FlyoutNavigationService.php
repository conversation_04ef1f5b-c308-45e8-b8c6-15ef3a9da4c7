<?php

namespace Modules\RajaMenu\Services;

class FlyoutNavigationService
{
    /**
     * Auto-discover navigation from panel resources
     */
    public static function discoverNavigationFromPanel(string $panelId = 'admin'): array
    {
        // Check navigation mode from config
        $navigationMode = config('rajamenu.navigation_mode', 'auto');

        if ($navigationMode === 'manual') {
            return self::getManualNavigationItems($panelId);
        }

        try {
            $panel = \Filament\Facades\Filament::getPanel($panelId);
            $navigationItems = [];
            $childItems = [];

            // Get all resources from panel
            $resources = $panel->getResources();
            $pages = $panel->getPages();

            // Process resources
            foreach ($resources as $resource) {
                $item = self::extractNavigationFromResource($resource);
                if ($item) {
                    if ($item['parent_item']) {
                        $childItems[$item['parent_item']][] = $item;
                    } else {
                        $navigationItems[$item['group']][] = $item;
                    }
                }
            }

            // Process pages
            foreach ($pages as $page) {
                $item = self::extractNavigationFromPage($page);
                if ($item) {
                    if ($item['parent_item']) {
                        $childItems[$item['parent_item']][] = $item;
                    } else {
                        $navigationItems[$item['group']][] = $item;
                    }
                }
            }

            // NEW: Process internal pages defined inside each resource
            foreach ($resources as $resource) {
                if (method_exists($resource, 'getPages')) {
                    foreach ($resource::getPages() as $registration) {
                        $pageClass = null;

                        // Filament v3 returns a PageRegistration object from route() helper
                        if (is_object($registration) && method_exists($registration, 'getPage')) {
                            $pageClass = $registration->getPage();
                        }
                        // Sometimes resources may return the class string directly
                        if (!$pageClass && is_string($registration) && class_exists($registration)) {
                            $pageClass = $registration;
                        }

                        if (!$pageClass || !class_exists($pageClass)) {
                            continue; // Skip if we cannot resolve class
                        }

                        $item = self::extractNavigationFromResourcePage($pageClass, $resource);
                        if ($item) {
                            if ($item['parent_item']) {
                                $childItems[$item['parent_item']][] = $item;
                            } else {
                                $navigationItems[$item['group']][] = $item;
                            }
                        }
                    }
                }
            }

            // Only add parent items that don't exist as actual resources/pages
            // Don't create parent items that already exist as standalone items
            self::addMissingParentItems($navigationItems, $childItems);

            // BARU: Filter item navigasi berdasarkan permission pengguna
            self::filterNavigationByPermissions($navigationItems, $childItems);

            return [
                'navigationItems' => $navigationItems,
                'childItems' => $childItems,
            ];
        } catch (\Exception $e) {
            // Fallback to static items if panel not available
            return [
                'navigationItems' => self::getNavigationItems(),
                'childItems' => [],
            ];
        }
    }

    /**
     * Add missing parent items that are referenced but don't exist
     */
    protected static function addMissingParentItems(array &$navigationItems, array $childItems): void
    {
        $parentItemsNeeded = array_keys($childItems);

        foreach ($parentItemsNeeded as $parentLabel) {
            // Skip empty parent labels
            if (empty($parentLabel)) {
                continue;
            }

            // Check if this parent has any valid children
            if (!isset($childItems[$parentLabel]) || empty($childItems[$parentLabel])) {
                continue;
            }

            $found = false;

            // Check if parent exists in any group
            foreach ($navigationItems as $items) {
                foreach ($items as $item) {
                    if ($item['label'] === $parentLabel) {
                        $found = true;
                        break 2;
                    }
                }
            }

            // If parent doesn't exist, create it only if it has valid children
            if (!$found && !empty($childItems[$parentLabel])) {
                $parentGroup = self::guessParentGroup($parentLabel, $childItems);
                $parentIcon = self::guessParentIcon($parentLabel);

                $navigationItems[$parentGroup][] = [
                    'label' => $parentLabel,
                    'icon' => $parentIcon,
                    'url' => '#',
                    'group' => $parentGroup,
                    'parent_item' => null,
                    'sort' => 0,
                    'badge' => null,
                    'type' => 'parent',
                    'class' => null,
                ];
            }
        }
    }

    /**
     * Guess the group for a parent item based on its children
     */
    protected static function guessParentGroup(string $parentLabel, array $childItems): string
    {
        if (!isset($childItems[$parentLabel]) || empty($childItems[$parentLabel])) {
            return 'Default';
        }

        // Get the most common group from children
        $groups = array_column($childItems[$parentLabel], 'group');
        $groupCounts = array_count_values($groups);
        arsort($groupCounts);

        return array_key_first($groupCounts) ?: 'Default';
    }

    /**
     * Guess the icon for a parent item based on its label
     */
    protected static function guessParentIcon(string $parentLabel): string
    {
        $iconMap = [
            'Business' => 'heroicon-o-building-office',
            'Data Management' => 'heroicon-o-circle-stack',
            'System Tools' => 'heroicon-o-wrench-screwdriver',
            'Content' => 'heroicon-o-document-text',
            'Settings' => 'heroicon-o-cog-6-tooth',
            'Users' => 'heroicon-o-users',
            'Reports' => 'heroicon-o-chart-bar',
            'Analytics' => 'heroicon-o-chart-pie',
        ];

        return $iconMap[$parentLabel] ?? 'heroicon-o-document';
    }

    /**
     * Extract navigation info from resource
     */
    protected static function extractNavigationFromResource(string $resource): ?array
    {
        try {
            // Check if resource should register navigation
            if (method_exists($resource, 'shouldRegisterNavigation') && !$resource::shouldRegisterNavigation()) {
                return null;
            }

            // Get navigation properties
            $label = method_exists($resource, 'getNavigationLabel') ? $resource::getNavigationLabel() : null;
            $icon = method_exists($resource, 'getNavigationIcon') ? $resource::getNavigationIcon() : 'heroicon-o-document';
            $group = method_exists($resource, 'getNavigationGroup') ? $resource::getNavigationGroup() : null;
            $parentItem = method_exists($resource, 'getNavigationParentItem') ? $resource::getNavigationParentItem() : null;
            $sort = method_exists($resource, 'getNavigationSort') ? $resource::getNavigationSort() : null;

            // Jika tidak memiliki navigationSort, tempatkan di paling akhir
            if ($sort === null) {
                $sort = 9999;
            }
            $badge = method_exists($resource, 'getNavigationBadge') ? $resource::getNavigationBadge() : null;

            if (!$label) {
                return null;
            }

            // Jika tidak memiliki group dan tidak memiliki parent, jadikan sebagai parent menu
            if (!$group && !$parentItem) {
                $group = $label; // Gunakan label sebagai group name
            } elseif (!$group && $parentItem) {
                $group = 'Default'; // Will be moved to correct group by parent logic
            }

            return [
                'label' => $label,
                'icon' => $icon,
                'url' => $resource::getUrl('index'),
                'group' => $group,
                'parent_item' => $parentItem,
                'sort' => $sort ?? 0,
                'badge' => $badge,
                'type' => 'resource',
                'class' => $resource,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract navigation info from page
     */
    protected static function extractNavigationFromPage(string $page): ?array
    {
        try {
            // Check if page should register navigation
            if (method_exists($page, 'shouldRegisterNavigation') && !$page::shouldRegisterNavigation()) {
                return null;
            }

            // Get navigation properties
            $label = method_exists($page, 'getNavigationLabel') ? $page::getNavigationLabel() : null;
            $icon = method_exists($page, 'getNavigationIcon') ? $page::getNavigationIcon() : 'heroicon-o-document';
            $group = method_exists($page, 'getNavigationGroup') ? $page::getNavigationGroup() : null;
            $parentItem = method_exists($page, 'getNavigationParentItem') ? $page::getNavigationParentItem() : null;
            $sort = method_exists($page, 'getNavigationSort') ? $page::getNavigationSort() : null;

            // Jika tidak memiliki navigationSort, tempatkan di paling akhir
            if ($sort === null) {
                $sort = 9999;
            }

            if (!$label) {
                return null;
            }

            // Jika tidak memiliki group dan tidak memiliki parent, jadikan sebagai menu utama tanpa group
            if (!$group && !$parentItem) {
                $group = 'Main'; // Gunakan group khusus untuk menu utama tanpa group
            } elseif (!$group && $parentItem) {
                $group = 'Default'; // Will be moved to correct group by parent logic
            }

            return [
                'label' => $label,
                'icon' => $icon,
                'url' => $page::getUrl(),
                'group' => $group,
                'parent_item' => $parentItem,
                'sort' => $sort ?? 0,
                'badge' => null,
                'type' => 'page',
                'class' => $page,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract navigation info from resource page
     */
    protected static function extractNavigationFromResourcePage(string $pageClass, string $resource): ?array
    {
        try {
            // Hanya tampilkan jika page MENDEFINISIKAN properti static $shouldRegisterNavigation = true
            $reflection = new \ReflectionClass($pageClass);

            if (!$reflection->hasProperty('shouldRegisterNavigation')) {
                return null; // Properti tidak dideklarasikan di kelas ini
            }

            $prop = $reflection->getProperty('shouldRegisterNavigation');

            // Pastikan properti dideklarasikan DI kelas ini (bukan pewarisan)
            if ($prop->getDeclaringClass()->getName() !== $pageClass) {
                return null;
            }

            if ($prop->getDefaultValue() !== true) {
                return null; // Nilai bukan true
            }

            // Navigation properties (fallback ke resource bila tidak ada)
            $label = method_exists($pageClass, 'getNavigationLabel') ? $pageClass::getNavigationLabel() : null;
            $icon = method_exists($pageClass, 'getNavigationIcon') ? $pageClass::getNavigationIcon() : null;
            $group = method_exists($pageClass, 'getNavigationGroup') ? $pageClass::getNavigationGroup() : null;

            // Fallback values from resource
            if (!$icon && method_exists($resource, 'getNavigationIcon')) {
                $icon = $resource::getNavigationIcon();
            }
            if (!$group && method_exists($resource, 'getNavigationGroup')) {
                $group = $resource::getNavigationGroup();
            }

            $parentItem = method_exists($pageClass, 'getNavigationParentItem') ? $pageClass::getNavigationParentItem() : null;
            $sort = method_exists($pageClass, 'getNavigationSort') ? $pageClass::getNavigationSort() : null;
            if ($sort === null) {
                $sort = 9999;
            }

            if (!$label) {
                // Derive label from class name if still empty
                $label = class_basename($pageClass);
            }

            // Group fallback handling
            if (!$group && !$parentItem) {
                $group = 'Main';
            } elseif (!$group && $parentItem) {
                $group = 'Default';
            }

            // Build navigation item
            return [
                'label' => $label,
                'icon' => $icon ?? 'heroicon-o-document',
                'url' => $pageClass::getUrl(),
                'group' => $group,
                'parent_item' => $parentItem,
                'sort' => $sort,
                'badge' => null,
                'type' => 'page',
                'class' => $pageClass,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get manual navigation items from config for specific panel
     */
    public static function getManualNavigationItems(string $panelId = 'admin'): array
    {
        $manualConfig = config("rajamenu.manual_navigation.{$panelId}.groups", []);
        $navigationItems = [];
        $childItems = [];

        foreach ($manualConfig as $groupName => $groupConfig) {
            $items = $groupConfig['items'] ?? [];

            foreach ($items as $item) {
                // Process children if exists
                if (isset($item['children']) && !empty($item['children'])) {
                    $childItems[$item['label']] = $item['children'];

                    // Add parent item without children array
                    $parentItem = $item;
                    unset($parentItem['children']);
                    $navigationItems[$groupName][] = $parentItem;
                } else {
                    // Regular item without children
                    $navigationItems[$groupName][] = $item;
                }
            }
        }

        return [
            'navigationItems' => $navigationItems,
            'childItems' => $childItems,
        ];
    }

    /**
     * Get navigation items organized by groups (simple array format)
     */
    public static function getNavigationItems(): array
    {
        return [
            'Cms' => [
                [
                    'label' => 'Content',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                ]
            ],
            'System' => [
                [
                    'label' => 'Data Management',
                    'icon' => 'heroicon-o-database',
                    'url' => '#',
                ],
                [
                    'label' => 'System Tools',
                    'icon' => 'heroicon-o-wrench-screwdriver',
                    'url' => '#',
                ]
            ],
            'Pengaturan' => [
                [
                    'label' => 'Business',
                    'icon' => 'heroicon-o-building-office',
                    'url' => '#',
                ]
            ]
        ];
    }

    /**
     * Filter navigation items & child items berdasarkan permission pengguna
     */
    protected static function filterNavigationByPermissions(array &$navigationItems, array &$childItems): void
    {
        // 1. Filter child items terlebih dahulu
        foreach ($childItems as $parentLabel => $items) {
            foreach ($items as $index => $childItem) {
                if (! self::userCanAccessItem($childItem)) {
                    unset($childItems[$parentLabel][$index]);
                }
            }

            // Susun ulang index array dan hapus parent yang tidak memiliki child
            $childItems[$parentLabel] = array_values($childItems[$parentLabel] ?? []);
            if (empty($childItems[$parentLabel])) {
                unset($childItems[$parentLabel]);
            }
        }

        // 2. Filter navigation items utama
        foreach ($navigationItems as $group => $items) {
            foreach ($items as $index => $navItem) {
                // Parent: tampilkan hanya jika masih memiliki child
                if ($navItem['type'] === 'parent') {
                    if (! isset($childItems[$navItem['label']]) || empty($childItems[$navItem['label']])) {
                        unset($navigationItems[$group][$index]);
                    }
                } else {
                    // Resource / Page biasa
                    if (! self::userCanAccessItem($navItem)) {
                        unset($navigationItems[$group][$index]);
                    }
                }
            }

            // Susun ulang index array dan hapus group kosong
            $navigationItems[$group] = array_values($navigationItems[$group] ?? []);
            if (empty($navigationItems[$group])) {
                unset($navigationItems[$group]);
            }
        }
    }

    /**
     * Cek apakah pengguna dapat mengakses item navigasi tertentu
     */
    protected static function userCanAccessItem(array $item): bool
    {
        try {
            $class = $item['class'] ?? null;
            $type = $item['type'] ?? null;

            if ($type === 'resource' && $class && class_exists($class)) {
                // Gunakan metode bawaan Filament jika tersedia
                if (method_exists($class, 'canViewAny')) {
                    return $class::canViewAny();
                }

                return true;
            }

            if ($type === 'page' && $class && class_exists($class)) {
                // Jika halaman menggunakan Filament Shield (HasPageShield), gunakan canAccess()
                if (method_exists($class, 'canAccess')) {
                    return $class::canAccess();
                }

                // Fallback ke canView() bila tersedia
                if (method_exists($class, 'canView')) {
                    return $class::canView();
                }

                return true;
            }

            // Untuk parent atau item lain, izinkan secara default (akan difilter berdasarkan child)
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }
}

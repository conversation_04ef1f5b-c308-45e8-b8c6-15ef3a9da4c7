<?php

namespace App\Aplikasi\Hotel\Services;

use Illuminate\Session\Store;
use InvalidArgumentException;
use Illuminate\Support\Collection;

class Keranjang
{
 
    protected $session;
 
    protected const KERANJANG_KEY = 'keranjang_hotel';
 
    public function __construct(Store $session)
    {
        $this->session = $session;
    }

 public function exportData(): array
    {
        return [
            'items' => $this->items()->toArray(),
            'total_harga' => $this->totalHarga(),
            'total_modal' => $this->totalModal(),
            'keuntungan' => $this->keuntungan(),
        ];
    }

    public function importData(array $data): void
    {
        // Reset keranjang terlebih dahulu
        $this->kosongkanKeranjang();
        
        // Import items satu per satu
        if (isset($data['items']) && is_array($data['items'])) {
            $items = collect($data['items']);
            $this->updateItems($items);
        }
    }
    
    public function items(): Collection
    {
        return collect($this->session->get(self::KERANJANG_KEY, []));
    }

    /**
     * Update items di session
     *
     * @param Collection $items
     * @return void
     */
    public function updateItems(Collection $items): void
    {
        $this->session->put(self::KERANJANG_KEY, $items);
    }
    /**
     * Menambah item ke keranjang
     *
     * @param array $item
     * @return void
     * @throws InvalidArgumentException
     */
    public function tambahItem(array $item): void
    {
        $this->validasiItem($item);

        $items = $this->items();
        
        // Jika item sudah ada, tambahkan jumlahnya
        if ($items->has($item['id'])) {
            $existingItem = $items->get($item['id']);
            $item['jumlah'] += $existingItem['jumlah'];
        }
        
        $items->put($item['id'], $item);
        $this->updateItems($items);
    }

    /**
     * Menghapus item dari keranjang
     *
     * @param int $id
     * @return void
     */
    public function hapusItem(int $id): void
    {
        $items = $this->items();
        $items->forget($id);
        $this->updateItems($items);
    }

    /**
     * Mengambil item spesifik dari keranjang
     *
     * @param int $id
     * @return array|null
     */
    public function ambilItem(int $id): ?array
    {
        return $this->items()->get($id);
    }

    /**
     * Menghitung total item di keranjang
     *
     * @return int
     */
    public function totalItem(): int
    {
        return $this->items()->count();
    }

    /**
     * Menghitung total harga semua item
     *
     * @return int
     */
    public function totalHarga(): int
    {
        return $this->items()->sum(function ($item) {
            return $item['harga'] * $item['jumlah'];
        });
    }

    /**
     * Menghitung total modal semua item
     *
     * @return int
     */
    public function totalModal(): int
    {
        return $this->items()->sum(function ($item) {
            return $item['harga_modal'] * $item['jumlah'];
        });
    }

    /**
     * Menghitung total keuntungan
     *
     * @return int
     */
    public function keuntungan(): int
    {
        return $this->totalHarga() - $this->totalModal();
    }

    /**
     * Menerapkan diskon berdasarkan persentase
     *
     * @param float $persen
     * @return int
     */
    public function terapkanDiskonPersen(float $persen): int
    {
        $total = $this->totalHarga();
        $diskon = round(($persen / 100) * $total);
        return $total - $diskon;
    }

    /**
     * Menambah jumlah item yang sudah ada
     *
     * @param int $id
     * @param int $tambahan
     * @return void
     */
    public function tambahJumlah(int $id, int $tambahan): void
    {
        $items = $this->items();
        if ($items->has($id)) {
            $item = $items->get($id);
            $item['jumlah'] += $tambahan;
            $items->put($id, $item);
            $this->updateItems($items);
        }
    }

    /**
     * Mengurangi jumlah item yang sudah ada
     *
     * @param int $id
     * @param int $kurang
     * @return void
     */
    public function kurangiJumlah(int $id, int $kurang): void
    {
        $items = $this->items();
        if ($items->has($id)) {
            $item = $items->get($id);
            $jumlahBaru = $item['jumlah'] - $kurang;
            
            if ($jumlahBaru <= 0) {
                $this->hapusItem($id);
            } else {
                $item['jumlah'] = $jumlahBaru;
                $items->put($id, $item);
                $this->updateItems($items);
            }
        }
    }

    /**
     * Menghitung total akhir dengan semua komponen
     *
     * @param float $diskonPersen
     * @param int $diskonNominal
     * @param float $biayaLayananPersen
     * @param float $pajakPersen
     * @return int
     */
    public function hitungTotalAkhir(
        float $diskonPersen = 0,
        int $diskonNominal = 0,
        float $biayaLayananPersen = 0,
        float $pajakPersen = 0
    ): int {
        $total = $this->totalHarga();
        
        // Terapkan diskon persen
        $diskonPersenNominal = round(($diskonPersen / 100) * $total);
        $total -= $diskonPersenNominal;
        
        // Terapkan diskon nominal
        $total -= $diskonNominal;
        
        // Hitung biaya layanan
        $biayaLayanan = round(($biayaLayananPersen / 100) * $total);
        $total += $biayaLayanan;
        
        // Hitung pajak
        $pajak = round(($pajakPersen / 100) * $total);
        $total += $pajak;
        
        return (int) round($total);
    }

    /**
     * Mengosongkan keranjang
     *
     * @return void
     */
    public function kosongkanKeranjang(): void
    {
        $this->updateItems(collect([]));
    }

    /**
     * Validasi item sebelum ditambahkan ke keranjang
     *
     * @param array $item
     * @return void
     * @throws InvalidArgumentException
     */
    protected function validasiItem(array $item): void
    {
        $required = [
            'id' => 'ID item',
            'jenis' => 'Jenis item',
            'produk_id' => 'ID produk',
            'nama_item' => 'Nama item',
            'harga_modal' => 'Harga modal',
            'harga' => 'Harga jual',
            'jumlah' => 'Jumlah',
            'ket' => 'Keterangan'
        ];
        
        // Cek field yang required
        foreach ($required as $field => $label) {
            if (!isset($item[$field])) {
                throw new InvalidArgumentException("Field {$label} harus ada");
            }
        }

        // Validasi jenis item
        // if (!in_array($item['jenis'], ['barang', 'jasa'])) {
        //     throw new InvalidArgumentException('Jenis harus barang atau jasa');
        // }

        // Validasi harga
        if ($item['harga'] < $item['harga_modal']) {
            throw new InvalidArgumentException('Harga jual harus lebih besar dari harga modal');
        }

        // Validasi jumlah
        if ($item['jumlah'] < 1) {
            throw new InvalidArgumentException('Jumlah minimal 1');
        }

        // Validasi tipe data
        if (!is_int($item['id']) || !is_int($item['produk_id'])) {
            throw new InvalidArgumentException('ID harus berupa angka');
        }

        if (!is_int($item['harga']) || !is_int($item['harga_modal'])) {
            throw new InvalidArgumentException('Harga harus berupa angka');
        }

        if (!is_string($item['nama_item']) || !is_string($item['ket'])) {
            throw new InvalidArgumentException('Nama item dan keterangan harus berupa text');
        }
    }
}

# Integrasi WebP Conversion dengan RajaPicker

Dokumentasi ini menjelaskan bagaimana menggunakan fitur konversi WebP yang terintegrasi dengan komponen RajaPicker.

## Fitur WebP Conversion

RajaPicker sekarang mendukung konversi otomatis gambar ke format WebP saat upload. Fitur ini dapat menghemat bandwidth dan meningkatkan performa website.

## Cara Penggunaan

### 1. Mengaktifkan WebP Conversion

```php
use Mo<PERSON>les\Rajapicker\Filament\Forms\Components\RajaPicker;

// Aktifkan konversi WebP
RajaPicker::make('gambar')
    ->convertWebp() // atau ->convertWebp(true)
    ->collection('cms')
    ->multiple();
```

### 2. Menonaktifkan WebP Conversion (Default)

```php
// WebP conversion dinonaktifkan secara default
RajaPicker::make('gambar')
    ->convertWebp(false) // eksplisit menonaktifkan
    ->collection('cms');
```

### 3. <PERSON><PERSON><PERSON> di Filament Resource

```php
// Di Filament Resource Form
public static function form(Form $form): Form
{
    return $form
        ->schema([
            RajaPicker::make('gambar_utama')
                ->label('Gambar Utama')
                ->collection('cms')
                ->convertWebp() // Aktifkan konversi WebP
                ->previewSize(200)
                ->columnSpanFull(),
                
            RajaPicker::make('gallery')
                ->label('Galeri Gambar')
                ->collection('gallery')
                ->multiple()
                ->convertWebp() // Aktifkan untuk multiple upload
                ->previewSize(150),
        ]);
}
```

## Cara Kerja

### 1. Proses Upload dengan WebP Conversion

1. **Upload File**: User memilih file gambar (JPG, PNG, GIF, BMP)
2. **Validasi**: Sistem memvalidasi tipe file dan ukuran
3. **WebP Conversion**: Jika `convertWebp()` aktif, gambar dikonversi ke WebP
4. **Storage**: File WebP disimpan ke media library
5. **Response**: URL file WebP dikembalikan ke RajaPicker

### 2. Konfigurasi WebP

Konversi WebP menggunakan konfigurasi default:
- **Quality**: 80% (kualitas tinggi dengan ukuran optimal)
- **Compression**: Method 6 (kompresi maksimal)
- **Format**: WebP dengan ekstensi `.webp`

### 3. Fallback Mechanism

Jika konversi WebP gagal, sistem akan:
- Log error untuk debugging
- Menggunakan file original
- Tidak menghentikan proses upload

## Monitoring dan Logging

### 1. Log Success

```php
Log::info('WebP conversion successful', [
    'original' => '/path/to/original.jpg',
    'webp' => '/path/to/converted.webp',
    'compression_ratio' => 75.5, // Persentase pengurangan ukuran
    'original_size' => '2.5 MB',
    'webp_size' => '600 KB'
]);
```

### 2. Log Error

```php
Log::warning('WebP conversion failed, using original file', [
    'error' => 'GD extension not available'
]);
```

## Requirements

### 1. Server Requirements

- PHP 8.0+
- GD extension dengan WebP support
- Intervention Image v3
- Cukup memory untuk processing gambar

### 2. Cek WebP Support

```php
use Modules\Rajapicker\Services\WebPConverterService;

$converter = new WebPConverterService();
$isSupported = $converter->isWebPSupported();

if ($isSupported) {
    echo "WebP conversion tersedia";
} else {
    echo "WebP conversion tidak tersedia";
}
```

## Troubleshooting

### 1. WebP Conversion Tidak Berfungsi

**Gejala**: Gambar tetap dalam format asli (JPG/PNG)

**Solusi**:
1. Cek apakah `convertWebp()` dipanggil dengan benar
2. Cek log untuk error messages
3. Pastikan GD extension dengan WebP support terinstall
4. Cek memory limit PHP

### 2. Error "WebP not supported"

**Solusi**:
```bash
# Install GD dengan WebP support
sudo apt-get install libwebp-dev
sudo apt-get install php-gd

# Restart web server
sudo systemctl restart apache2
# atau
sudo systemctl restart nginx
```

### 3. Memory Limit Error

**Solusi**:
```ini
; Di php.ini
memory_limit = 256M
max_execution_time = 300
```

## Best Practices

### 1. Kapan Menggunakan WebP Conversion

✅ **Gunakan WebP untuk**:
- Gambar website (banner, produk, galeri)
- Gambar yang akan ditampilkan di web
- Optimasi performa website

❌ **Jangan gunakan WebP untuk**:
- File yang perlu diedit ulang
- Gambar yang perlu kompatibilitas maksimal
- File yang akan didownload user

### 2. Quality Settings

```php
// Untuk gambar berkualitas tinggi
RajaPicker::make('gambar')
    ->convertWebp()
    ->collection('high_quality');

// Untuk gambar dengan kompresi maksimal
// (perlu modifikasi service untuk custom quality)
```

### 3. Collection Strategy

```php
// Pisahkan collection berdasarkan kebutuhan
RajaPicker::make('banner')
    ->collection('banners')
    ->convertWebp(), // Banner perlu optimasi

RajaPicker::make('document')
    ->collection('documents')
    ->convertWebp(false), // Document tidak perlu WebP
```

## Testing

### 1. Test WebP Conversion

```php
// Test di controller atau command
use Modules\Rajapicker\Services\WebPConverterService;

$converter = new WebPConverterService();
$result = $converter->convertToWebP('/path/to/test.jpg');

if ($result['success']) {
    echo "Conversion berhasil: {$result['compression_ratio']}% pengurangan ukuran";
} else {
    echo "Conversion gagal: {$result['error']}";
}
```

### 2. Test di Browser

1. Upload gambar melalui RajaPicker dengan `convertWebp()` aktif
2. Cek file yang tersimpan di storage
3. Verifikasi ekstensi file adalah `.webp`
4. Cek ukuran file (seharusnya lebih kecil)

## Migration dari Versi Lama

Jika Anda sudah menggunakan RajaPicker tanpa WebP conversion:

1. **Tidak ada breaking changes** - semua kode lama tetap berfungsi
2. **WebP conversion dinonaktifkan secara default**
3. **Aktifkan secara bertahap** untuk testing
4. **Monitor performa** setelah aktivasi

## Support

Jika mengalami masalah dengan WebP conversion:

1. Cek log Laravel di `storage/logs/laravel.log`
2. Pastikan semua requirements terpenuhi
3. Test dengan file gambar yang berbeda
4. Hubungi tim development untuk bantuan lebih lanjut 
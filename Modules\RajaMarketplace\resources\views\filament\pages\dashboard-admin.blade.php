<x-filament-panels::page>
    {{-- Widget Statistik Ringkas --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {{-- Kartu Total Produk --}}
       <div class="bg-white rounded-lg shadow p-6 flex items-center">
            <x-heroicon-o-cube class="w-12 h-12 text-primary-600" />
            <a href="{{ route('filament.rajamember.resources.marketplace.produk.index') }}"> <div class="ml-4">
                <p class="text-sm text-gray-500">Produk Saya</p>
                <p class="text-2xl font-bold text-gray-900">{{ $totalProduk }}</p>
            </div></a>
        </div>

        {{-- Info Toko --}}
        <div class="bg-white rounded-lg shadow p-6 w-full">
            @if($toko)
                <div class="flex items-start space-x-4">
                    {{-- Logo --}}
                    <div class="shrink-0">
                        @if ($toko->logo)
                            <img src="{{ asset($toko->logo) }}" alt="Logo {{ $toko->nama }}" class="w-16 h-16 rounded-full object-cover border" />
                        @else
                            <x-heroicon-o-building-storefront class="w-16 h-16 text-primary-600" />
                        @endif
                    </div>

                    {{-- Detail teks --}}
                    <div>
                        <p class="text-lg font-semibold text-gray-900 mb-1">{{ $toko->nama }}</p>
                        @if($toko->alamat)
                            <p class="text-sm text-gray-600 flex items-center"><x-heroicon-o-map-pin class="w-4 h-4 mr-1" /> {{ $toko->alamat }}</p>
                        @endif
                        @if($toko->telpon)
                            <p class="text-sm text-gray-600 flex items-center"><x-heroicon-o-phone class="w-4 h-4 mr-1" /> {{ $toko->telpon }}</p>
                        @endif
                        @if($toko->email)
                            <p class="text-sm text-gray-600 flex items-center"><x-heroicon-o-envelope class="w-4 h-4 mr-1" /> {{ $toko->email }}</p>
                        @endif
                    </div>
                </div>

                {{-- Foto toko jika ada --}}
                @if($toko->foto)
                    <div class="mt-4">
                        <img src="{{ asset($toko->foto) }}" alt="Foto {{ $toko->nama }}" class="w-full h-40 object-cover rounded-lg border" />
                    </div>
                @endif
            @else
                <div class="flex items-center space-x-3">
                    <x-heroicon-o-building-storefront class="w-12 h-12 text-primary-600" />
                    <p class="text-gray-600">Anda belum memiliki toko.</p>
                </div>
            @endif
        </div>
    </div>

    {{-- Daftar Produk Terbaru --}}
    @if($toko && $produkTerbaru->count())
        <div class="mt-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">5 Produk Terbaru</h3>
                <div class="divide-y divide-gray-100">
                    @foreach($produkTerbaru as $produk)
                        <div class="py-3 flex items-center justify-between">
                            <div class="flex items-center">
                                <x-heroicon-o-square-2-stack class="w-6 h-6 text-gray-400" />
                                <span class="ml-2 text-sm text-gray-700">{{ $produk->nama }}</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">Rp {{ number_format($produk->harga,0,',','.') }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
</x-filament-panels::page> 
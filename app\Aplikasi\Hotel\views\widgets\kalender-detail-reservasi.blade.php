<x-filament::modal id="tampilan-detail" width="5xl" sticky-header>

<x-slot name="heading">
    <h3 class="text-base sm:text-lg font-medium mb-2 sm:mb-3">Reservasi pada tanggal
        {{ $hariTerpilih }}/{{ $bulan }}/{{ $tahun }}</h3>
</x-slot>

<div wire:loading.flex wire:target="pilihHari" class="w-full justify-center py-8">
    <div class="flex flex-col items-center space-y-4">
        <div
            class="h-10 w-10 animate-spin rounded-full border-4 border-primary-500 border-t-transparent">
        </div>
        <p class="text-sm text-gray-500">Memuat data reservasi...</p>
    </div>
</div>

<div wire:loading.remove wire:target="pilihHari" class="overflow-x-auto rounded-lg border">
    <!-- Konten tabel tetap sama -->
</div>

<div class="overflow-x-auto rounded-lg border">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tamu</th>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kamar</th>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Check-in</th>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Check-out</th>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durasi</th>
                <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            @foreach ($reservasiData[$hariTerpilih] as $reservasi)
                <tr class="hover:bg-gray-50">
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        <div class="font-medium">{{ $reservasi['tamu'] }}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        <div>{{ $reservasi['kamar'] }}</div>
                        <div class="text-xs text-gray-500">{{ $reservasi['kamar_tipe'] ?? '-' }}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        {{ $reservasi['check_in'] }}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        {{ $reservasi['check_out'] }}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        {{ $reservasi['durasi'] ?? '-' }} hari
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                        <span
                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $reservasi['status_kelas'] ?? 'bg-blue-500' }} text-white">
                            {{ $reservasi['status'] }}
                        </span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

</x-filament::modal>
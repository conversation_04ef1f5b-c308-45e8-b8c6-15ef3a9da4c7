<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperTamu
 */
class Tamu extends Model
{
    use HasFactory;

      public function getTable()
    {
        return config('tabel.t_tamu.nama_tabel', 'kategori');
    }


    public function getFillable()
    {
        // Ambil kolom dari konfigurasi dengan nilai default array kosong
        $kolom = config('tabel.t_tamu.kolom', []);

        // Nilai default jika konfigurasi tidak tersedia atau tidak valid
        $defaultFillable = [
            'jenis', 'nama', 'email', 'telpon', 'jenis_identitas', 'foto_identitas', 'no_identitas', 'created_at', 'updated_at',
        ];

        // Pastikan $kolom adalah array
        if (!is_array($kolom) || empty($kolom)) {
            return $defaultFillable;
        }

        // Hapus kolom 'id' dari fillable karena biasanya ID tidak perlu fillable
        return array_values(array_filter($kolom, function ($item) {
            return $item !== 'id';
        }));
    }




}

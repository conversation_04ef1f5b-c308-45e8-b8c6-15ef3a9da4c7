<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\MetodePembayaranUtama;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperMetodePembayaran
 */
class MetodePembayaran extends MetodePembayaranUtama
{
    use HasFactory;

    // protected $table = 'metode_pembayaran';
    // protected $fillable = [
    //     'id',
    //     'nama',
    //     'jenis',
    //     'ket',
    //     'gambar',
    //     'info_tujuan',
    //     'info_pengirim',
    //     'status',
    // ];
  protected $casts = [
        'status' => 'boolean',
        'info_pengirim' => 'array', // Menggunakan array agar Laravel otomatis encode/decode
    ];
 
    
    public function pembayaran(): BelongsTo
    {
        return $this->belongsTo(Pembayaran::class);
    }
 
 
 
 
}
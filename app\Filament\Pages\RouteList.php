<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class RouteList extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'pages.rute';

    protected static ?string $title = 'Daftar Rute';
    protected static ?string $slug = 'system/rute';

    protected static ?string $navigationLabel = 'Daftar Rute';
    protected static ?string $navigationGroup = 'System';
    protected static ?int $navigationSort = 3;
    protected static bool $shouldRegisterNavigation = true;

    public $search = '';
    public $selectedType = '';
    public Collection $routeCollection;

    // Tambahkan properti ini
    public $selectedPanel = null;

    public $panelGroups = [];

    public function mount(): void
    {
        // Ambil panel dari query string jika ada
        $this->selectedPanel = request()->query('panel', 'Semua');
        $this->loadRoutes();
    }

    // Tambahkan method baru untuk beralih panel
    public function switchPanel($panel): void
    {
        $this->selectedPanel = $panel;

        // Gunakan redirect untuk menyimpan parameter di URL
        $this->redirect(route('filament.pages.rute', ['panel' => $panel]));
    }
    // Ubah method loadRoutes()
    protected function loadRoutes(): void
    {
        $routes = Route::getRoutes();
        $routeData = [];
        $panelGroups = ['Semua' => 0];

        foreach ($routes as $route) {
            $name = $route->getName() ?: '-';
            $type = $this->determineRouteType($name);
            $panel = $this->determinePanelName($name);

            // Tambahkan jumlah rute ke panel
            if (!isset($panelGroups[$panel])) {
                $panelGroups[$panel] = 0;
            }
            $panelGroups[$panel]++;

            $routeData[] = [
                'domain' => $route->getDomain() ?: '-',
                'method' => implode('|', $route->methods()),
                'uri' => $route->uri(),
                'name' => $name,
                'action' => $route->getActionName(),
                'middleware' => implode(', ', $route->middleware()),
                'type' => $type,
                'panel' => $panel,
            ];
        }

        // Perbarui jumlah total
        $panelGroups['Semua'] = count($routeData);

        $this->panelGroups = $panelGroups;
        $this->routeCollection = collect($routeData);
    }

    protected function determineRouteType(string $name): string
    {
        if (Str::startsWith($name, 'filament.resources.')) {
            return 'Resource';
        } elseif (Str::startsWith($name, 'filament.pages.')) {
            return 'Page';
        } elseif (Str::startsWith($name, 'filament.clusters.')) {
            $parts = explode('.', $name);
            if (count($parts) > 3 && isset($parts[3]) && $parts[3] === 'resources') {
                return 'Cluster Resource: ' . $parts[2];
            }
            return 'Cluster';
        } elseif (Str::startsWith($name, 'filament.')) {
            return 'Filament Lainnya';
        } else {
            return 'Web';
        }
    }

    // Ubah method getFilteredRoutes()
    public function getFilteredRoutes(): Collection
    {
        $filteredRoutes = $this->routeCollection;

        if (!empty($this->selectedPanel) && $this->selectedPanel !== 'Semua') {
            $filteredRoutes = $filteredRoutes->filter(function ($route) {
                return $route['panel'] === $this->selectedPanel;
            });
        }

        if (!empty($this->search)) {
            $searchLower = strtolower($this->search);
            $filteredRoutes = $filteredRoutes->filter(function ($route) use ($searchLower) {
                return Str::contains(strtolower($route['name']), $searchLower) ||
                    Str::contains(strtolower($route['uri']), $searchLower) ||
                    Str::contains(strtolower($route['action']), $searchLower);
            });
        }

        if (!empty($this->selectedType)) {
            $filteredRoutes = $filteredRoutes->filter(function ($route) {
                return Str::startsWith($route['type'], $this->selectedType);
            });
        }

        return $filteredRoutes;
    }

    public function getRouteTypes(): array
    {
        $types = $this->routeCollection->pluck('type')->unique()->sort()->values()->toArray();
        return array_combine($types, $types);
    }

    // Tambahkan method baru ini
    protected function determinePanelName(string $name): string
    {
        $parts = explode('.', $name);

        if (count($parts) >= 2 && $parts[0] === 'filament') {
            if (isset($parts[1]) && $parts[1] !== 'pages' && $parts[1] !== 'resources' && $parts[1] !== 'clusters') {
                return ucfirst($parts[1]);
            }
        }

        return 'Web';
    }
}

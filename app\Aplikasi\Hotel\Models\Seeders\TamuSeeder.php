<?php

namespace App\Aplikasi\Hotel\Models\Seeders;

use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use App\Aplikasi\Hotel\Models\Tamu;

class TamuSeeder extends Seeder
{
    /**
     * Menjalankan proses seeding database untuk Tamu.
     * 
     * @return void
     */
    public function run(): void
    {
        // Mendapatkan jumlah data dari environment variable atau default ke 10
        $jumlah = env('SEEDER_COUNT', 10);
        
        // Inisialisasi Faker dengan lokalisasi Indonesia
        $faker = Faker::create('id_ID');
        
        echo "Membuat {$jumlah} data Tamu...\n";
        
        // Tipe identitas yang mungkin
        $jenisIdentitas = ['KTP', 'SIM', 'PASSPORT', 'KITAS'];
        
        // Tipe tamu yang mungkin
        $jenisTamu = ['PERSONAL', 'CORPORATE', 'TRAVEL_AGENT', 'GOVERNMENT'];
        
        for ($i = 0; $i < $jumlah; $i++) {
            // Pilih jenis tamu secara acak
            $tipe = $faker->randomElement($jenisTamu);
            
            // Data tamu akan bervariasi berdasarkan tipe
            if ($tipe === 'PERSONAL') {
                // Tamu perorangan
                // Membuat tamu personal
                $tamu = new Tamu();
                $tamu->jenis_tamu = $tipe;
                $tamu->nama = $faker->name;
                $tamu->email = $faker->email;
                $tamu->telpon = $faker->phoneNumber;
                $tamu->jenis_identitas = $faker->randomElement($jenisIdentitas);
                $tamu->no_identitas = $faker->numerify('##############');
                // Asumsi foto identitas akan diupload manual atau bisa ditambahkan path default
                $tamu->foto_identitas = null;
                $tamu->save();
            } elseif ($tipe === 'CORPORATE') {
                // Tamu korporat
                $perusahaan = $faker->company;
                // Membuat tamu korporat
                $tamu = new Tamu();
                $tamu->jenis_tamu = $tipe;
                $tamu->nama = $perusahaan . ' - ' . $faker->name;
                $tamu->email = $faker->companyEmail;
                $tamu->telpon = $faker->phoneNumber;
                $tamu->jenis_identitas = 'NPWP';
                $tamu->no_identitas = $faker->numerify('#################');
                $tamu->foto_identitas = null;
                $tamu->save();
            } elseif ($tipe === 'TRAVEL_AGENT') {
                // Agen travel
                $agenTravel = ['Traveloka', 'Tiket.com', 'Booking.com', 'Agoda', 'Expedia', 'AirBnB'];
                $namaAgen = $faker->randomElement($agenTravel);
                
                // Membuat tamu agen travel
                $tamu = new Tamu();
                $tamu->jenis_tamu = $tipe;
                $tamu->nama = $namaAgen . ' - ' . $faker->name;
                $tamu->email = strtolower(str_replace(' ', '', $namaAgen)) . '@' . $faker->freeEmailDomain;
                $tamu->telpon = $faker->phoneNumber;
                $tamu->jenis_identitas = 'NPWP';
                $tamu->no_identitas = $faker->numerify('#################');
                $tamu->foto_identitas = null;
                $tamu->save();
            } else {
                // Pemerintah
                $instansi = ['Kementerian', 'Dinas', 'Badan', 'BUMN', 'Pemda'];
                $namaInstansi = $faker->randomElement($instansi) . ' ' . $faker->word;
                
                // Membuat tamu instansi pemerintah
                $tamu = new Tamu();
                $tamu->jenis_tamu = $tipe;
                $tamu->nama = $namaInstansi . ' - ' . $faker->name;
                $tamu->email = $faker->email;
                $tamu->telpon = $faker->phoneNumber;
                $tamu->jenis_identitas = 'NIP';
                $tamu->no_identitas = $faker->numerify('##################');
                $tamu->foto_identitas = null;
                $tamu->save();
            }
        }
        
        echo "Selesai membuat {$jumlah} data Tamu.\n";
    }
}
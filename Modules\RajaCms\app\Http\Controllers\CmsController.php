<?php

namespace Modules\RajaCms\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\RajaCms\Models\Cms;
use App\Models\Kategori;
use App\Models\Konfig;
use App\Models\KonfigAdmin;
use App\Models\Toko;
use Datlechin\FilamentMenuBuilder\Models\Menu;
use Illuminate\Http\Request;

class CmsController extends Controller
{
    public function __construct()
    {
        $namaTema = config('tema.aktif', 'default');
        $temapath = public_path('tema/' . $namaTema);
        $temaurl =  ENV('APP_URL') . '/tema/' . $namaTema;
        $temaAsset = asset('tema/' . $namaTema);
        $menu = Menu::location('website_header');
        // $menu = \Datlechin\FilamentMenuBuilder\Models\Menu::location('website_header')->first();
        $menuItems = $menu ? $menu->menuItems()->get() : collect([]);
        // Gunakan jcolObject agar bisa diakses dengan property
        $infoweb = Konfig::jcolObject('website');

        $halaman = Cms::where('jenis', 'halaman')->get();

        view()->share('toko', $infoweb);
        view()->share('infoweb', $infoweb);
        //  dd($menu);
        view()->share('temaNama', $namaTema);
        view()->share('temaPath', $temapath);
        view()->share('temaUrl', $temaurl);
        view()->share('temaAsset', $temaAsset);
        view()->share('menu', $menu);
        view()->share('menuItems', $menuItems);
    }

    public function beranda()
    {
        // Ambil data slideshow

        // Ambil data acara dengan metadata jadwal (sebagai koleksi)
        // Menggunakan join dengan tabel meta untuk mengurutkan berdasarkan meta acara_jadwal_dari
        $event = Cms::where('jenis', 'ACARA')
            ->where('status', 'tampil')
            
            ->limit(5)
            ->get();

        $halaman = Cms::where('jenis', 'halaman')->where('status', 'home')->first();
        $slideshow = $halaman->json['slideshow'] ?? [];
        return view('tema::layout.beranda', compact('slideshow', 'halaman', 'event'));
    }

    /**
     * Menampilkan daftar semua artikel/blog
     */
    public function daftarArtikel(Request $request)
    {
        $kategori = $request->kategori ?? null;
        $cari = $request->cari ?? null;

        $artikelQuery = Cms::where('jenis', 'ARTIKEL')->with('kategori');

        if ($kategori) {
            $artikelQuery->where('kategori_id', $kategori);
        }

        if ($cari) {
            $artikelQuery->where(function ($query) use ($cari) {
                $query->where('judul', 'like', "%{$cari}%")
                    ->orWhere('isi', 'like', "%{$cari}%");
            });
        }

        $artikel = $artikelQuery->latest()->paginate(10);
        $kategoris = Cms::where('jenis', 'ARTIKEL')->get();

        return view('tema::layout.artikel_list', compact('artikel', 'kategoris'));
    }

    /**
     * Menampilkan detail artikel/blog
     */
    public function detailArtikel($slug)
    {
        $artikel = Cms::where('slug', $slug)->where('jenis', 'ARTIKEL')->firstOrFail();
        $artikelTerkait = Cms::where('kategori_id', $artikel->kategori_id)
            ->where('id', '!=', $artikel->id)
            ->where('jenis', 'ARTIKEL')
            ->limit(3)
            ->get();

        $kategoris = Kategori::where('jenis', 'ARTIKEL')->get();
        return view('tema::layout.artikel_detail', compact('artikel', 'artikelTerkait', 'kategoris'));
    }
 
    /**
     * Menampilkan halaman statis (pages)
     */
    public function halaman($slug)
    {
        $halaman = Cms::where('slug', $slug)->where('jenis', 'halaman')->firstOrFail();

        return view('tema::layout.halaman', compact('halaman'));
    }

    public function acara()
    {
        $acara = Cms::where('jenis', 'ACARA')->get();
        return view('tema::layout.acara', compact('acara'));
    }

    public function daftarAcara()
    {
        $acara = Cms::where('jenis', 'ACARA')->get();
        return view('tema::layout.acara', compact('acara'));
    }

    public function detailAcara($slug)
    {
        $halaman = Cms::where('slug', $slug)->where('jenis', 'ACARA')->firstOrFail();
        return view('tema::layout.acara-detail', compact('halaman'));
    }

    public function detailEvent($slug)
    {
        $halaman = Cms::where('slug', $slug)->where('jenis', 'ACARA')->firstOrFail();
        return view('tema::layout.acara-detail', compact('halaman'));
    }

    public function marketplace()
    {
        return view('tema::layout.marketplace');
    }
} 
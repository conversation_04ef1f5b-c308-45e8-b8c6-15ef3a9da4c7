# RajaPicker Enhancement: Collection Selector & Pagination

## Overview

RajaPicker telah ditingkatkan dengan fitur collection selector dan pagination untuk memberikan pengalaman yang lebih baik dalam mengelola media library yang besar.

## Fitur Baru

### 1. Collection Selector
- Dropdown select untuk memilih collection di modal picker
- Option "Semua Collection" sebagai default untuk menampilkan semua gambar
- Filter real-time berdasarkan collection yang dipilih
- UI yang konsisten dengan design FilamentPHP

### 2. Pagination System
- Implementasi pagination dengan 24 item per halaman untuk performa optimal
- Navigation controls: Previous/Next buttons dan page numbers
- Responsive pagination design untuk mobile dan desktop
- Informasi jumlah data: "Menampilkan X sampai Y dari Z hasil"
- Page numbers dengan max 5 halaman visible untuk UX yang baik

## API Endpoints Baru

### GET /api/media/collections
Mendapatkan daftar semua collections yang tersedia.

**Response:**
```json
[
    "default",
    "gallery", 
    "cms",
    "products",
    "avatars"
]
```

### GET /api/media/all-images (Enhanced)
Mendapatkan media dengan pagination dan filter collection.

**Parameters:**
- `page` (optional): Nomor halaman (default: 1)
- `per_page` (optional): Jumlah item per halaman (default: 24)
- `collection` (optional): Filter berdasarkan collection

**Example:**
```
GET /api/media/all-images?page=2&per_page=12&collection=gallery
```

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "Sample Image",
            "file_name": "sample.jpg",
            "url": "/gallery/sample.jpg",
            "mime_type": "image/jpeg",
            "size": 1024000,
            "collection_name": "gallery",
            "width": 1920,
            "height": 1080,
            "created_at": "2025-01-06T10:00:00.000000Z"
        }
    ],
    "pagination": {
        "current_page": 2,
        "last_page": 5,
        "per_page": 12,
        "total": 58,
        "from": 13,
        "to": 24,
        "has_more_pages": true
    }
}
```

## Penggunaan

### Basic Usage
Penggunaan RajaPicker tetap sama seperti sebelumnya:

```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

RajaPicker::make('gambar')
    ->label('Pilih Gambar')
    ->collection('cms')
    ->previewSize(150)
```

### Advanced Configuration
```php
RajaPicker::make('featured_image')
    ->label('Gambar Utama')
    ->collection('cms')
    ->previewSize(200)
    ->maxFileSize(10)
    ->placeholder('Pilih atau upload gambar utama')
    ->enablePicker()
    ->enableUploader()
```

### Multiple Selection
```php
RajaPicker::make('galeri')
    ->multiple()
    ->collection('gallery')
    ->previewSize(150)
    ->label('Galeri Foto')
```

## UI/UX Improvements

### Collection Filter
- Dropdown select dengan styling Tailwind CSS yang konsisten
- Auto-load collections dari API
- Real-time filtering tanpa reload halaman
- Option "Semua Collection" untuk melihat semua gambar

### Pagination Controls
- Previous/Next buttons dengan disable state
- Page numbers dengan highlighting untuk halaman aktif
- Responsive design untuk mobile dan desktop
- Loading states dengan spinner
- Informasi jumlah data yang jelas

### Responsive Design
- Mobile: Previous/Next buttons saja
- Desktop: Full pagination dengan page numbers
- Grid layout yang responsive (2-3-4 kolom)
- Touch-friendly untuk mobile devices

## Technical Implementation

### Frontend (Alpine.js)
```javascript
// State management baru
availableCollections: [],
selectedCollection: 'all',
pagination: null,
currentPage: 1,

// Method baru
async loadCollections() { ... }
async loadMediaWithFilter() { ... }
async loadPage(page) { ... }
getPageNumbers() { ... }
```

### Backend (Laravel)
```php
// MediaController method baru
public function getCollections(): JsonResponse { ... }
public function getAllImages(Request $request): JsonResponse { ... }
```

## Performance Optimization

### Pagination Benefits
- Mengurangi loading time dengan membatasi 24 item per halaman
- Mengurangi memory usage di browser
- Faster API response dengan data yang lebih sedikit
- Better user experience dengan loading yang cepat

### Collection Filter Benefits
- Mengurangi jumlah data yang ditampilkan
- Memudahkan user menemukan gambar yang diinginkan
- Organized media management
- Scalable untuk media library yang besar

## Testing

### Manual Testing
Akses halaman testing di: `https://hotel.rid/testing/rajapicker-pagination`

### Test Cases
1. **API Collections Test**: Test endpoint `/api/media/collections`
2. **API Pagination Test**: Test endpoint dengan pagination
3. **Collection Filter Test**: Test filtering berdasarkan collection
4. **Interactive Pagination**: Test navigasi halaman secara interaktif

### Test Features
- Console logging untuk debugging
- Visual feedback untuk setiap test
- Interactive pagination dengan Alpine.js
- Real-time data loading dan filtering

## Compatibility

### Backward Compatibility
- ✅ Tetap kompatibel dengan implementasi existing
- ✅ Tidak mengubah API endpoint URLs yang sudah ada
- ✅ Format penyimpanan tetap sama: `/namacollection/namagambar.jpg`
- ✅ Tetap terintegrasi dengan Spatie Media Library
- ✅ Compatible dengan PakaiJcol slideshow integration

### Browser Support
- Modern browsers dengan ES6+ support
- Alpine.js 3.x compatibility
- Responsive design untuk semua device sizes
- Touch support untuk mobile devices

## Troubleshooting

### Common Issues

1. **Collections tidak muncul**
   - Pastikan endpoint `/api/media/collections` dapat diakses
   - Check console untuk error JavaScript
   - Verify media library memiliki data

2. **Pagination tidak berfungsi**
   - Check endpoint `/api/media/all-images` response format
   - Verify pagination metadata dalam response
   - Check Alpine.js initialization

3. **Filter collection tidak bekerja**
   - Verify collection parameter dikirim ke API
   - Check collection name spelling
   - Verify media memiliki collection_name yang benar

### Debug Mode
Untuk debugging, buka browser console dan monitor:
- API requests dan responses
- Alpine.js state changes
- Error messages dan warnings
- Performance metrics

## Future Enhancements

### Planned Features
- Search functionality dalam modal picker
- Sort options (name, date, size)
- Bulk selection untuk multiple mode
- Preview modal untuk gambar
- Drag & drop reordering untuk multiple selection

### Performance Improvements
- Lazy loading untuk images
- Virtual scrolling untuk large datasets
- Image compression options
- CDN integration untuk faster loading

## Changelog

**Version 1.1.0 (2025-01-06)**
- ✅ Added collection selector dropdown
- ✅ Implemented pagination system (24 items per page)
- ✅ Enhanced API endpoints with pagination support
- ✅ Responsive pagination controls
- ✅ Interactive testing page
- ✅ Improved UI/UX with loading states
- ✅ Backward compatibility maintained

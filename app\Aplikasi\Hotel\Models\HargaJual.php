<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\HargaJual as ModelsHargaJual;
use Illuminate\Database\Eloquent\Model;
 

/**
 * @mixin IdeHelperHargaJual
 */
class HargaJual extends ModelsHargaJual
{
 
    
 
 
    
    protected $casts = [
        'tgl_mulai' => 'datetime',
        'tgl_selesai' => 'datetime',
        'harga' => 'integer',
        'minimal' => 'integer',
        'maksimal' => 'integer',
    ];
 
    // public function produk()
    // {
    //     return $this->belongsTo(Produk::class);
    // }
    
    public function kamar()
    {
        return $this->belongsTo(Kamar::class);
    }
     
    public function isActive(): bool
    {
        $now = now();
        
        // If no date range is set, the price is considered always active
        if (!$this->tgl_mulai && !$this->tgl_selesai) {
            return true;
        }
        
        // If only start date is set, check if now is after start date
        if ($this->tgl_mulai && !$this->tgl_selesai) {
            return $now->gte($this->tgl_mulai);
        }
        
        // If only end date is set, check if now is before end date
        if (!$this->tgl_mulai && $this->tgl_selesai) {
            return $now->lte($this->tgl_selesai);
        }
        
        // If both dates are set, check if now is between both dates
        return $now->between($this->tgl_mulai, $this->tgl_selesai);
    }
    
   
    public function scopeActive($query)
    {
        $now = now();
        
        return $query->where(function ($q) use ($now) {
            $q->where(function ($q) {
                $q->whereNull('tgl_mulai')->whereNull('tgl_selesai');
            })->orWhere(function ($q) use ($now) {
                $q->whereNull('tgl_selesai')->where('tgl_mulai', '<=', $now);
            })->orWhere(function ($q) use ($now) {
                $q->whereNull('tgl_mulai')->where('tgl_selesai', '>=', $now);
            })->orWhere(function ($q) use ($now) {
                $q->where('tgl_mulai', '<=', $now)->where('tgl_selesai', '>=', $now);
            });
        });
    }
    
    /**
     * Check if this price is applicable to the given quantity.
     */
    public function isApplicableForQuantity(int $quantity): bool
    {
        // If no min/max are set, the price is applicable to any quantity
        if (!$this->minimal && !$this->maksimal) {
            return true;
        }
        
        // If only min is set, check if quantity is at least min
        if ($this->minimal && !$this->maksimal) {
            return $quantity >= $this->minimal;
        }
        
        // If only max is set, check if quantity is at most max
        if (!$this->minimal && $this->maksimal) {
            return $quantity <= $this->maksimal;
        }
        
        // If both are set, check if quantity is between min and max
        return $quantity >= $this->minimal && $quantity <= $this->maksimal;
    }
}
<?php

namespace Modules\Rajapicker\Filament\rajamember\Resources\MediaResource\Widgets;

use Mo<PERSON>les\Rajapicker\Models\MediaMember as Media;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MediaStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalMedia = Media::count();
        $totalImages = Media::where('mime_type', 'like', 'image/%')->count();
        $totalVideos = Media::where('mime_type', 'like', 'video/%')->count();
        $totalDocuments = Media::where('mime_type', 'like', 'application/%')->count();
        
        // Hitung total ukuran file dalam MB
        $totalSize = Media::sum('size');
        $totalSizeMB = round($totalSize / 1024 / 1024, 2);
        
        return [
            Stat::make('Total Media', $totalMedia)
                ->description('File media di library')
                ->descriptionIcon('heroicon-m-photo')
                ->color('primary'),
                
            Stat::make('Gambar', $totalImages)
                ->description('File gambar')
                ->descriptionIcon('heroicon-m-camera')
                ->color('success'),
                
            Stat::make('Video', $totalVideos)
                ->description('File video')
                ->descriptionIcon('heroicon-m-video-camera')
                ->color('info'),
                
            Stat::make('Dokumen', $totalDocuments)
                ->description('File dokumen')
                ->descriptionIcon('heroicon-m-document')
                ->color('warning'),
                
            Stat::make('Total Ukuran', $totalSizeMB . ' MB')
                ->description('Ruang storage terpakai')
                ->descriptionIcon('heroicon-m-server')
                ->color('gray'),
        ];
    }
}

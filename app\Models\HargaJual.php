<?php

namespace App\Models;

 
use Illuminate\Database\Eloquent\Model;
 

/**
 * @mixin IdeHelperHargaJual
 */
class HargaJual extends Model
{
    public function getTable()
    {
         return config('tabel.t_harga_jual.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_harga_jual.kolom', []);
    }
 
    // public function produk()
    // {
    //     return $this->belongsTo(Produk::class);
    // }


    // public function kamar()
    // {
    //     return $this->belongsTo(Kamar::class);
    // }


}

<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use ValentinMorice\FilamentJsonColumn\JsonInfolist;

class ViewCms extends ViewRecord
{
    protected static string $resource = CmsResource::class;

    protected int | string | array $columnSpan = 'full';

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),

            Actions\Action::make('back')
                ->label('Kembali')
                ->icon('heroicon-o-arrow-left')
                ->color('gray')
                ->url(fn() => CmsResource::getUrl()),
        ];
    }

    public  function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                \Filament\Infolists\Components\Section::make('Informasi Konten')
                    ->schema([




                        Grid::make(2)
                            ->schema([
                                TextEntry::make('judul')
                                    ->label('Judul')
                                    ->weight(\Filament\Support\Enums\FontWeight::Bold)
                                    ->size(\Filament\Infolists\Components\TextEntry\TextEntrySize::Large)
                                    ->columnSpanFull(),

                                TextEntry::make('jenis')
                                    ->label('Jenis Konten')
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'ARTIKEL' => 'success',
                                        'HALAMAN' => 'info',
                                        'SLIDESHOW' => 'warning',
                                        'WIDGET' => 'danger',
                                        default => 'gray',
                                    }),

                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match ($state) {
                                        'tampil' => 'success',
                                        'draft' => 'gray',
                                        'home' => 'primary',
                                        default => 'secondary',
                                    }),

                                TextEntry::make('slug')
                                    ->label('Slug')
                                    ->icon('heroicon-o-link')
                                    ->url(fn($record) => url('/' . $record->slug))
                                    ->openUrlInNewTab(),

                                TextEntry::make('created_at')
                                    ->label('Dibuat Pada')
                                    ->dateTime('d M Y, H:i')
                                    ->icon('heroicon-o-calendar'),

                                TextEntry::make('updated_at')
                                    ->label('Diperbarui Pada')
                                    ->dateTime('d M Y, H:i')
                                    ->icon('heroicon-o-clock'),
                            ]),
                    ]),

                Grid::make()
                    ->columns(6)
                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN', 'WIDGET', 'ACARA']))
                    ->schema([
                        Section::make()
                            ->columnSpan(4)
                            ->columns(1)
                            ->schema([
                                TextEntry::make('isi')
                                    ->label('')
                                    ->html()
                                    ->extraAttributes(['class' => 'prose max-w-none'])
                                    ->columnSpanFull()
                                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN', 'WIDGET', 'ACARA'])),


                                \Filament\Infolists\Components\ImageEntry::make('gambar')
                                    ->label('Gambar Utama')
                                    ->height(300)
                                    ->extraImgAttributes(['class' => 'rounded-lg shadow-md object-cover'])
                                    ->defaultImageUrl(url('/noimage.jpg'))
                                    ->checkFileExistence(false)
                                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN']))
                                    ->state(function ($record) {
                                        // Jika gambar adalah array, ambil elemen pertama
                                        if (is_array($record->gambar)) {
                                            return $record->gambar[0] ?? null;
                                        }

                                        // Jika gambar adalah string JSON, decode dan ambil elemen pertama
                                        if (is_string($record->gambar) && (str_starts_with($record->gambar, '[') || str_starts_with($record->gambar, '{'))) {
                                            $decoded = json_decode($record->gambar, true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                                if (isset($decoded[0])) {
                                                    return $decoded[0];
                                                } elseif (!empty($decoded)) {
                                                    // Jika array asosiatif, ambil nilai pertama
                                                    return reset($decoded);
                                                }
                                            }
                                        }

                                        // Jika bukan array atau JSON, kembalikan apa adanya
                                        return $record->gambar;
                                    }),


                                TextEntry::make('gambar_gallery')
                                    ->label('Galeri Gambar')
                                    ->html()
                                    ->columnSpanFull()
                                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN']))
                                    ->state(function ($record) {
                                        $gambarArray = [];

                                        // Jika gambar adalah array
                                        if (is_array($record->gambar)) {
                                            $gambarArray = $record->gambar;
                                        }
                                        // Jika gambar adalah string JSON
                                        elseif (is_string($record->gambar) && (str_starts_with($record->gambar, '[') || str_starts_with($record->gambar, '{'))) {
                                            $decoded = json_decode($record->gambar, true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                                // Jika array numerik
                                                if (isset($decoded[0])) {
                                                    $gambarArray = $decoded;
                                                }
                                                // Jika array asosiatif dengan UUID
                                                elseif (!empty($decoded)) {
                                                    $gambarArray = array_values($decoded);
                                                }
                                            }
                                        }
                                        // Jika gambar adalah string biasa
                                        elseif (is_string($record->gambar) && !empty($record->gambar)) {
                                            $gambarArray = [$record->gambar];
                                        }

                                        // Jika tidak ada gambar, kembalikan pesan
                                        if (empty($gambarArray)) {
                                            return '<div class="p-4 bg-gray-100 rounded-lg text-gray-600">Tidak ada gambar yang tersedia.</div>';
                                        }

                                        // Buat galeri gambar
                                        $html = '<div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">';
                                        foreach ($gambarArray as $gambar) {
                                            $imgUrl = asset('storage/' . $gambar);
                                            $html .= '<div class="relative group overflow-hidden rounded-lg shadow-md">';
                                            $html .= '<img src="' . $imgUrl . '" alt="Gambar" class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110">';
                                            $html .= '<div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-center justify-center">';
                                            $html .= '<a href="' . $imgUrl . '" target="_blank" class="p-2 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">';
                                            $html .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                                            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />';
                                            $html .= '</svg>';
                                            $html .= '</a>';
                                            $html .= '</div>';
                                            $html .= '</div>';
                                        }
                                        $html .= '</div>';

                                        return $html;
                                    }),
                                TextEntry::make('pembicara_list')
                                    ->label('Daftar Pembicara')
                                    ->html()
                                    ->columnSpanFull()
                                    ->visible(fn($record) => $record->jenis === 'ACARA')
                                    ->state(function ($record) {
                                        // Ambil data pembicara menggunakan accessor
                                        $pembicara = $record->pembicara;

                                        // Jika tidak ada data pembicara, kembalikan pesan
                                        if (empty($pembicara) || !is_array($pembicara)) {
                                            return '<div class="p-4 bg-gray-100 rounded-lg text-gray-600">Tidak ada data pembicara yang tersedia.</div>';
                                        }

                                        // Buat tampilan pembicara
                                        $html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">';
                                        foreach ($pembicara as $item) {
                                            $html .= '<div class="bg-white rounded-lg shadow-md overflow-hidden">';

                                            // Tampilkan foto pembicara jika ada
                                            if (!empty($item['foto'])) {
                                                $foto = $item['foto'];

                                                // Jika foto adalah array, ambil elemen pertama
                                                if (is_array($foto)) {
                                                    if (isset($foto[0])) {
                                                        $foto = $foto[0];
                                                    } else {
                                                        $foto = reset($foto);
                                                    }
                                                }

                                                $imgUrl = asset('storage/' . $foto);
                                                $html .= '<div class="relative h-48 overflow-hidden">';
                                                $html .= '<img src="' . $imgUrl . '" alt="' . htmlspecialchars($item['nama'] ?? 'Pembicara') . '" class="w-full h-full object-cover">';
                                                $html .= '</div>';
                                            } else {
                                                // Tampilkan placeholder jika tidak ada foto
                                                $html .= '<div class="bg-gray-200 h-48 flex items-center justify-center">';
                                                $html .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                                                $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />';
                                                $html .= '</svg>';
                                                $html .= '</div>';
                                            }

                                            // Tampilkan informasi pembicara
                                            $html .= '<div class="p-4">';
                                            $html .= '<h3 class="text-lg font-semibold text-gray-800">' . htmlspecialchars($item['nama'] ?? 'Tanpa Nama') . '</h3>';

                                            if (!empty($item['jabatan'])) {
                                                $html .= '<p class="text-sm text-gray-600 mt-1">' . htmlspecialchars($item['jabatan']) . '</p>';
                                            }

                                            $html .= '</div>';
                                            $html .= '</div>';
                                        }
                                        $html .= '</div>';

                                        return $html;
                                    }),
                                // Untuk slideshow, tampilkan jumlah slide
                                TextEntry::make('slide_count')
                                    ->label('Jumlah Slide')
                                    ->state(function ($record) {
                                        $slideshow = $record->slideshow;
                                        $count = is_array($slideshow) ? count($slideshow) : 0;
                                        return $count;
                                    })
                                    ->formatStateUsing(function ($state) {
                                        return "{$state} slide";
                                    })
                                    ->visible(fn($record) => $record->jenis === 'SLIDESHOW'),
                            ]),

                        Section::make()
                            ->columnSpan(2)
                            ->columns(1)
                            ->schema([
                                // SEO Information
                                TextEntry::make('seo_keywords')
                                    ->label('SEO Keywords')
                                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN', 'ACARA']))
                                    ->state(fn($record) => $record->seo['keywords'] ?? null),

                                TextEntry::make('seo_description')
                                    ->label('SEO Description')
                                    ->visible(fn($record) => in_array($record->jenis, ['ARTIKEL', 'HALAMAN', 'ACARA']))
                                    ->state(fn($record) => $record->seo['description'] ?? null),
                                    
                                JsonInfolist::make('json'),
                               
                            ]),

 


                    ]),


                Section::make('Daftar Slide')
                    ->columnSpanFull()
                    ->extraAttributes(['class' => 'w-full', 'style' => 'width: 100%;'])
                    ->visible(fn($record) => $record->jenis === 'SLIDESHOW')
                    ->schema([
                        TextEntry::make('slideshow_list')
                            ->label('')
                            ->html()
                            ->columnSpanFull()
                            ->state(function ($record) {
                                // Ambil data slideshow menggunakan accessor
                                $slideshow = $record->slideshow;

                                // Jika tidak ada data slideshow, kembalikan pesan
                                if (empty($slideshow) || !is_array($slideshow)) {
                                    return '<div class="p-4 bg-gray-100 rounded-lg text-gray-600">Tidak ada slide yang tersedia.</div>';
                                }

                                // Buat tampilan slideshow
                                $html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">';
                                foreach ($slideshow as $index => $slide) {
                                    $html .= '<div class="bg-white rounded-lg shadow-md overflow-hidden">';

                                    // Tampilkan gambar slide jika ada
                                    if (!empty($slide['gambar'])) {
                                        $gambar = $slide['gambar'];

                                        // Jika gambar adalah array, ambil elemen pertama
                                        if (is_array($gambar)) {
                                            if (isset($gambar[0])) {
                                                $gambar = $gambar[0];
                                            } else {
                                                $gambar = reset($gambar);
                                            }
                                        }

                                        $imgUrl = asset('storage/' . $gambar);
                                        $html .= '<div class="relative h-48 overflow-hidden">';
                                        $html .= '<img src="' . $imgUrl . '" alt="' . htmlspecialchars($slide['judul'] ?? 'Slide ' . ($index + 1)) . '" class="w-full h-full object-cover">';
                                        $html .= '<div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">';
                                        $html .= '<span class="text-xs font-medium">Slide ' . ($index + 1) . '</span>';
                                        $html .= '</div>';
                                        $html .= '</div>';
                                    } else {
                                        // Tampilkan placeholder jika tidak ada gambar
                                        $html .= '<div class="bg-gray-200 h-48 flex items-center justify-center">';
                                        $html .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                                        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />';
                                        $html .= '</svg>';
                                        $html .= '</div>';
                                    }

                                    // Tampilkan informasi slide
                                    $html .= '<div class="p-4">';
                                    $html .= '<h3 class="text-lg font-semibold text-gray-800">' . htmlspecialchars($slide['judul'] ?? 'Tanpa Judul') . '</h3>';

                                    if (!empty($slide['isi'])) {
                                        $html .= '<div class="mt-2 text-sm text-gray-600 line-clamp-3">' . $slide['isi'] . '</div>';
                                    }

                                    $html .= '</div>';
                                    $html .= '</div>';
                                }
                                $html .= '</div>';

                                return $html;
                            }),
                    ]),


            ]);
    }
    // Menghapus footer widgets untuk menghindari masalah dengan Livewire
    protected function getFooterWidgets(): array
    {
        return [];
    }

    public function getTitle(): string
    {
        return $this->record->judul;
    }

    public function getSubHeading(): ?string
    {
        $jenis = match ($this->record->jenis) {
            'ARTIKEL' => 'Artikel',
            'HALAMAN' => 'Halaman',
            'SLIDESHOW' => 'Slideshow',
            'WIDGET' => 'Widget',
            default => $this->record->jenis,
        };

        return "{$jenis} - {$this->record->status}";
    }

    /**
     * Render slide item as HTML
     *
     * @param array $item
     * @return string
     */
    protected function renderSlideItem(array $item): string
    {
        $judul = $item['slideshow_judul'] ?? 'Tanpa Judul';
        $isi = $item['slideshow_isi'] ?? '';
        $gambar = $item['slideshow_gambar'] ?? '';

        $html = '<td style="vertical-align: top;"><div class="border rounded-lg p-3 bg-white shadow-sm h-full flex flex-col w-full">';
        $html .= '<h3 class="text-base font-bold mb-2">' . htmlspecialchars($judul) . '</h3>';

        if (!empty($gambar)) {
            // Jika gambar adalah array, ambil elemen pertama
            if (is_array($gambar)) {
                if (!empty($gambar)) {
                    $firstKey = array_key_first($gambar);
                    $gambar = $gambar[$firstKey] ?? '';
                } else {
                    $gambar = '';
                }
            }

            if (!empty($gambar) && is_string($gambar)) {
                $imgUrl = asset('storage/' . $gambar);
                $html .= '<div class="mb-2 flex-shrink-0"><img src="' . $imgUrl . '" alt="' . htmlspecialchars($judul) . '" class="rounded-lg w-full h-32 object-cover"></div>';
            }
        }

        if (!empty($isi)) {
            $html .= '<div class="prose flex-grow">' . $isi . '</div>';
        }

        $html .= '</div></td>';

        return $html;
    }
}

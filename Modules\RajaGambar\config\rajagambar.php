<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Image Quality
    |--------------------------------------------------------------------------
    |
    | Kualitas default untuk gambar yang diproses (1-100)
    |
    */
    'default_quality' => 85,

    /*
    |--------------------------------------------------------------------------
    | Default Image Format
    |--------------------------------------------------------------------------
    |
    | Format default untuk output gambar
    | Supported: 'jpg', 'jpeg', 'png', 'gif', 'webp'
    |
    */
    'default_format' => 'jpg',

    /*
    |--------------------------------------------------------------------------
    | Max Image Dimensions
    |--------------------------------------------------------------------------
    |
    | Dimensi maksimum yang diizinkan untuk pemrosesan gambar
    |
    */
    'max_width' => 4000,
    'max_height' => 4000,

    /*
    |--------------------------------------------------------------------------
    | Default Fit Method
    |--------------------------------------------------------------------------
    |
    | Method default untuk fit operation
    | Supported: 'contain', 'max', 'fill', 'stretch', 'crop'
    |
    */
    'default_fit_method' => 'contain',

    /*
    |--------------------------------------------------------------------------
    | Default Crop Method
    |--------------------------------------------------------------------------
    |
    | Method default untuk crop operation
    | Supported: 'crop-top-left', 'crop-top', 'crop-top-right', 'crop-left', 
    | 'crop-center', 'crop-right', 'crop-bottom-left', 'crop-bottom', 'crop-bottom-right'
    |
    */
    'default_crop_method' => 'crop-center',

    /*
    |--------------------------------------------------------------------------
    | Default Border Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan default untuk border
    |
    */
    'border' => [
        'width' => 1,
        'color' => '#000000',
        'type' => 'overlay', // overlay, shrink, expand
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Background Color
    |--------------------------------------------------------------------------
    |
    | Warna background default untuk gambar transparan
    |
    */
    'default_background' => '#ffffff',

    /*
    |--------------------------------------------------------------------------
    | Watermark Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan default untuk watermark
    |
    */
    'watermark' => [
        'opacity' => 50,
        'position' => 'bottom-right', // top-left, top-right, bottom-left, bottom-right, center
        'padding' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Text Overlay Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan default untuk text overlay
    |
    */
    'text' => [
        'font_size' => 16,
        'color' => '#000000',
        'position' => 'bottom-right',
        'padding' => 10,
        'font_path' => null, // Path ke font file jika diperlukan
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed File Extensions
    |--------------------------------------------------------------------------
    |
    | Ekstensi file yang diizinkan untuk diproses
    |
    */
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],

    /*
    |--------------------------------------------------------------------------
    | Storage Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan penyimpanan untuk gambar yang diproses
    |
    */
    'storage' => [
        'disk' => 'public',
        'path' => 'rajagambar',
        'temp_path' => 'rajagambar/temp',
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk API endpoints
    |
    */
    'api' => [
        'prefix' => 'api/rajagambar',
        'middleware' => ['api'],
        'rate_limit' => '60,1', // 60 requests per minute
    ],
];

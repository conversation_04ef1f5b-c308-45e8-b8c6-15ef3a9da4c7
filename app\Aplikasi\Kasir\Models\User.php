<?php

namespace App\Aplikasi\Kasir\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\User as UserUtama;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * @mixin IdeHelperUser
 */
class User extends UserUtama
{


  use HasApiTokens, HasFactory, Notifiable;

  /**
   * The attributes that are mass assignable.
   *
   * @var array<int, string>
   */
  protected $fillable = [
    'name',
    'email',
    'password',
    'pin',
    'username',
    'shift_id',
  ];

  public function penjualanShift(): HasMany
  {
    return $this->hasMany(PenjualanShift::class, 'karyawan_id');
  }

  public static function statusShift()
  {
    $user = self::find(auth()->id());
    if (! $user || ! $user->shift_id) {
      return null;
    }

    $shift = PenjualanShift::find($user->shift_id);
    return $shift ? $shift->status : null;
  }

  // Relasi ke PenjualanShift
  public function shift(): BelongsTo
  {
    return $this->belongsTo(PenjualanShift::class, 'shift_id');
  }
}

<?php

namespace App\Aplikasi\Hotel\Models\Seeders;

use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use App\Aplikasi\Hotel\Models\Tamu;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Models\Pembayaran;
use App\Aplikasi\Hotel\Models\Transaksi;
use App\Aplikasi\Hotel\Models\KamarTipe;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ReservasiSeeder extends Seeder
{
    /**
     * Menjalankan proses seeding database.
     * 
     * @return void
     */
    public function run(): void
    {
        // Mendapatkan jumlah, bulan, dan tahun dari environment variable
        $jumlah = env('SEEDER_COUNT', 10);
        $bulan = (int)env('SEEDER_MONTH', date('m'));
        $tahun = (int)env('SEEDER_YEAR', date('Y'));
        
        // Buat tanggal referensi dari bulan dan tahun yang dipilih
        $tanggalReferensi = Carbon::createFromDate($tahun, $bulan, 1);
        
        // Informasikan ke terminal tentang referensi waktu yang digunakan
        $this->command->info("🗓️ Menggunakan referensi waktu: {$tanggalReferensi->format('F Y')}");
        
        // Inisialisasi Faker dengan lokalisasi Indonesia
        $faker = Faker::create('id_ID');
        
        // Cek koneksi database sebelum mulai
        try {
            DB::connection()->getPdo();
            echo "Database terhubung dengan baik.\n";
        } catch (\Exception $e) {
            die("Database tidak terhubung: " . $e->getMessage() . "\n");
        }
        
        // Step 1-3: Persiapan data tamu, tipe kamar, dan kamar tetap sama

        // Step 4: Mulai membuat data reservasi
        $this->command->info("=============================================");
        $this->command->info("🔄 Memulai proses pembuatan {$jumlah} data Reservasi...");
        $progressBar = $this->command->getOutput()->createProgressBar($jumlah);
        $progressBar->start();
        
        for ($i = 0; $i < $jumlah; $i++) {
            // Menentukan tanggal acak dalam bulan terpilih
            $awalBulan = $tanggalReferensi->copy()->startOfMonth();
            $akhirBulan = $tanggalReferensi->copy()->endOfMonth();
            
            // Buat tanggal created_at, antara awal bulan hingga hari ini dalam bulan tersebut
            $tanggalCreated = Carbon::instance(
                $faker->dateTimeBetween(
                    $awalBulan->format('Y-m-d'), 
                    $akhirBulan->format('Y-m-d')
                )
            );
            
            // Buat tanggal check-in, mulai dari tanggal created
            // Asumsi: booking bisa dilakukan beberapa hari sebelum check-in
            $checkInCarbon = $tanggalCreated->copy()->addDays($faker->numberBetween(0, 5));
            
            // Jika tanggal check-in melebihi bulan terpilih, kembalikan ke akhir bulan
            if ($checkInCarbon->month != $bulan) {
                $checkInCarbon = $akhirBulan->copy()->subDays($faker->numberBetween(0, 3));
            }
            
            // Hitung durasi menginap (1-5 hari)
            $durasi = $faker->numberBetween(1, 5);
            $checkOutCarbon = $checkInCarbon->copy()->addDays($durasi);
            
            // Pilih kamar secara acak yang tersedia
            $kamarTerpilih = Kamar::all()->random();
            
            // Pilih status reservasi berdasarkan tanggal check-in dan check-out
            $now = Carbon::now();
            $statusReservasi = 'BK'; // Default status adalah Booking
            
            if ($checkOutCarbon->lt($now)) {
                // Jika check-out sudah lewat, maka sudah check-out
                $statusReservasi = 'SCO'; // Status Check-Out
            } elseif ($checkInCarbon->lt($now) && $checkOutCarbon->gt($now)) {
                // Jika saat ini di antara check-in dan check-out, maka sedang check-in
                $statusReservasi = 'SCI'; // Status Check-In
            } 
            
            // Buat instance Reservasi
            $reservasi = new Reservasi();
            $reservasi->toko_id = 1; 
            $reservasi->no_invoice = 'INV-' . $tanggalCreated->format('Ymd') . '-' . Str::upper(Str::random(5));
            $reservasi->tamu_id = Tamu::all()->random()->id;
            $reservasi->kamar_id = $kamarTerpilih->id;
            $reservasi->check_in = $checkInCarbon;
            $reservasi->check_out = $checkOutCarbon;
            $reservasi->referensi = $faker->randomElement(['LANGSUNG', 'ONLINE', 'TELEPON', 'AGEN']);
            $reservasi->ket_referensi = $faker->sentence();
            $reservasi->status_reservasi = $statusReservasi;
            $reservasi->diskon = $faker->randomElement([0, 5, 10, 15]);
            $reservasi->pajak = 10; // PPN 10%
            $reservasi->servis = 5; // Service charge 5%
            $reservasi->ket = $faker->paragraph();
            $reservasi->karyawan_id = 1;
            $reservasi->shift_id = $faker->randomElement([1, 2, 3]);
            
            // Nonaktifkan timestamps agar bisa set created_at manual
            $reservasi->timestamps = false;
            $reservasi->created_at = $tanggalCreated;
            $reservasi->updated_at = $tanggalCreated;
            $reservasi->save();
            
            // Step 5: Buat transaksi untuk kamar
            $hargaKamar = $kamarTerpilih->harga;
            $hargaModal = $kamarTerpilih->harga_modal;
            
            // Buat instance Transaksi untuk kamar
            $transaksiKamar = new Transaksi();
            $transaksiKamar->toko_id = 1;
            $transaksiKamar->jenis = 'KAMAR';
            $transaksiKamar->reservasi_id = $reservasi->id;
            $transaksiKamar->produk_id = $kamarTerpilih->id;
            $transaksiKamar->nama_item = 'Sewa Kamar ' . $kamarTerpilih->nama . ' (' . $durasi . ' hari)';
            $transaksiKamar->harga_modal = $hargaModal;
            $transaksiKamar->harga = $hargaKamar;
            $transaksiKamar->jumlah = $durasi;
            $transaksiKamar->ket = 'Periode: ' . $checkInCarbon->format('d/m/Y') . ' - ' . $checkOutCarbon->format('d/m/Y');
            
            // Set created_at sama dengan reservasi
            $transaksiKamar->timestamps = false;
            $transaksiKamar->created_at = $tanggalCreated;
            $transaksiKamar->updated_at = $tanggalCreated;
            $transaksiKamar->save();
            
            // Step 6: Tambahkan transaksi tambahan (layanan) dengan probabilitas 60%
            if ($faker->boolean(60)) {
                $layanan = ['Sarapan', 'Laundry', 'Spa', 'Airport Transfer', 'Tambahan Bed'];
                $layananTerpilih = $faker->randomElement($layanan);
                $hargaLayanan = $faker->numberBetween(100000, 500000);
                
                // Buat instance Transaksi untuk layanan tambahan
                $transaksiLayanan = new Transaksi();
                $transaksiLayanan->toko_id = 1;
                $transaksiLayanan->jenis = 'LAYANAN';
                $transaksiLayanan->reservasi_id = $reservasi->id;
                $transaksiLayanan->nama_item = $layananTerpilih;
                $transaksiLayanan->harga_modal = $hargaLayanan * 0.6; // Asumsi modal 60% dari harga
                $transaksiLayanan->harga = $hargaLayanan;
                $transaksiLayanan->jumlah = 1;
                $transaksiLayanan->ket = 'Layanan tambahan untuk ' . $reservasi->no_invoice;
                
                // Set created_at sama dengan reservasi
                $transaksiLayanan->timestamps = false;
                $transaksiLayanan->created_at = $tanggalCreated;
                $transaksiLayanan->updated_at = $tanggalCreated;
                $transaksiLayanan->save();
            }
            
            // Step 7-8: Hitung total dan buat pembayaran
            $totalTransaksi = DB::table('transaksi')
                ->where('reservasi_id', $reservasi->id)
                ->selectRaw('SUM(harga * jumlah) as total')
                ->first()
                ->total ?? 0;
            
            // Terapkan diskon, pajak, dan servis
            $setelahDiskon = $totalTransaksi * (1 - ($reservasi->diskon / 100));
            $setelahPajak = $setelahDiskon * (1 + ($reservasi->pajak / 100));
            $totalAkhir = $setelahPajak * (1 + ($reservasi->servis / 100));
            
            // Atur status pembayaran berdasarkan status reservasi
            $statusPembayaran = 'pending';
            
            if ($statusReservasi == 'SCO' && $faker->boolean(90)) {
                $statusPembayaran = 'berhasil';
            } elseif ($statusReservasi == 'SCI' && $faker->boolean(70)) {
                $statusPembayaran = 'berhasil';
            } elseif ($statusReservasi == 'BK' && $faker->boolean(30)) {
                $statusPembayaran = 'berhasil';
            }
            
            // Buat instance Pembayaran
            $pembayaran = new Pembayaran();
            $pembayaran->nama = 'Pembayaran ' . $reservasi->no_invoice;
            $pembayaran->penjualan_id = null;
            $pembayaran->reservasi_id = $reservasi->id;
            $pembayaran->metode_pembayaran_id = $faker->numberBetween(1, 3);
            $pembayaran->jumlah = $totalAkhir;
            $pembayaran->status = $statusPembayaran;
            $pembayaran->pengirim = [
                'nama' => Tamu::find($reservasi->tamu_id)->nama,
                'rekening' => $faker->creditCardNumber,
                'bank' => $faker->randomElement(['BCA', 'BNI', 'Mandiri', 'BRI'])
            ];
            $pembayaran->tujuan = 'Rekening Hotel';
            $pembayaran->ket = 'Pembayaran untuk reservasi ' . $reservasi->no_invoice;
            
            // Set created_at sama dengan reservasi
            $pembayaran->timestamps = false;
            $pembayaran->created_at = $tanggalCreated;
            $pembayaran->updated_at = $tanggalCreated;
            $pembayaran->save();
            
            // Update progress bar
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->command->newLine(2);
        
        // Statistik akhir
        $this->command->info("=============================================");
        $this->command->info("✅ SELESAI: Berhasil membuat {$jumlah} data Reservasi untuk {$tanggalReferensi->format('F Y')}");
        $this->command->info("=============================================");
    }
}
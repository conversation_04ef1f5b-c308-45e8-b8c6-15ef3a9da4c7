<?php

namespace App\Aplikasi\Hotel\Forms\Components;

use App\Aplikasi\Hotel\Models\Kamar;
use App\Filament\Forms\Components\Rupiah;
use App\Models\HargaJual;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;

class PilihHarga
{
    public static function make(string $name): Select
    {
        return Select::make($name)
            ->label('Harga khusus')
            ->options(function (Get $get) {
                $kamarId = $get('kamar_id');
                if (!$kamarId) {
                    return [];
                }

                // Ambil data kamar
                $kamar = Kamar::find($kamarId);
                if (!$kamar) {
                    return [];
                }
                // Buat opsi untuk harga standar/asli
                $options = [
                    'original_' . $kamarId => 'Harga Standar - Rp ' . number_format($kamar->harga, 0, ',', '.')
                ];
                // Ambil data harga khusus dari relasi
                $hargaJualOptions = HargaJual::where('produk_id', $kamarId)
                    ->orderBy('harga')
                    ->get()
                    ->mapWithKeys(function ($harga) {
                        return [
                            $harga->id => $harga->nama . ' - Rp ' . number_format($harga->harga, 0, ',', '.')
                        ];
                    })
                    ->toArray();

                // Gabungkan opsi harga standar dan khusus
                return $options + $hargaJualOptions;
            })
            ->createOptionForm([
                TextInput::make('nama')
                    ->label('Nama Harga')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Contoh: Promo Spesial, Diskon Member'),
                Rupiah::make('harga')
                    ->label('Harga')
                    ->required()
      
            ])
            ->createOptionUsing(function (array $data, Get $get, $livewire) {
                // Ambil kamar_id dari form utama
                $kamarId = $get('kamar_id');
                if (!$kamarId) {
                    throw new \Exception('ID Kamar tidak tersedia');
                }
                // Konversi format harga dari form ke integer
                $harga = (int) str_replace(['Rp', '.', ','], '', $data['harga']);
                // Buat record harga baru
                $hargaJual = HargaJual::create([
                    'nama' => $data['nama'],
                    'harga' => $harga,
                    'produk_id' => $kamarId,
                ]);
                return $hargaJual->id;
            })
            ->default(function (Get $get) {
                $kamarId = $get('kamar_id');
                if ($kamarId) {
                    return 'original_' . $kamarId;
                }
                return null;
            })
            ->live()
            ->afterStateUpdated(function ($state, Get $get, Set $set, $livewire) {
                if (!$state || !$livewire || !method_exists($livewire, 'updateHargaKamar')) {
                    return;
                }

                $kamarId = $get('kamar_id');
                if (!$kamarId) {
                    return;
                }

                try {
                    // Cek apakah harga asli atau harga khusus
                    if (is_string($state) && strpos($state, 'original_') === 0) {
                        // Ambil ID kamar dari nilai
                        $kamarIdFromValue = (int) str_replace('original_', '', $state);
                        $kamar = Kamar::find($kamarIdFromValue);

                        if ($kamar) {
                            $livewire->updateHargaKamar($kamarId, $kamar->harga);
                        }
                    } else {
                        // Ambil data harga dari database (harga khusus)
                        $hargaJual = HargaJual::find($state);

                        if ($hargaJual) {
                            // Perbarui keranjang dengan harga khusus
                            $livewire->updateHargaKamar($kamarId, $hargaJual->harga);
                        }
                    }
                } catch (\Exception $e) {
                    // Catat kesalahan tanpa mengganggu UI
                }
            })
            ->placeholder('--- Pilihan harga ---')
            ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Pilih, hanya jika ingin merubah harga kamar')
            ->disabled(fn(Get $get): bool => !$get('kamar_id'))
            ->createOptionModalHeading('Tambah Harga Baru');
    }
}
<?php
namespace Modules\Rajapicker\Models;

use Illuminate\Database\Eloquent\Model;
use Mo<PERSON>les\Rajapicker\Models\Media;
use Spatie\MediaLibrary\MediaCollections\Models\Media as ModelsMedia;


class MediaMember extends Media
{

 


     protected static function booted()
    {
        parent::booted();

        // Filter hanya media milik user yang sedang login
        static::addGlobalScope('user_media', function ($builder) {
            if (\Illuminate\Support\Facades\Auth::check()) {
                $builder->where('user_id', \Illuminate\Support\Facades\Auth::id());
            }
        });

        static::creating(function ($model) {
            $model->user_id = \Illuminate\Support\Facades\Auth::id();
        });

        static::updating(function ($model) {
            $model->user_id = \Illuminate\Support\Facades\Auth::id();
        });
    }

    
}
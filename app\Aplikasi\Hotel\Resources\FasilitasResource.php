<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel\Models\Fasilitas;
use App\Aplikasi\Hotel\Resources\FasilitasResource\Pages;
use App\Filament\Forms\Components\Rupiah;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FasilitasResource extends Resource
{
    protected static ?string $model = Fasilitas::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $slug = 'pengaturan/fasilitas';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\TextInput::make('nama')
                    ->required()
                    ->maxLength(255)
                    ->label('Nama '),
                Forms\Components\TextInput::make('stok')
                    ->numeric()
                    ->default(1)
                    ->label('Jumlah stok'),
                Rupiah::make('harga_modal')
                    ->numeric()
                    ->label('Harga Modal'),
                Rupiah::make('harga')
                    ->required()
                    ->numeric()
                    ->label('Harga jual'),


                // Forms\Components\FileUpload::make('gambar')
                //     ->image()
                //     ->directory('kamar')
                //     ->label('Gambar Kamar'),


                Forms\Components\Toggle::make('tampil')
                    ->default(true)
                    ->label('aktif')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            ->columns([

                Tables\Columns\TextColumn::make('nama')
                    ->searchable()
                    ->sortable()
                    ->label('Nama Kamar'),
                Tables\Columns\TextColumn::make('harga_modal')
                    ->numeric(locale: 'id')

                    ->sortable()
                    ->label('Harga modal'),
                Tables\Columns\TextColumn::make('harga')
                    ->numeric(locale: 'id')
                    ->sortable()
                    ->label('Harga jual'),
                // Tables\Columns\ImageColumn::make('gambar')
                //     ->label('Gambar'),
                TextColumn::make('stok'),
                Tables\Columns\ToggleColumn::make('tampil')
                    ->label('aktif'),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageFasilitas::route('/'),
        ];
    }
}

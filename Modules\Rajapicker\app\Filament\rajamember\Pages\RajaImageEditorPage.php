<?php

namespace Modules\Rajapicker\Filament\rajamember\Pages;

use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Mo<PERSON>les\Rajapicker\Models\MediaMember as Media;
use Modules\Rajapicker\Models\RajaGaleri;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class RajaImageEditorPage extends Page
{
    public static string $view = 'rajapicker::filament.pages.raja-image-editor';
    public static ?string $navigationLabel = 'Raja Image Editor';
    public static ?string $slug = 'raja-image-editor';  

    protected static bool $shouldRegisterNavigation = false;
    public static ?string $navigationIcon = 'heroicon-o-photo';

    public ?int $selectedMediaId = null;
    public ?array $selectedMedia = null;
    public ?string $selectedCollection = 'all';
    public bool $isLoading = false;

    

    public function getTitle(): string
    {
        return __('Raja Image Editor');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Media Management');
    }

    public function mount(): void
    {
        // Cek apakah ada parameter media ID dari URL
        $mediaId = request()->get('media');

        if ($mediaId && is_numeric($mediaId)) {
            $this->loadMediaById((int) $mediaId);
        } else {
            $this->loadInitialData();
        }
    }

    protected function loadInitialData(): void
    {
        // Load initial media if needed
        if ($this->selectedMediaId) {
            $this->loadMediaById($this->selectedMediaId);
        }
    }

    public function loadMediaById(int $mediaId): void
    {
        try {
            $media = Media::find($mediaId);
            if ($media && $media->isImage()) {
                $this->selectedMedia = [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'url' => $this->getRelativeUrl($media),
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'collection_name' => $media->collection_name,
                    'dimensions' => $this->getImageDimensions($media),
                ];
                $this->selectedMediaId = $mediaId;
            }
        } catch (\Exception $e) {
            Log::error('Error loading media: ' . $e->getMessage());
            Notification::make()
                ->title('Error')
                ->body('Gagal memuat media')
                ->danger()
                ->send();
        }
    }

    public function saveEditedImage(array $imageData): void
    {
        try {
            $this->isLoading = true;

            if (!$this->selectedMedia) {
                throw new \Exception('Tidak ada media yang dipilih');
            }

            // Decode base64 image data
            $imageContent = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $imageData['dataURL']));

            // Generate filename
            $originalMedia = Media::find($this->selectedMedia['id']);
            $pathInfo = pathinfo($originalMedia->file_name);
            $newFileName = $pathInfo['filename'] .  time() . '.' . $pathInfo['extension'];

            // Create new RajaGaleri instance
            $rajaGaleri = new RajaGaleri();
            $rajaGaleri->save();

            // Create temporary file
            $tempPath = storage_path('app/temp/' . $newFileName);
            if (!file_exists(dirname($tempPath))) {
                mkdir(dirname($tempPath), 0755, true);
            }
            file_put_contents($tempPath, $imageContent);

            // Add media to collection
            $media = $rajaGaleri
                ->addMedia($tempPath)
                ->usingName($originalMedia->name . ' (Edited)')
                ->usingFileName($newFileName)
                ->toMediaCollection($originalMedia->collection_name);

            // Clean up temp file
            unlink($tempPath);

            // Update selected media
            $this->loadMediaById($media->id);

            Notification::make()
                ->title('Berhasil')
                ->body('Gambar berhasil disimpan')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Log::error('Error saving edited image: ' . $e->getMessage());
            Notification::make()
                ->title('Error')
                ->body('Gagal menyimpan gambar: ' . $e->getMessage())
                ->danger()
                ->send();
        } finally {
            $this->isLoading = false;
        }
    }

    protected function getRelativeUrl($media): string
    {
        $path = $media->collection_name . '/' . $media->file_name;
        $prefix = config('media-library.prefix', 'uploads');
        return '/storage/' . $prefix . '/' . $path;
    }

    protected function getImageDimensions($media): ?array
    {
        try {
            $fullPath = storage_path('app/public/' . config('media-library.prefix', 'uploads') . '/' . $media->collection_name . '/' . $media->file_name);
            if (file_exists($fullPath)) {
                $imageInfo = getimagesize($fullPath);
                return [
                    'width' => $imageInfo[0] ?? null,
                    'height' => $imageInfo[1] ?? null,
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Could not get image dimensions: ' . $e->getMessage());
        }
        return null;
    }

    protected function getHeaderActions(): array
    {
        $actions = [
            Action::make('back')
                ->label('Kembali')
                ->icon('heroicon-o-arrow-left')
                ->url(back()->getTargetUrl())
                ->color('gray')
        ];

        // Jika bukan super_admin, tidak tampilkan action Pilih Media
        if (!$this->isSuperAdmin()) {
            return $actions;
        }

        $actions[] = Action::make('selectMedia')
            ->label('Pilih Media')
            ->icon('heroicon-o-photo')
            ->form([
                Grid::make()
                    ->schema([
                        Section::make('Pilih Gambar untuk Diedit')
                            ->schema([
                                Select::make('media_id')
                                    ->label('Pilih Media')
                                    ->options(function () {
                                        return Media::where('mime_type', 'LIKE', 'image/%')
                                            ->where('collection_name', '!=', 'conversion')
                                            ->orderBy('created_at', 'desc')
                                            ->limit(100)
                                            ->get()
                                            ->mapWithKeys(function ($media) {
                                                return [$media->id => $media->name . ' (' . $media->collection_name . ')'];
                                            });
                                    })
                                    ->searchable()
                                    ->required()
                                    ->helperText('Pilih gambar yang ingin diedit'),
                            ])
                    ])
            ])
            ->action(function (array $data) {
                $this->loadMediaById($data['media_id']);
            })
            ->modalWidth('lg');

        return $actions;
    }

    public function notify(array $notification): void
    {
        $type = $notification['type'] ?? 'info';
        $title = $notification['title'] ?? '';
        $body = $notification['body'] ?? '';

        $notificationInstance = Notification::make()
            ->title($title)
            ->body($body);

        switch ($type) {
            case 'success':
                $notificationInstance->success();
                break;
            case 'danger':
            case 'error':
                $notificationInstance->danger();
                break;
            case 'warning':
                $notificationInstance->warning();
                break;
            default:
                $notificationInstance->info();
                break;
        }

        $notificationInstance->send();
    }

    public function getAssetUrls(): array
    {
        $manifestPath = public_path('build-rajapicker/manifest.json');

        if (file_exists($manifestPath)) {
            $manifest = json_decode(file_get_contents($manifestPath), true);

            return [
                'js' => asset('build-rajapicker/' . $manifest['resources/assets/js/image-editor.js']['file']),
                'css' => [
                    asset('build-rajapicker/' . $manifest['resources/assets/sass/image-editor.scss']['file']),
                    asset('build-rajapicker/' . $manifest['resources/assets/js/image-editor.css']['file'])
                ]
            ];
        }

        // Fallback URLs
        return [
            'js' => asset('build-rajapicker/assets/image-editor-cffbbb1b.js'),
            'css' => [
                asset('build-rajapicker/assets/image-editor-ec0c1278.css'),
                asset('build-rajapicker/assets/image-editor-7fc992e4.css')
            ]
        ];
    }

    /**
     * Cek apakah user saat ini memiliki role super_admin
     */
    public function isSuperAdmin(): bool
    {
        return function_exists('sayaSuperAdmin') ? sayaSuperAdmin() : false;
    }

    /**
     * Data yang akan dikirim ke view
     */
    public function getViewData(): array
    {
        return [
            'isSuperAdmin' => $this->isSuperAdmin(),
        ];
    }
}

<?php

namespace App\Filament\Resources;

// Resource ini digunakan untuk mengelola permission Spatie secara langsung
// di panel Filament.
// Semua komentar menggunakan Bahasa Indonesia sesuai preferensi pengguna.

use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Spatie\Permission\Models\Permission;
use Filament\Tables\Grouping\Group;
use Illuminate\Support\Str;

class PermissionResource extends Resource
{
    // Model yang digunakan
    protected static ?string $model = Permission::class;

    /* -------------------------------------------------------------- */
    /*  Konfigurasi Navigasi                                          */
    /* -------------------------------------------------------------- */

    protected static ?string $navigationIcon  = 'heroicon-o-lock-closed';
    protected static ?string $navigationLabel = 'Permissions';
    protected static ?string $navigationGroup = 'System';
    protected static ?int    $navigationSort  = 101;

    public static function form(Form $form): Form
    {
        $guardOptions = collect(config('auth.guards'))
            ->keys()
            ->mapWithKeys(fn ($key) => [$key => $key])
            ->toArray();

        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Nama Permission')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),

                Select::make('guard_name')
                    ->label('Guard')
                    ->options($guardOptions)
                    ->default('web')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    { 
        return $table
            ->recordUrl(false)
            ->columns([
                TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->recordClasses(['text-sm', 'py-0.5'])
            ->groups([
                Group::make('name')
                    ->label('Model')
                    ->getKeyFromRecordUsing(function (Permission $record): string {
                        $name = Str::afterLast($record->name, '_');
                        $name = Str::afterLast($name, '::');
                        return $name;
                    })
                    ->getTitleFromRecordUsing(function (Permission $record): string {
                        $name = Str::afterLast($record->name, '_');
                        $name = Str::afterLast($name, '::');
                        return Str::of($name)->headline();
                    })
                    ->titlePrefixedWithLabel(false)
                    ->collapsible()
                    ->orderQueryUsing(function ($query, string $direction) {
                        // Urutkan berdasarkan kata setelah underscore terakhir untuk memastikan grup berdekatan
                        return $query->orderByRaw("SUBSTRING_INDEX(name, '_', -1) $direction")
                                    ->orderBy('name', $direction);
                    }),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => PermissionResource\Pages\ListPermissions::route('/'),
            'create' => PermissionResource\Pages\CreatePermission::route('/create'),
            'edit' => PermissionResource\Pages\EditPermission::route('/{record}/edit'),
        ];
    }
} 
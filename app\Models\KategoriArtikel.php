<?php

namespace App\Models;

use App\Models\Kategori;
use Illuminate\Database\Eloquent\Factories\HasFactory;
 

/**
 * @mixin IdeHelperKategoriArtikel
 */
class KategoriArtikel extends Kategor<PERSON>
{
    use HasFactory;

    protected static function booted()
    {
        static::addGlobalScope('toko_id', function (\Illuminate\Database\Eloquent\Builder $builder) {

            $builder->where('jenis', 'ARTIKEL');
        

        });

        static::creating(function ($model) {
            $model->jenis = 'ARTIKEL';
        });

        static::updating(function ($model) {
            $model->jenis = 'ARTIKEL';
        });
    }
  
    
}

<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Pages;

use Mo<PERSON>les\Rajapicker\Filament\admin\Resources\MediaResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMedia extends EditRecord
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat'),
                
            Actions\DeleteAction::make()
                ->label('Hapus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Media: ' . $this->record->name;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Media berhasil diupdate';
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Isi form dengan data custom properties
        $customProperties = $this->record->custom_properties ?? [];

        $data['custom_properties'] = [
            'alt' => $customProperties['alt'] ?? '',
            'caption' => $customProperties['caption'] ?? '',
            'description' => $customProperties['description'] ?? '',
        ];

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Update custom properties jika ada
        if (isset($data['custom_properties'])) {
            $existingProperties = $this->record->custom_properties ?? [];
            $newProperties = array_filter($data['custom_properties'] ?? []);

            $this->record->custom_properties = array_merge($existingProperties, $newProperties);
            $this->record->save();

            // Remove custom_properties dari data yang akan disave ke fields lain
            unset($data['custom_properties']);
        }

        return $data;
    }
}

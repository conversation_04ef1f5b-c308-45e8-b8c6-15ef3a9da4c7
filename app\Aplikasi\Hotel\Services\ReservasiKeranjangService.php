<?php

namespace App\Aplikasi\Hotel\Services;

use App\Aplikasi\Hotel\Models\Fasilitas;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Services\Keranjang;
use Carbon\Carbon;

class ReservasiKeranjangService
{
    protected Keranjang $keranjang;
    
    public function __construct(Keranjang $keranjang)
    {
        $this->keranjang = $keranjang;
    }
    
  /**
 * Update harga item kamar di keranjang
 *
 * @param int $kamarId
 * @param int $newHarga
 * @return bool
 */
public function updateHargaKamar(int $kamarId, int $newHarga): bool
{
    try {
        $items = $this->keranjang->items();
        
        // Cari item kamar di keranjang
        $updated = false;
        $items = $items->map(function ($item) use ($kamarId, $newHarga, &$updated) {
            if ($item['jenis'] === 'kamar' && $item['kamar_id'] == $kamarId) {
                $item['harga'] = $newHarga;
                $updated = true;
            }
            return $item;
        });
        
        if ($updated) {
            $this->keranjang->updateItems($items);
            return true;
        }
        
        return false;
    } catch (\Exception $e) {
        report($e);
        return false;
    }
}

    public function tambahKamar(int $kamarId, string $checkIn, string $checkOut): bool
{
    try {
        $checkInDate = Carbon::parse($checkIn);
        $checkOutDate = Carbon::parse($checkOut);
        $durasi = max(1, $checkOutDate->diffInDays($checkInDate));
        
        // Validasi keberadaan kamar
        $kamar = Kamar::find($kamarId);
        if (!$kamar) {
            return false;
        }
        
        // Periksa ketersediaan kamar
        if (!$kamar->isAvailable($checkInDate, $checkOutDate)) {
            return false;
        }
        
        // Hapus semua item berkategori jasa (kamar) yang sudah ada
        $existingItems = $this->keranjang->items();
        $existingItems = $existingItems->reject(function ($item) {
            return $item['jenis'] === 'kamar';
        });
        $this->keranjang->updateItems($existingItems);
        
        // Ambil informasi tipe kamar
        $tipeKamar = KamarTipe::find($kamar->kategori_id);
        $namaItem = $tipeKamar ? "{$tipeKamar->nama} - {$kamar->nama}" : $kamar->nama;
        
        // Buat ID unik untuk item keranjang
        $uniqueId = $kamarId . '_' . $checkInDate->timestamp . '_' . $checkOutDate->timestamp;
        
        // Siapkan detail item untuk ditambahkan ke keranjang
        $item = [
            'id' => crc32($uniqueId),
            'jenis' => 'kamar',
            'produk_id' => $kamar->id,
            'nama_item' => $namaItem,
            'harga_modal' => $kamar->harga_modal ?: 0,
            'harga' => $kamar->harga,
            'jumlah' => $durasi,
            'ket' => "Check-in: {$checkInDate->format('d/m/Y')} - Check-out: {$checkOutDate->format('d/m/Y')}",
            'check_in' => $checkIn,
            'check_out' => $checkOut,
            'kamar_id' => $kamarId,
            'tipe_kamar' => $tipeKamar ? $tipeKamar->nama : null
        ];
        
        $this->keranjang->tambahItem($item);
        return true;
    } catch (\Exception $e) {
        report($e);
        return false;
    }
}
 
        public function tambahFasilitas($produkId)
    {
        $produk = Fasilitas::find($produkId);
        if (!$produk) {
            return false;
        }
        
        $item = [
            'id' => $produk->id,
            'jenis' => 'fasilitas',
            'produk_id' => $produk->id,
            'nama_item' => $produk->nama,
            'harga_modal' => $produk->harga_modal,
            'harga' => $produk->harga,
            'jumlah' => 1,
            'ket' => $produk->ket ?? '-',
            'barcode' => $produk->barcode ?? '',
        ];
        
        $this->keranjang->tambahItem($item);
        return true;
    }

 
    public function getItems()
    {
        return $this->keranjang->items();
    }
    
    /**
     * Mendapatkan total lama menginap di keranjang
     *
     * @return int
     */
    public function getTotalDurasi(): int
    {
        return $this->keranjang->items()->sum('jumlah');
    }
    
    /**
     * Mendapatkan total harga di keranjang
     *
     * @return int
     */
    public function getTotalHarga(): int
    {
        return $this->keranjang->totalHarga();
    }
    
    /**
     * Menghapus item dari keranjang
     *
     * @param int $itemId
     * @return void
     */
    public function hapusItem(int $itemId): void
    {
        $this->keranjang->hapusItem($itemId);
    }
    
    /**
     * Mengosongkan keranjang
     *
     * @return void
     */
    public function kosongkan(): void
    {
        $this->keranjang->kosongkanKeranjang();
    }
}
<?php

namespace App\Filament\Resources;


use App\Filament\Forms\Components\FieldBuilder6;
use App\Filament\Forms\Components\FieldBuilder;
use App\Filament\Resources\MetodePembayaranUtamaResource\Pages;
use App\Filament\Resources\MetodePembayaranUtamaResource\RelationManagers;
use App\Models\Konfig;
use App\Models\MetodePembayaranUtama as MetodePembayaran;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;
use Str;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

class MetodePembayaranUtamaResource extends Resource
{
    protected static ?string $model = MetodePembayaran::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static bool $shouldRegisterNavigation = true; // Aktifkan navigation
    protected static ?string $navigationLabel = 'Metode Pembayaran';
    protected static ?string $navigationGroup = 'Pengaturan';
    // protected static ?string $navigationParentItem = 'Data Management'; // Parent item
    protected static ?string $slug = 'pengaturan/metodepembayaran';
    protected static ?string $pluralModelLabel = 'Semua Metode pembayaran';

    public static function form(Form $form): Form
    {
        $contoh_custom_value = '[
                                    {   "type": "textinput", "name": "contoh_nama", "tooltip": "contoh tooltips"  },
                                    { "type": "textinput","name": "contoh_dua"  },
                                     { "type": "select",   "name": "contoh_select",   "options" : "bca,bni,mandiri"  }
                                            ]';

        $apiDefault = '[ { "client_key":"", "server_key":"", "is_production":false,"merchant_id":"" }]';
        return $form
            ->schema([
                Grid::make(4)->schema([

                    Select::make('aplikasi')
                        ->options(
                            bacaSemuaAplikasi()
                                ->where('status', 'aktif')
                                ->mapWithKeys(function ($item) {
                                    return [strtoupper($item['id']) => strtoupper($item['nama'])];
                                })
                                ->prepend('GLOBAL', 'GLOBAL') // Menambahkan opsi SEMUA dengan nilai kosong
                                ->toArray()
                        )
                        ->label('Aplikasi')
                        ->placeholder('Pilih aplikasi')
                        ->required()
                        ->columnSpan(1),

                    TextInput::make('nama')
                        ->required()
                        ->placeholder('Masukkan nama metode pembayaran')
                        ->columnSpan(1),

                    Select::make('jenis')
                        ->options(Konfig::jsonKu('jenis_metode_pembayaran')->pluck('value', 'key'))
                        ->label('Jenis metode pembayaran')
                        ->placeholder('Pilih jenis')
                        ->required()
                        ->live()
                        ->columnSpan(1),






                    // FileUpload::make('gambar')
                    //     ->label('Logo/Gambar')
                    //     ->directory('metode-pembayaran')
                    //     ->image()
                    //     ->imageResizeMode('cover')
                    //     ->imageCropAspectRatio('1:1')
                    //     ->imageResizeTargetWidth('200')
                    //     ->imageResizeTargetHeight('200')

                    //     ->columnSpan(1),

                    RichEditor::make('info_tujuan')
                        ->label('Informasi rekening tujuan')
                        ->placeholder('Masukkan informasi rekening tujuan, seperti nomor rekening dan nama pemilik')
                        ->visible(fn(Get $get): bool => in_array(
                            $get('jenis'),
                            ['transfer', 'qris']
                        ))
                        ->columnSpan(1),

                    Section::make('informasi pengirim')
                        ->description('klik tombol buka widget rajadesain, untuk membuat form, kemudian copy kodenya, dan paste disini')
                        ->schema([JsonColumn::make('info_pengirim')
                            ->label(false)
                            ->editorHeight(500)
                            ->viewerHeight(300)
                            ->editorOnly()
                            ->default(fn(Get $get) => $get('jenis') == 'api' ? $apiDefault : $contoh_custom_value)

                           
                            ->reactive(),])
                            ->columnSpan(2) 
                            ->visible(fn(Get $get): bool => in_array(
                                $get('jenis'),
                                ['transfer', 'kartu', 'api']
                            )),

                            Section::make('preview form')
                            ->description('preview fields, hanya tampil saat edit, simpan terlebih dahulu untuk melihat preview')
                            ->schema(function (Get $get) {
                                // Ini akan mengevaluasi closure terlebih dahulu
                                return FieldBuilder::buatFields(
                                    $get('info_pengirim') ?? [],
                                    'info_pengirim_preview'
                                );
                            })
                            ->columnSpan(2) ->visible(fn(Get $get): bool => in_array(
                                $get('jenis'),
                                ['transfer', 'kartu', 'api']
                            ))
                             ,
 

                ]),


                Toggle::make('status')
                    ->label('Status aktif')
                    ->default(true)

                    ->onColor('success')
                    ->offColor('danger')
                    ->onIcon('heroicon-o-check')
                    ->offIcon('heroicon-o-x-mark')
                    ->helperText('Aktifkan/nonaktifkan metode pembayaran'),

                Toggle::make('print')
                    ->label('Tampilkan di struk')
                    ->default(true)
                    ->onColor('success')
                    ->offColor('danger')
                    ->onIcon('heroicon-o-check')
                    ->offIcon('heroicon-o-x-mark')
                    ->helperText('Tampilkan info metode ini di struk pembayaran'),


                // Textarea::make('ket')
                //     ->label('Keterangan tambahan')
                //     ->placeholder('Masukkan keterangan tambahan jika diperlukan')
                //     ->rows(3)
                //     ->columnSpan('full'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('aplikasi')
                    ->label('aplikasi')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('jenis')
                    ->label('Jenis')
                    ->sortable()
                    ->searchable(),

                // TextColumn::make('toko_id')
                //     ->label('ID Toko')
                //     ->numeric()
                //     ->sortable()
                //     ->formatStateUsing(fn(mixed $state): string => $state ? "Toko #$state" : 'Semua Toko')
                //     ->toggleable(),

                // ImageColumn::make('gambar')
                //     ->label('Logo')
                //     ->circular()
                //     ->toggleable(),

                ToggleColumn::make('status')
                    ->label('Status')
                    ->sortable()
                    ->alignCenter(),

                ToggleColumn::make('print')
                    ->label('Struk')
                    ->sortable()
                    ->alignCenter()
                    ->toggleable(),

                TextColumn::make('created_at')
                    ->label('Dibuat pada')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Diperbarui pada')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis')
                    ->options(Konfig::jsonKu('jenis_metode_pembayaran')->pluck('value', 'key'))
                    ->label('Filter berdasarkan jenis'),

                Tables\Filters\TernaryFilter::make('status')
                    ->label('Status aktif')
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('aktifkan')
                        ->label('Aktifkan Terpilih')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->status = true;
                                $record->save();
                            });
                        }),

                    Tables\Actions\BulkAction::make('nonaktifkan')
                        ->label('Nonaktifkan Terpilih')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->status = false;
                                $record->save();
                            });
                        }),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetodePembayaran::route('/'),
            'create' => Pages\CreateMetodePembayaran::route('/create'),
            // 'view' => Pages\ViewMetodePembayaran::route('/{record}'),
            'edit' => Pages\EditMetodePembayaran::route('/{record}/edit'),
        ];
    }


    public static function canBeSharedInMultiplePanels(): bool
    {
        return true; // Mengizinkan resource ini digunakan di banyak panel
    }

    public static function scopeEloquentQueryToPanelScope(string $panel, Builder $query): Builder
    {
        // Jika di panel kasir, filter hanya data dengan jenis KASIR
        if ($panel === 'hotel') {
            return $query->where('aplikasi', 'HOTEL');
        }

        // Untuk panel utama atau lainnya, tidak perlu filter khusus
        return $query;
    }

    // Method untuk menyesuaikan data sebelum disimpan berdasarkan panel
    public static function mutateDataBeforeCreateToPanelContext(string $panel, array $data): array
    {
        if ($panel === 'hotel') {
            $data['aplikasi'] = 'HOTEL';
        }

        return $data;
    }
}

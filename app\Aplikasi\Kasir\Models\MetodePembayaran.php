<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * @mixin IdeHelperMetodePembayaran
 */
class MetodePembayaran extends Model
{
    use HasFactory;

    // Nama tabel di database
    protected $table = 'metode_pembayaran';

    // Field yang diizinkan untuk pengisian massal
    protected $fillable = [
        'id',
        'toko_id',
        'nama',
        'jenis',
        'gambar',
        'info_tujuan',
        'info_pengirim',
        'status',
        'print',
        'ket',
    ];

    // Konversi otomatis antara JSON dan array PHP
    protected $casts = [
        // 'info_pengirim' => 'json',
        'status' => 'boolean',
        'print' => 'boolean',
    ];

    public function penjualans()
    {
        return $this->hasMany(Penjualan::class);
    }

    public function penjualan()
    {
        return $this->hasBelongsTo(Penjualan::class);
    }

    public static function getActive()
    {
        return self::where('status', true)->get();
    }

    public function hasValidInfoPengirim(): bool
    {
        // Cek nilai info_pengirim
        if ($this->info_pengirim === null || $this->info_pengirim === '' ||
            $this->info_pengirim === '[]' || $this->info_pengirim === '{}') {
            return false;
        }

        try {
            // Parse JSON jika string
            $data = is_string($this->info_pengirim)
                ? json_decode($this->info_pengirim, true)
                : $this->info_pengirim;

            // Cek field
            if (is_array($data)) {
                foreach ($data as $field) {
                    if (isset($field['type']) && isset($field['name'])) {
                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

      public function isActive(): bool
    {
        return (bool) $this->status;
    }

    // Method untuk mendapatkan field info pengirim sebagai array
    public function getInfoPengirimArray(): array
    {
        if (empty($this->info_pengirim)) {
            return [];
        }

        $data = json_decode($this->info_pengirim, true);
        return is_array($data) ? $data : [];
    }

    // Method untuk memeriksa apakah memerlukan input pelanggan
    public function membutuhkanInfoPengirim(): bool
    {
        return ! empty($this->getInfoPengirimArray());
    }

}
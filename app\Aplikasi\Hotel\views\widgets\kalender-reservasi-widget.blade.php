<div class="p-2 sm:p-4 bg-white rounded-lg shadow max-w-full">
    <div class="space-y-4 w-full">
        <!-- Head<PERSON> -->
        <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 mb-2">
            <!-- Tombol Navigasi Bulan -->
            <div class="flex space-x-2 w-full md:w-auto justify-center md:justify-start">
                <button wire:click="bulanSebelumnya" class="p-2 rounded hover:bg-gray-200 transition text-xs">
                    <x-heroicon-o-arrow-left class="h-5 w-5" /> Bulan sebelumnya
                </button>
                <button wire:click="aturHariIni"
                    class="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition">
                   Bulan ini
                </button>
                <button wire:click="bulanSelanjutnya" class="p-2 rounded hover:bg-gray-200 transition text-xs text-wrap">
                    Bulan selanjutnya  <x-heroicon-o-arrow-right class="h-5 w-5" /> 


                </button>
            </div>

            <!-- <PERSON><PERSON><PERSON>ulan dan <PERSON> -->
            <h2 class="text-xl font-bold text-center w-full md:w-auto md:text-left">
                {{ Carbon\Carbon::createFromDate($tahun, $bulan, 1)->translatedFormat('F Y') }}</h2>

            <!-- Pilihan Tampilan -->
            <div class="flex space-x-1 bg-gray-200 rounded-lg p-1 w-full md:w-auto justify-center">
                <button wire:click="aturTampilan('month')"
                    class="px-3 py-1 rounded-lg {{ $tampilan === 'month' ? 'bg-white shadow' : '' }}">Bulan</button>

                <button wire:click="aturTampilan('list')"
                    class="px-3 py-1 rounded-lg {{ $tampilan === 'list' ? 'bg-white shadow' : '' }}">List reservasi
                    bulan ini</button>
            </div>
        </div>

        <!-- Tampilan Bulan -->
        @if ($tampilan === 'month')
            <div class="overflow-x-auto -mx-2 sm:mx-0">
                @include('hotel::widgets.kalender-tampilan-month')
            </div>


            <!-- Tampilan List Reservasi -->
        @elseif($tampilan === 'list')
            @include('hotel::widgets.kalender-list-reservasi')
        @endif



        <!-- Detail Reservasi untuk Hari Terpilih -->
        @if ($hariTerpilih && isset($reservasiData[$hariTerpilih]) && $tampilan === 'month')
            @include('hotel::widgets.kalender-detail-reservasi')
        @endif


    </div>
</div>

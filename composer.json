{"name": "ridwans2/hotel", "type": "project", "description": "Sistem per<PERSON>", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "abdelhamiderrahmouni/filament-monaco-editor": "^0.2.5", "afsakar/filament-leaflet-map-picker": "^1.3", "agencetwogether/hookshelper": "^0.0.2", "awcodes/filament-table-repeater": "^3.1", "barryvdh/laravel-dompdf": "^3.1", "bezhansalleh/filament-shield": "^3.3", "blade-ui-kit/blade-heroicons": "^2.6", "codeat3/blade-phosphor-icons": "^2.3", "croustibat/filament-jobs-monitor": "^2.6", "datlechin/filament-menu-builder": "^0.6.5", "dutchcodingcompany/filament-developer-logins": "^1.8", "erusev/parsedown": "^1.7", "ezyang/htmlpurifier": "^4.18", "filament/filament": "^3.2", "guzzlehttp/guzzle": "^7.2", "icetalker/filament-table-repeater": "^1.3", "intervention/image": "^3.11", "jeffgreco13/filament-breezy": "^2.6", "laravel/folio": "^1.1", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "mike42/escpos-php": "^4.0", "psr/simple-cache": "2.0", "pxlrbt/filament-excel": "^2.4", "shuvroroy/filament-spatie-laravel-backup": "^2.2", "solution-forest/filament-scaffold": "^0.0.9", "spatie/image": "^3.8", "spatie/laravel-medialibrary": "^11.0", "tinymce/tinymce": "^7.9", "tomatophp/filament-plugins": "^1.1", "torgodly/html2media": "^1.1", "valentin-morice/filament-json-column": "^1.7", "visualbuilder/email-templates": "^3.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.9.1", "laradumps/laradumps": "4.0", "laradumps/laradumps-core": "3.0", "laravel/dusk": "^8.3", "laravel/pint": "^1.0", "nunomaduro/collision": "^8.1", "opcodesio/log-viewer": "^3.14", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"files": ["app/Helpers/RajaHelper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [], "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-dusk-install": ["@php artisan dusk:install"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"dont-discover": ["laravel/telescope"]}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}
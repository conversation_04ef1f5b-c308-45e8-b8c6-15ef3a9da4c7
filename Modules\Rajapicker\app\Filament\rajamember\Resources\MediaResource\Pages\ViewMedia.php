<?php

namespace Modules\Rajapicker\Filament\rajamember\Resources\MediaResource\Pages;

use Modules\Rajapicker\Filament\rajamember\Resources\MediaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Grid;

class ViewMedia extends ViewRecord
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit'),
                
            Actions\DeleteAction::make()
                ->label('Hapus'),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Media: ' . $this->record->name;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Preview Media')
                    ->schema([
                        ImageEntry::make('url')
                            ->label('')
                            ->size(400)
                            ->visible(fn ($record): bool => $record->isImage()),
                            
                        TextEntry::make('file_name')
                            ->label('File tidak dapat ditampilkan')
                            ->visible(fn ($record): bool => !$record->isImage()),
                    ])
                    ->columnSpanFull(),
                    
                Section::make('Informasi File')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama'),
                                    
                                TextEntry::make('file_name')
                                    ->label('Nama File Asli'),
                                    
                                TextEntry::make('mime_type')
                                    ->label('Tipe MIME'),
                                    
                                TextEntry::make('size')
                                    ->label('Ukuran File')
                                    ->formatStateUsing(fn ($state): string => number_format($state / 1024, 1) . ' KB'),
                                    
                                TextEntry::make('collection_name')
                                    ->label('Koleksi')
                                    ->badge(),
                                    
                                TextEntry::make('disk')
                                    ->label('Storage Disk'),
                            ]),
                    ]),
                    
                Section::make('Metadata')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('custom_properties.alt')
                                    ->label('Alt Text')
                                    ->placeholder('Tidak ada'),
                                    
                                TextEntry::make('custom_properties.caption')
                                    ->label('Caption')
                                    ->placeholder('Tidak ada'),
                                    
                                TextEntry::make('custom_properties.description')
                                    ->label('Deskripsi')
                                    ->placeholder('Tidak ada')
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->collapsible(),
                    
                Section::make('Informasi Sistem')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Dibuat')
                                    ->dateTime('d M Y H:i:s'),
                                    
                                TextEntry::make('updated_at')
                                    ->label('Diupdate')
                                    ->dateTime('d M Y H:i:s'),
                                    
                                TextEntry::make('id')
                                    ->label('ID'),
                                    
                                TextEntry::make('uuid')
                                    ->label('UUID')
                                    ->placeholder('Tidak ada'),
                            ]),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}

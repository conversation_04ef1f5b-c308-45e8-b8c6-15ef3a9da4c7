<div class="space-y-4">
    @php
        $hasData = false;
        ksort($reservasiData);
    @endphp

    @foreach ($reservasiData as $day => $reservasi)
        @php $hasData = true; @endphp
        <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow transition-shadow">
            <div class="bg-gray-100 p-2 sm:p-3 font-medium border-b text-sm sm:text-base">
                {{ $day }}
                {{ Carbon\Carbon::createFromDate($tahun, $bulan, 1)->translatedFormat('F Y') }}
            </div>
            <div class="divide-y">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                                Tamu</th>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Kamar</th>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Check-in</th>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Check-out</th>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Durasi</th>
                            <th scope="col"
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($reservasi as $item)
                            <tr class="hover:bg-gray-50">
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    {{ $item['tamu'] }}
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    <div>{{ $item['kamar'] }} | {{ $item['kamar_tipe'] ?? '-' }}</div>
                              
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    {{ $item['check_in'] }}
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    {{ $item['check_out'] }}
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    {{ $item['durasi'] ?? '-' }} hari
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap text-xs sm:text-sm">
                                    <span
                                        class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $item['status_kelas'] ?? 'bg-blue-500' }} text-white">
                                        {{ $item['status'] }}
                                    </span>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endforeach

    @if (!$hasData)
        <div class="text-center py-8 sm:py-12 text-gray-500 bg-gray-50 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 text-gray-400"
                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p class="text-sm sm:text-base">Tidak ada reservasi pada bulan ini</p>
        </div>
    @endif
</div>

{"main": {"id": "6fab859b3765f089", "type": "split", "children": [{"id": "1449e55d2db15189", "type": "tabs", "children": [{"id": "1c0ab33d2aec303c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "RajaMenu/panduan-rajamenu.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "panduan-rajamenu"}}]}], "direction": "vertical"}, "left": {"id": "5b40f1d90ac0de87", "type": "split", "children": [{"id": "6d74ae9fba9a88cb", "type": "tabs", "children": [{"id": "47f6aaabf9767a63", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "307950d47386f888", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "73cbedefe111cc61", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "09c4c8fc0371543c", "type": "split", "children": [{"id": "40a9b23a6700fd46", "type": "tabs", "children": [{"id": "d101003b4f6d4d0b", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Shortcode/integrasi-shortcode.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for integrasi-shortcode"}}, {"id": "6c5f9c9f15bcc565", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Shortcode/integrasi-shortcode.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from integrasi-shortcode"}}, {"id": "64917949b1cb9493", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "b222200821e7b081", "type": "leaf", "state": {"type": "outline", "state": {"file": "Shortcode/integrasi-shortcode.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of integrasi-shortcode"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "1c0ab33d2aec303c", "lastOpenFiles": ["migrasi-meta-acara.md", "RajaMenu/panduan-rajamenu.md", "panduan-rajamenu.md", "panduanmeta.md", "pakaimeta.md", "rajamenu-migration.md", "RajaMenu/rajamenu-migration.md", "RajaMenu/flyout-navigation-implementation.md", "<PERSON><PERSON><PERSON><PERSON>", "pakaijcol.md", "contoh-penggunaan-form-generator.md", "contoh-kompatibilitas-fieldbuilder.md", "pengaturan-lanjutan-fieldbuilder.md", "kompatibilitas-fieldbuilder.md", "raja-form-builder-troubleshooting.md", "cara-testing-raja-form-builder.md", "contoh-implementasi-raja-form-builder.md", "raja-form-builder.md", "form-builder-improvements.md", "form-builder-auto-label-test.md", "form-builder-testing.md", "<PERSON><PERSON><PERSON><PERSON>-module-migration.md", "troubleshooting-rajajson.md", "testing-r<PERSON><PERSON>son-fields.md", "testing-automatic-rajajson.md", "<PERSON><PERSON><PERSON><PERSON>-fields.md", "<PERSON><PERSON><PERSON><PERSON>-comparison.md", "contoh-penggunaan-waad-metadata.php"]}
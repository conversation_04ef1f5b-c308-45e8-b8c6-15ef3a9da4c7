<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;

use App\Aplikasi\Hotel\Resources\ReservasiResource;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Models\Pembayaran;
use App\Aplikasi\Hotel\Models\Kamar;
// Import action yang sudah dipisahkan
use App\Aplikasi\Hotel\Resources\ReservasiResource\Actions\PerpanjangKamarAction;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Actions\GantiKamarAction;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Actions\TambahPembayaranAction;
// Import komponen yang dibutuhkan untuk infolist
use Filament\Actions;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\Card;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewReservasi extends ViewRecord
{
    protected static string $resource = ReservasiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Tombol Perpanjang Kamar
            PerpanjangKamarAction::make(),
            Actions\Action::make('invoice')
                ->label('Lihat Invoice')
                ->icon('heroicon-o-document-text')
                ->url(fn () => ReservasiResource::getUrl('invoice', ['record' => $this->record])),
            // Tombol Ganti Kamar
            GantiKamarAction::make(),

            // Tombol Tambah Pembayaran
            TambahPembayaranAction::make(),

            // Tombol Proses Check Out
            Actions\Action::make('prosesCheckOut')
                ->label('Proses Check Out')
                ->color('danger')
                ->icon('heroicon-o-arrow-right')
                ->requiresConfirmation()
                ->modalWidth('md')
                ->modalIcon('heroicon-o-exclamation-triangle')
                ->modalIconColor('danger')
                ->modalHeading('Konfirmasi Check Out')
                ->modalDescription('Apakah Anda yakin ingin melakukan check out? Tindakan ini tidak dapat dibatalkan.')
                ->modalSubmitActionLabel('Ya, Proses Check Out')
                ->modalCancelActionLabel('Batal')
                ->action(function (Reservasi $record): void {
                    try {
                        $record->update([
                            'status_reservasi' => 'SCO',
                        ]);

                        Notification::make()
                            ->title('Check out berhasil diproses')
                            ->icon('heroicon-o-check-badge')
                            ->iconColor('success')
                            ->success()
                            ->duration(5000)
                            ->send();

                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Terjadi kesalahan: '.$e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }


    // Menggunakan kembali struktur infolist yang sebelumnya berfungsi
    // Di dalam method infolist di class ViewReservasi
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Detail Reservasi')
                    ->icon('heroicon-o-information-circle')
                    ->description('Informasi lengkap tentang reservasi')
                    ->collapsible()
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                Group::make([
                                    TextEntry::make('kamar.nama')
                                        ->label('Kamar')
                                        ->formatStateUsing(fn ($state, $record) => $record->kamar->nama.' :: '.$record->kamar->tipe->nama)
                                        ->icon('heroicon-o-home')
                                        ->weight('bold')
                                        ->size('sm')
                                        ->color('success'),

                                    TextEntry::make('kamar.harga')
                                        ->label('Harga Per Malam')
                                        ->prefix('Rp ')
                                        ->numeric(
                                            decimalPlaces: 0,
                                            decimalSeparator: ',',
                                            thousandsSeparator: '.',
                                        ),

                                    TextEntry::make('check_in')
                                        ->label('Check In')
                                        ->icon('heroicon-o-arrow-right')
                                        ->color('success')
                                        ->dateTime('d/m/Y H:i'),

                                    TextEntry::make('check_out')
                                        ->label('Check Out')
                                        ->icon('heroicon-o-arrow-left')
                                        ->color('danger')
                                        ->dateTime('d/m/Y H:i'),
                                ])->columns(4)->columnSpan(3),

                                Group::make([
                                    TextEntry::make('tamu.nama')
                                        ->label('Tamu')
                                        ->icon('heroicon-o-user')
                                        ->weight('medium'),

                                    TextEntry::make('tamu.telpon')
                                        ->label('Telepon')
                                        ->icon('heroicon-o-phone')
                                        ->visible(fn ($record) => $record->tamu && $record->tamu->telpon),

                                    TextEntry::make('referensi')
                                        ->label('Referensi')
                                        ->icon('heroicon-o-bookmark')
                                        ->default('Langsung'),

                                    TextEntry::make('ket_referensi')
                                        ->label('Keterangan Referensi')
                                        ->default('-'),
                                ])->columns(4)->columnSpan(3),


                            ]),

                        Grid::make(4)
                            ->schema([


                                TextEntry::make('lama_menginap')
                                    ->label('Lama Menginap')
                                    ->icon('heroicon-o-calendar')
                                    ->getStateUsing(function (Reservasi $record): string {
                                        $checkIn = \Carbon\Carbon::parse($record->check_in);
                                        $checkOut = \Carbon\Carbon::parse($record->check_out);
                                        return $checkIn->diffInDays($checkOut).' hari';
                                    }),

                                TextEntry::make('status_reservasi')
                                    ->label('Status')
                                    ->formatStateUsing(function ($state) {
                                        // Konversi kode status ke teks
                                        return match ($state) {
                                            'SCI' => 'Check In',
                                            'SCO' => 'Check Out',
                                            'BK' => 'Boking',
                                            'PD' => 'Pending',
                                            default => $state,
                                        };
                                    })
                                    ->icon('heroicon-o-check-circle') // Menggunakan icon yang tersedia
                                    ->badge()
                                    ->size('lg')
                                    ->color(fn (string $state): string => match ($state) {
                                        'SCI' => 'success',
                                        'SCO' => 'danger',
                                        'PD' => 'warning',
                                        'BK' => 'info',
                                        default => 'gray',
                                    }),
                            ]),

                        Grid::make(3)
                            ->schema([
                                TextEntry::make('user.name')
                                    ->label('Dilayani oleh')
                                    ->icon('heroicon-o-user-circle')
                                    ->color('gray'),

                                TextEntry::make('created_at')
                                    ->label('Tanggal daftar')
                                    ->icon('heroicon-o-clock')
                                    ->dateTime('d/m/Y H:i'),

                                TextEntry::make('updated_at')
                                    ->label('Terakhir diperbarui')
                                    // ->icon('heroicon-o-refresh') // Menggunakan icon yang tersedia
                                    ->dateTime('d/m/Y H:i'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Section::make('Ringkasan Tagihan')

                    ->collapsible()
                    ->schema([

                        Group::make([
                            TextEntry::make('diskon')
                                ->label('Diskon')
                                ->suffix('%')
                                ->icon('heroicon-o-tag'),

                            TextEntry::make('pajak')
                                ->label('Pajak')
                                ->suffix('%')
                            // ->icon('heroicon-o-office-building')
                            , // Menggunakan icon yang tersedia

                            TextEntry::make('servis')
                                ->label('Servis')
                                ->suffix('%')
                                ->icon('heroicon-o-cog'), // Menggunakan icon yang tersedia
                        ])->columns(3),

                        Group::make([
                            TextEntry::make('totalHarga')
                                ->label('Total Tagihan')
                                ->getStateUsing(fn (Reservasi $record) => 'Rp '.number_format($record->totalHarga(), 0, ',', '.'))
                                ->weight('bold')
                                ->color('primary')
                                ->size('xl')
                                ->icon('heroicon-o-banknotes')
                            , // Menggunakan icon yang tersedia
                            TextEntry::make('statusPembayaran')
                                ->label('Status Pembayaran')
                                ->getStateUsing(function (Reservasi $record) {
                                    $totalPembayaran = Pembayaran::where('reservasi_id', $record->id)
                                        ->sum('jumlah');
                                    $sisaTagihan = $record->totalHarga() - $totalPembayaran;
                                    if ($sisaTagihan <= 0) {
                                        return 'Lunas';
                                    } elseif ($totalPembayaran > 0) {
                                        return 'Sebagian';
                                    } else {
                                        return 'Belum Bayar';
                                    }
                                })
                                ->badge()
                                ->size('lg')
                                ->icon('heroicon-o-document') // Menggunakan icon yang tersedia
                                ->color(function (string $state): string {
                                    return match ($state) {
                                        'Lunas' => 'success',
                                        'Sebagian' => 'warning',
                                        default => 'danger',
                                    };
                                }),

                        ])->columns(2),

                        Group::make([
                            // Di dalam fungsi getStateUsing untuk totalPembayaran
                            TextEntry::make('totalPembayaran')
                                ->label('Total Pembayaran')
                                ->getStateUsing(function (Reservasi $record) {
                                    $semuaPembayaran = Pembayaran::where('reservasi_id', $record->id)->get();
                                    $totalPembayaran = Pembayaran::where('reservasi_id', $record->id)->sum('jumlah');
                                    return 'Rp '.number_format($totalPembayaran, 0, ',', '.');
                                })
                                ->weight('bold')
                                ->color('success')
                                ->icon('heroicon-o-arrow-down'),

                            TextEntry::make('sisaTagihan')
                                ->label('Sisa Tagihan')
                                ->getStateUsing(function (Reservasi $record) {
                                    $totalPembayaran = Pembayaran::where('reservasi_id', $record->id)
                                        ->sum('jumlah');
                                    $sisaTagihan = $record->totalHarga() - $totalPembayaran;
                                    return $sisaTagihan < 0 ? 'Rp 0' : 'Rp '.number_format($sisaTagihan, 0, ',', '.');
                                })
                                ->weight('bold')
                                ->icon('heroicon-o-arrow-up') // Menggunakan icon yang tersedia
                                ->color(function (Reservasi $record) {
                                    $totalPembayaran = Pembayaran::where('reservasi_id', $record->id)
                                        ->sum('jumlah');
                                    $sisaTagihan = $record->totalHarga() - $totalPembayaran;
                                    return $sisaTagihan > 0 ? 'danger' : 'success';
                                }),
                        ])->columns(2),




                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }
}
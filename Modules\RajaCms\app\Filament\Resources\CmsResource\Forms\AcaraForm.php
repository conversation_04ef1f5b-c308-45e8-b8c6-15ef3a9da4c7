<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Afsakar\LeafletMapPicker\LeafletMapPicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;


class AcaraForm
{

    private static function getAddressFromCoordinates($lat, $lng): string
    {
        // Validasi koordinat
        if (!is_numeric($lat) || !is_numeric($lng)) {
            Log::warning('Koordinat tidak valid untuk reverse geocoding', [
                'lat' => $lat,
                'lng' => $lng
            ]);
            return "Koordinat tidak valid";
        }

        try {
            // Tambahkan delay kecil untuk menghindari rate limiting (Nominatim membatasi 1 request per detik)
            usleep(100000); // 100ms delay

            // Gunakan Nominatim API untuk reverse geocoding
            $response = Http::withHeaders([
                'User-Agent' => 'HotelApp/1.0', // User-Agent wajib untuk Nominatim
                'Accept-Language' => 'id,en;q=0.9', // Prioritaskan bahasa Indonesia
            ])->timeout(5)->get('https://nominatim.openstreetmap.org/reverse', [
                'format' => 'json',
                'lat' => $lat,
                'lon' => $lng,
                'zoom' => 18, // Level detail alamat
                'addressdetails' => 1,
                'accept-language' => 'id',
            ]);

            if ($response->successful()) {
                $data = $response->json();

                // Jika ada address details, buat alamat yang ringkas
                if (isset($data['address']) && is_array($data['address'])) {
                    $addressParts = [];

                    // Hanya tampilkan komponen alamat yang relevan (jalan, kecamatan, kota/kabupaten)
                    // Urutan prioritas: jalan, nomor rumah, kecamatan, kota/kabupaten
                    $relevantComponents = [
                        'road' => true,         // Jalan
                        'house_number' => true, // Nomor rumah
                        'suburb' => true,       // Kecamatan/area
                        'town' => true,         // Kota kecil
                        'city' => true,         // Kota
                        'county' => true,       // Kabupaten
                        'state' => true,        // Provinsi
                        'country' => false,     // Negara (tidak ditampilkan)
                        'country_code' => false, // Kode negara (tidak ditampilkan)
                        'continent' => false,    // Kontinen (tidak ditampilkan)
                        'neighbourhood' => false, // Lingkungan (tidak ditampilkan)
                        'city_district' => false, // Wilayah kota (tidak ditampilkan)
                        'municipality' => false, // Kota (tidak ditampilkan)
                        'postcode' => true,     // Kode pos (tidak ditampilkan)
                    ];

                    // Tambahkan komponen alamat yang relevan
                    foreach ($data['address'] as $component => $value) {
                        if (isset($relevantComponents[$component]) && !empty($value)) {
                            $addressParts[] = $value;
                        }
                    }

                    if (!empty($addressParts)) {
                        return implode(', ', $addressParts);
                    }
                }

                // Jika tidak bisa membuat alamat ringkas dari address details,
                // gunakan display_name tapi hapus bagian negara dan koordinat
                if (isset($data['display_name']) && !empty($data['display_name'])) {
                    // Hapus bagian negara (biasanya di akhir alamat)
                    $displayName = preg_replace('/, Indonesia$/', '', $data['display_name']);
                    return $displayName;
                }
            } else {
                Log::warning('Nominatim API response tidak sukses', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
            }

            // Jika Nominatim gagal, kembalikan pesan lokasi tanpa koordinat
            return "Lokasi tidak ditemukan";

        } catch (\Exception $e) {
            // Jika terjadi error, log error
            Log::error('Error saat reverse geocoding: ' . $e->getMessage(), [
                'lat' => $lat,
                'lng' => $lng,
                'exception' => get_class($e)
            ]);

            // Kembalikan pesan error yang lebih user-friendly
            return "Tidak dapat menentukan alamat";
        }
    }



    public static function make(): Grid
    {
        return Grid::make(1)
            ->schema([

                Section::make('Info Acara')
                    ->collapsible()
                    ->schema([
                        DateTimePicker::make('jcol.acara.tgl_mulai')
                            ->label('Tanggal Mulai')
                            ->seconds(false)
                            ->required(),

                        DateTimePicker::make('jcol.acara.tgl_selesai')
                            ->label('Tanggal Selesai')
                            ->seconds(false),


                        LeafletMapPicker::make('location')
                            ->label('Peta lokasi acara')
                            ->defaultLocation([-8.583773, 116.100430])
                            ->hideTileControl()
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                // Log state untuk debugging
                                // Log::debug('LeafletMapPicker state:', ['state' => $state]);

                                // Ekstrak koordinat dari berbagai format yang mungkin
                                $lat = null;
                                $lng = null;

                                if (is_array($state)) {
                                    // Format array [lat, lng]
                                    if (count($state) === 2 && isset($state[0]) && isset($state[1])) {
                                        $lat = $state[0];
                                        $lng = $state[1];
                                    }
                                    // Format array dengan key lat, lng
                                    elseif (isset($state['lat']) && isset($state['lng'])) {
                                        $lat = $state['lat'];
                                        $lng = $state['lng'];
                                    }
                                    // Format array dengan key 0, 1
                                    elseif (isset($state[0]) && isset($state[1])) {
                                        $lat = $state[0];
                                        $lng = $state[1];
                                    }
                                }
                                // Format string JSON
                                elseif (is_string($state) && !empty($state)) {
                                    try {
                                        $decoded = json_decode($state, true);
                                        if (json_last_error() === JSON_ERROR_NONE) {
                                            if (isset($decoded['lat']) && isset($decoded['lng'])) {
                                                $lat = $decoded['lat'];
                                                $lng = $decoded['lng'];
                                            } elseif (is_array($decoded) && count($decoded) === 2) {
                                                $lat = $decoded[0];
                                                $lng = $decoded[1];
                                            }
                                        }
                                    } catch (\Exception $e) {
                                        Log::error('Error decoding JSON from LeafletMapPicker: ' . $e->getMessage());
                                    }
                                }

                                // Jika koordinat valid, dapatkan alamat
                                if ($lat !== null && $lng !== null) {
                                    // Dapatkan alamat dari koordinat
                                    $address = self::getAddressFromCoordinates($lat, $lng);

                                    // Update field lokasi dengan alamat
                                    $set('jcol.acara.alamat', $address);
                                }
                            })
                            ->columnSpanFull(),

                            Textarea::make('jcol.acara.alamat')
                            ->label('alamat')
                            ->placeholder('di generate otomatis setelah memilih lokasi di peta / anda bisa menuliskan manual')
                            ->live(onBlur: true)
                            ->columnSpanFull(),


                    ])->columns(2),

                Section::make('Pembicara')
                    ->collapsible()
                    ->collapsed()
                    ->schema([
                        Repeater::make('jcol.pembicara')
                            ->label('Daftar Pembicara')
                            ->grid(2)
                            ->addActionLabel('Tambah Pembicara')

                            ->schema([
                                TextInput::make('nama')->label('Nama Pembicara'),
                                TextInput::make('jabatan')->label('Jabatan'),
                                RajaPicker::make('foto')->label('Foto Pembicara')->collection('cms'),
                                // FileUpload::make('foto')->label('Foto Pembicara')->directory('cms')
                                //     ->image()
                                //     ->imageResizeTargetWidth('200')
                                //     ->imageResizeTargetHeight('200')
                                //     ->maxFiles(1) 
                            ])
                    ])

            ]);
    }
}

<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;
use App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers\KamarRelationManager;
use App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers\PembayaranRelationManager;

use App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers\TamuRelationManager;

use App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers\TransaksiRelationManager;


use Carbon\Carbon;
use Filament\Resources\Resource;
use Session;


class ReservasiResource extends Resource
{
    protected static ?string $model = Reservasi::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';


    protected static ?string $navigationLabel = 'Reservasi';
    protected static ?string $navigationGroup = 'Reservasi';
    protected static ?string $slug = 'reservasi';
    protected static ?string $createActionLabel = 'Tambah Reservasi';
    protected static bool $shouldRegisterNavigation = false;



    public static function getRelations(): array
    {
        return [
            // KamarRelationManager::class,
            TransaksiRelationManager::class,
            PembayaranRelationManager::class,
            TamuRelationManager::class,

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReservasis::route('/'),
            'create' => Pages\CreateReservasi::route('/create'),
            'edit' => Pages\EditReservasi::route('/{record}/edit'),
            'invoice' => Pages\InvoiceHotel::route('/{record}/invoice'),
            'view' => Pages\ViewReservasi::route('/{record}'),
      


        ];
    }


    // public function getKamarTersedia($checkIn, $checkOut)
    // {
    //     if (empty($checkIn) || empty($checkOut)) {
    //         return collect([]);
    //     }

    //     $checkInDate = Carbon::parse($checkIn);
    //     $checkOutDate = Carbon::parse($checkOut);

    //     // Dapatkan semua tipe kamar
    //     $tipeKamars = KamarTipe::with(['kamar'])->get();

    //     // Buat hasil berupa collection dengan struktur yang dibutuhkan
    //     $result = collect();

    //     foreach ($tipeKamars as $tipe) {
    //         $kamars = Kamar::where('kategori_id', $tipe->id)
    //             ->get()
    //             ->map(function ($kamar) use ($checkInDate, $checkOutDate) {
    //                 // Cek apakah kamar tersedia
    //                 $isAvailable = $kamar->isAvailable($checkInDate, $checkOutDate);

    //                 return [
    //                     'id' => $kamar->id,
    //                     'nama' => $kamar->nama,
    //                     'harga' => $kamar->harga,
    //                     'tersedia' => $isAvailable,
    //                     'fasilitas' => $kamar->fasilitas,
    //                     'spesifikasi' => $kamar->spesifikasi ?? []
    //                 ];
    //             });

    //         // Hanya tambahkan tipe kamar jika ada kamarnya
    //         if ($kamars->count() > 0) {
    //             $result->push([
    //                 'tipe_id' => $tipe->id,
    //                 'tipe_nama' => $tipe->nama,
    //                 'kamars' => $kamars
    //             ]);
    //         }
    //     }

    //     return $result;
    // }


    public static function getWidgets(): array
    {
        return [
            ReservasiResource\Widgets\KeranjangWidget::class,
        ];
    }


    protected function hitungTotalPembayaran(float $totalBelanja, Reservasi $penjualan): float
    {
        $pajak = $totalBelanja * ($penjualan->pajak / 100);
        $servis = $totalBelanja * ($penjualan->servis / 100);
        $diskon = $totalBelanja * ($penjualan->diskon / 100);

        return $totalBelanja + $pajak + $servis - $diskon;
    }

    protected function processJsonField($field)
    {
        if (is_string($field) && $this->isJson($field)) {
            return json_decode($field, true);
        }
        return $field ?? [];
    }

    protected function isJson($string)
    {
        if (! is_string($string))
            return false;
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }


    protected function angkaBersih($nilai)
    {
        if (is_string($nilai)) {
            return (float) str_replace(['Rp', '.', ','], '', $nilai);
        }

        return (float) $nilai;
    }


}

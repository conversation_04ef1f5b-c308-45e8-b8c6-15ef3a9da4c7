<?php

namespace App\Aplikasi\Hotel\Models;


use App\Models\Produk as Produk;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperFasilitas
 */
class Fasilitas extends Produk
{
 


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'FASILITAS');
        });

        static::creating(function ($model) {
            $model->jenis = 'FASILITAS';
        });
        
        static::updating(function ($model) {
            $model->jenis = 'FASILITAS';
        });
    }
  
    
}

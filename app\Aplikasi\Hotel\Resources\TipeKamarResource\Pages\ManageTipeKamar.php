<?php

namespace App\Aplikasi\Hotel\Resources\TipeKamarResource\Pages;

use App\Aplikasi\Hotel\Resources\TipeKamarResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageTipeKamar extends ManageRecords
{
    protected static string $resource = TipeKamarResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

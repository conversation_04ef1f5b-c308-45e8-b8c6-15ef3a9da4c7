<?php

namespace Modules\RajaCms\Filament\Pages\Cms;

use App\Models\KategoriArtikel;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
class Kategori extends Page implements HasTable
{
    use HasPageShield;
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $slug = 'cms/kategori';
    protected static ?string $navigationLabel = 'Kategori Artikel';
    protected static ?string $navigationGroup = 'Cms';
    protected static ?int $navigationSort = 5;

    protected static string $view = 'pages.kategori'; // pastikan view ada

    public function table(Table $table): Table
    {
        return $table
            ->query(KategoriArtikel::query())
            ->columns([
                TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Tanggal Dibuat')
                    ->dateTime('d M Y')
                    ->sortable(),
            ])
            ->actions([
                ViewAction::make()->form(fn(Form $form)=>$form->schema($this->getFormSchema())),
                EditAction::make()->form(fn(Form $form)=>$form->schema($this->getFormSchema())),
                DeleteAction::make(),
            ])
            ->headerActions([
                CreateAction::make()->form(fn(Form $form)=>$form->schema($this->getFormSchema())),
            ]);
    }

    protected function getFormSchema(): array
    {
        return [
            TextInput::make('nama')
                ->label('Judul')
                ->required()
                ->maxLength(255),
        ];
    }
} 
<?php

namespace Modules\RajaCms\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class TemaController extends Controller
{
    /**
     * Menampilkan daftar tema yang tersedia
     */
    public function daftar()
    {
        $temaAktif = 'default'; // Tema default
        $temaTersedia = $this->dapatkanDaftarTema();
        
        return view('tema', compact('temaAktif', 'temaTersedia'));
    }
    
    /**
     * Mendapatkan daftar tema yang tersedia
     */
    private function dapatkanDaftarTema()
    {
        $temaPath = resource_path('views/themes');
        $temaDirs = File::directories($temaPath);
        
        $daftarTema = [];
        
        foreach ($temaDirs as $dir) {
            $namaTema = basename($dir);
            $daftarTema[] = $namaTema;
        }
        
        return $daftarTema;
    }
} 
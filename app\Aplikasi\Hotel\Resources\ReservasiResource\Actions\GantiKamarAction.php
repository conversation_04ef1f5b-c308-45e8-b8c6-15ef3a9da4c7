<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Actions;

use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Models\Pembayaran;
use App\Aplikasi\Hotel\Models\MetodePembayaran;
use App\Aplikasi\Hotel\Models\Transaksi;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Filament\Forms\Components\Rupiah;
use Filament\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Section as FormsSection;
use Filament\Forms\Components\Placeholder;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class GantiKamarAction
{
    public static function make(): Action
    {
        return Action::make('gantiKamar')
            ->label('Ganti Kamar')
            // ->color('info')
            ->icon('heroicon-o-home')
            ->modalWidth('xl')
            ->modalHeading('Ganti Kamar')
            ->form([
              
                        Select::make('kamar_baru_id')
                            ->label('Kamar Baru')
                            ->options(function () {
                                return Kamar::where('jenis', 'KAMAR')
                                    ->with('tipe') // Eager loading relasi tipe kamar
                                    ->get()
                                    ->mapWithKeys(function ($kamar) {
                                        // Menambahkan tipekamar.nama sebelum nama kamar
                                        $tipeName = $kamar->tipe ? $kamar->tipe->nama : 'Tidak ada tipe';
                                        return [$kamar->id => "{$tipeName} - {$kamar->nama} - Rp ".number_format($kamar->harga, 0, ',', '.')];
                                    });
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Get $get, Set $set, ?string $state, Reservasi $record) {
                                if (! $state) {
                                    $set('harga', 0);
                                    $set('harga_modal', 0);
                                    return;
                                }

                                // Ambil data kamar baru
                                $kamarBaru = Kamar::find($state);
                                if (! $kamarBaru) {
                                    $set('harga', 0);
                                    $set('harga_modal', 0);
                                    return;
                                }

                                // Ambil data kamar lama
                                $kamarLama = Kamar::find($record->kamar_id);
                                if (! $kamarLama) {
                                    $set('harga', 0);
                                    $set('harga_modal', 0);
                                    return;
                                }

                                // Hitung selisih harga
                                $selisihHarga = $kamarBaru->harga - $kamarLama->harga;
                                $selisihModal = $kamarBaru->harga_modal - $kamarLama->harga_modal;

                                // Jika selisih negatif (kamar baru lebih murah), tetapkan ke 0
                                $set('harga', max(0, $selisihHarga));
                                $set('harga_modal', max(0, $selisihModal));

                                // Set jumlah pembayaran sama dengan selisih harga jika positif
                                if ($selisihHarga > 0) {
                                    $set('jumlah', $selisihHarga);
                                } else {
                                    $set('jumlah', 0);
                                }
                            }),
                        Rupiah::make('harga')->label('Selisih harga')->disabled(),
                        // TextInput::make('harga')
                        //     ->label('Selisih Harga')
                        //     ->numeric()
                        //     ->disabled()
                        //     ->default(0)
                        //     ->prefix('Rp')
                        //     ->mask('999.999.999.999')
                        //     ->helperText('Selisih harga kamar baru dengan kamar sebelumnya'),

                     
               

                // Bagian Pembayaran
                // FormsSection::make('Pembayaran')
                //     ->description('Masukkan informasi pembayaran (jika ada selisih harga)')
                //     ->icon('heroicon-o-banknotes')
                //     ->schema([
                //         Select::make('metode_pembayaran_id')
                //             ->label('Metode Pembayaran')
                //             ->options(MetodePembayaran::where('status', 1)->pluck('nama', 'id'))
                //             ->required()
                //             ->searchable()
                //             ->preload()
                //             ->visible(fn (Get $get): bool => (int) $get('harga') > 0),

                //         TextInput::make('jumlah')
                //             ->label('Jumlah Pembayaran')
                //             ->numeric()
                //             ->prefix('Rp')
                //             ->mask('999.999.999.999')
                //             ->required(fn (Get $get): bool => (int) $get('harga') > 0)
                //             ->visible(fn (Get $get): bool => (int) $get('harga') > 0),

                //         Fieldset::make('Detail Bank')
                //             ->schema([
                //                 TextInput::make('pengirim')
                //                     ->label('Pengirim')
                //                     ->placeholder('Nama Pengirim'),

                //                 TextInput::make('tujuan')
                //                     ->label('Tujuan')
                //                     ->placeholder('Nomor Rekening / Tujuan'),

                //                 FileUpload::make('bukti')
                //                     ->label('Bukti Pembayaran')
                //                     ->directory('pembayaran')
                //                     ->preserveFilenames()
                //                     ->acceptedFileTypes(['image/*', 'application/pdf'])
                //                     ->maxSize(2048),
                //             ])
                //             ->columns(2)
                //             ->visible(fn (Get $get): bool => (int) $get('harga') > 0),

                //         Textarea::make('ket')
                //             ->label('Keterangan')
                //             ->placeholder('Keterangan tambahan')
                //             ->rows(3),

                //         Placeholder::make('info_selisih_harga')
                //             ->label('Informasi')
                //             ->content(fn (Get $get): string => (int) $get('harga') > 0
                //                 ? 'Ada selisih harga yang perlu dibayarkan.'
                //                 : 'Tidak ada selisih harga yang perlu dibayarkan.')
                //             ->columnSpan('full'),
                //     ]),
            ])
            ->action(function (array $data, Reservasi $record): void {
                DB::beginTransaction();
                try {
                    // Ambil data kamar lama dan baru
                    $kamarLama = Kamar::find($record->kamar_id);
                    $kamarBaru = Kamar::find($data['kamar_baru_id']);
                    
                    if (!$kamarLama || !$kamarBaru) {
                        throw new \Exception('Kamar tidak ditemukan');
                    }
                    
                    // Hitung selisih harga
                    $selisihHarga = $kamarBaru->harga - $kamarLama->harga;
                    $selisihModal = $kamarBaru->harga_modal - $kamarLama->harga_modal;
                    
                    // Jika selisih negatif (kamar baru lebih murah), tetapkan ke 0
                    $harga = max(0, $selisihHarga);
                    $hargaModal = max(0, $selisihModal);
                    
                    // Update kamar di reservasi
                    $record->update([
                        'kamar_id' => $data['kamar_baru_id'],
                        'produk_id' => $data['kamar_baru_id'], // Produk id
                    ]);
                    
                    // Selalu mencatat transaksi ganti kamar, terlepas dari ada tidaknya selisih harga
                    Transaksi::create([
                        'toko_id' => $record->toko_id,
                        'reservasi_id' => $record->id,
                        'produk_id' => $data['kamar_baru_id'],
                        'nama_item' => 'Ganti kamar dari ' . $kamarLama->nama . ' ke ' . $kamarBaru->nama,
                        'harga_modal' => 0,
                        'harga' => 0,
                        'jumlah' => 1,
                        'ket' => 'Pergantian kamar pada tanggal ' . date('d/m/Y H:i'),
                    ]);
                    
                    // Tambahkan transaksi untuk selisih harga jika ada
                    if ($harga > 0) {
                        // Transaksi untuk selisih harga
                        Transaksi::create([
                            'toko_id' => $record->toko_id,
                            'reservasi_id' => $record->id,
                            'produk_id' => $data['kamar_baru_id'],
                            'nama_item' => 'Biaya selisih ganti kamar',
                            'harga_modal' => $hargaModal,
                            'harga' => $harga,
                            'jumlah' => 1,
                            'ket' => 'Selisih harga dari ' . $kamarLama->nama . ' (Rp ' . number_format($kamarLama->harga, 0, ',', '.') . 
                                  ') ke ' . $kamarBaru->nama . ' (Rp ' . number_format($kamarBaru->harga, 0, ',', '.') . ')',
                        ]);
                        
                        // // Tambahkan ke pembayaran
                        // Pembayaran::create([
                        //     'reservasi_id' => $record->id,
                        //     'metode_pembayaran_id' => $data['metode_pembayaran_id'],
                        //     'jumlah' => $data['jumlah'],
                        //     'pengirim' => $data['pengirim'] ?? '',
                        //     'tujuan' => $data['tujuan'] ?? '',
                        //     'bukti' => $data['bukti'] ?? null,
                        //     'status' => 'LUNAS',
                        //     'ket' => $data['ket'] ?? 'Pembayaran selisih harga ganti kamar dari ' . $kamarLama->nama . ' ke ' . $kamarBaru->nama,
                        //     'nama' => 'Biaya selisih ganti kamar'
                        // ]);
                    }
                    
                    DB::commit();
                    
                    $notifikasiBody = 'Kamar berhasil diganti dari '.$kamarLama->nama.' ke '.$kamarBaru->nama;
                    if ($harga > 0) {
                        $notifikasiBody .= ' dengan selisih harga Rp ' . number_format($harga, 0, ',', '.');
                    }
                    
                    Notification::make()
                        ->title('Ganti kamar berhasil')
                        ->body($notifikasiBody)
                        ->success()
                        ->duration(5000)
                        ->send();
                        
                } catch (\Exception $e) {
                    DB::rollBack();
                    Notification::make()
                        ->title('Terjadi kesalahan: ' . $e->getMessage())
                        ->danger()
                        ->send();
                }
            });
    }
}
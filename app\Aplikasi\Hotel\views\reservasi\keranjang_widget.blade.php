<x-filament::widget>
  <x-filament::section>
    <div class=" flex   items-end justify-between border-b border-neutral-200    mb-2 text-right  ">
      <button class="tombol border-none tombol-ikon tombol-kecil !rounded-lg hover:bg-danger" wire:confirm="Apakah Anda yakin ingin mengosongkan keranjang?" wire:click.prevent="kosongkan"><x-heroicon-o-trash class="h-5 w-5 mr-2 text-danger-600" /> <span> Kosongkan</span></button>
    </div>

    <div class="space-y-1" x-data="{}" x-on:keranjang-updated.window="$wire.$refresh()">
    
      <div class="flow-root">
  
        @if ($items->count() > 0)
          <div class="divide-y divide-neutral-200">
            @foreach ($items as $item)
              <div class="flex items-start justify-between rounded px-2 py-3 transition-colors duration-150 hover:bg-neutral-50">
            
                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm font-medium text-neutral-800">
                    {{ $item['nama_item'] }}
                  </p>
                  <div class="mt-1 flex text-xs text-neutral-500">
                    @if ($item['jenis'] == 'kamar')
                      <span>{{ number_format($item['harga'], 0, ',', '.') }} x
                        {{ $item['jumlah'] }}
                      </span>

                      @if (isset($item['check_in']) && isset($item['check_out']))
                        <span class="text-primary-600 ml-2">
                          {{ \Carbon\Carbon::parse($item['check_in'])->format('d/m/Y') }}
                          -
                          {{ \Carbon\Carbon::parse($item['check_out'])->format('d/m/Y') }}
                        </span>
                      @endif
                    @elseif($item['jenis'] == 'fasilitas')
                      <span>{{ number_format($item['harga'], 0, ',', '.') }} x
                        {{ $item['jumlah'] }}
                      </span>
                      <div
                        class="ml-3 flex w-1/5 items-center justify-center gap-0">

                        <button
                          class="flex items-center justify-center rounded-md bg-gray-100 p-1 hover:bg-gray-200"
                          wire:click.prevent="kurangiJumlah({{ $item['id'] }})">
                          <x-heroicon-o-minus class="h-3 w-3 text-slate-700" />
                        </button>
                        <!-- Tampilan normal (hanya angka) -->
                        <div
                          class="w-full cursor-pointer rounded py-1 text-center text-xs font-medium text-slate-600 transition-colors duration-150"
                          style="min-width: 30px;">
                          {{ $item['jumlah'] }}
                        </div>
                        <button
                          class="flex items-center justify-center rounded-md bg-gray-100 p-1 hover:bg-gray-200"
                          wire:click.prevent="tambahJumlah({{ $item['id'] }})">
                          <x-heroicon-o-plus class="h-3 w-3 text-gray-600" />
                        </button>

                      </div>
                    @endif

                  </div>
                </div>

                <div class="ml-4 flex items-center space-x-3">
                  <div class="text-sm font-medium text-neutral-800">
                    Rp
                    {{ number_format($item['harga'] * $item['jumlah'], 0, ',', '.') }}
                  </div>
                  <button
                    class="text-danger-600 hover:text-danger-700 transition-colors duration-150"
                    type="button" wire:click="removeItem({{ $item['id'] }})">
                    <x-heroicon-o-trash class="h-4 w-4" />
                  </button>
                </div>
              </div>
            @endforeach
          </div>

          <!-- Ringkasan Biaya -->
          <div
            class="mt-4 space-y-2 rounded-md border border-neutral-200 bg-neutral-50 p-3 pt-3 text-sm">
            <div class="flex justify-between">
              <span class="text-neutral-700">Subtotal</span>
              <span class="font-medium text-neutral-800">Rp
                {{ number_format($totalHarga, 0, ',', '.') }}</span>
            </div>

            <!-- Diskon jika ada -->
            @if ($persenValues['diskon'] > 0)
              <div class="flex justify-between text-neutral-700">
                <span>Diskon ({{ $persenValues['diskon'] }}%)</span>
                <span class="text-success-600">-Rp
                  {{ number_format($komponenBiaya['diskonNominal'], 0, ',', '.') }}</span>
              </div>
            @endif

            <!-- Pajak jika ada -->
            @if ($persenValues['pajak'] > 0)
              <div class="flex justify-between text-neutral-700">
                <span>Pajak ({{ $persenValues['pajak'] }}%)</span>
                <span>Rp
                  {{ number_format($komponenBiaya['pajakNominal'], 0, ',', '.') }}</span>
              </div>
            @endif

            <!-- Biaya layanan jika ada -->
            @if ($persenValues['servis'] > 0)
              <div class="flex justify-between text-neutral-700">
                <span>Service ({{ $persenValues['servis'] }}%)</span>
                <span>Rp
                  {{ number_format($komponenBiaya['servisNominal'], 0, ',', '.') }}</span>
              </div>
            @endif
            <div class="flex justify-between text-neutral-700">
              <span>Total Pembayaran </span>
              <span>Rp
                {{ number_format($totalPembayaran, 0, ',', '.') }}</span>
            </div>

          </div>

          <!-- Total Akhir -->

          <div class="mt-1 border-neutral-200 pt-1">
            <div class="flex justify-between font-medium" x-data="{}"
              x-on:pembayaran-updated.window="$wire.$refresh()">
              <p class="text-base text-neutral-800">Total Tagihan</p>
              <p class="text-danger-700 text-lg">Rp
                {{ number_format($totalAkhir, 0, ',', '.') }}</p>
            </div>
          </div>

       {{-- <div class="mt-1 border-neutral-200 pt-1">
            <div class="flex justify-between font-medium" x-data="{}"
              x-on:pembayaran-updated.window="$wire.$refresh()">
              <p class="text-base text-neutral-800">Total Pembayaran</p>
              <p class="text-primary-700 text-lg">Rp
                {{ number_format($totalAkhir, 0, ',', '.') }}</p>
            </div>
          </div>
           --}}

          <div class="mt-1 border-t border-neutral-200 pt-1">
            <div class="flex justify-between font-medium">
              <p class="text-base text-neutral-800">Sisa tagihan</p>
              <p
                class="{{ $sisaTagihan > 0 ? 'text-neutral-600' : 'text-success-600' }} text-lg">
                Rp {{ number_format($sisaTagihan, 0, ',', '.') }}
              </p>
            </div>
          </div>
        @else
          <div
            class="rounded-lg border border-neutral-200 bg-neutral-50 py-6 text-center text-sm text-neutral-600">
            <div class="mb-3">
              <x-heroicon-o-shopping-cart
                class="mx-auto h-10 w-10 text-neutral-400" />
            </div>
            <p class="font-medium">Keranjang Kosong</p>
            <p class="mt-1 text-xs text-neutral-500">Silakan pilih kamar untuk
              melanjutkan reservasi</p>
          </div>
        @endif
      </div>
    </div>
  </x-filament::section>
</x-filament::widget>

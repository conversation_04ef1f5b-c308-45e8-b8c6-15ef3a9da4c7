<?php

namespace App\Aplikasi\Hotel\Resources\TamuResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ReservasiRelationManager extends RelationManager
{
    protected static string $relationship = 'reservasi';
    protected static ?string $title = 'Reservasi';
    protected static ?string $recordTitleAttribute = 'no_invoice';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('kamar_id')
                    ->relationship('kamar', 'nomor_kamar')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\DateTimePicker::make('check_in')
                    ->required(),

                Forms\Components\DateTimePicker::make('check_out')
                    ->required(),

                Forms\Components\Select::make('status_reservasi')
                    ->options([
                        'pending' => 'Pending',
                        'confirmed' => 'Confirmed',
                        'checked_in' => 'Checked In',
                        'checked_out' => 'Checked Out',
                        'cancelled' => 'Cancelled',
                    ])
                    ->required(),

                Forms\Components\Select::make('referensi')
                    ->options([
                        'langsung' => 'Langsung',
                        'online' => 'Online',
                        'travel_agent' => 'Travel Agent',
                    ])
                    ->required(),

                Forms\Components\Textarea::make('ket_referensi')
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('no_invoice')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('kamar.nomor_kamar')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('check_in')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('check_out')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status_reservasi')
                    ->colors([
                        'danger' => 'cancelled',
                        'warning' => 'pending',
                        'success' => 'confirmed',
                        'primary' => 'checked_in',
                        'secondary' => 'checked_out',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status_reservasi')
                    ->options([
                        'pending' => 'Pending',
                        'confirmed' => 'Confirmed',
                        'checked_in' => 'Checked In',
                        'checked_out' => 'Checked Out',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}

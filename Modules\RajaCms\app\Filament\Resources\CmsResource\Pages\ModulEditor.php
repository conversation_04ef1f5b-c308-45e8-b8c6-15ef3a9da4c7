<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Filament\Resources\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

use Filament\Forms\Concerns\InteractsWithForms;

class ModulEditor extends Page
{
    use InteractsWithForms;

    protected static string $resource = CmsResource::class;

    protected static string $view = 'filament.resources.cms-resource.pages.modul-editor';

    protected static ?string $title = 'Edit Modul';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static bool $shouldRegisterNavigation = false;

    public $filePath;
    public $fileName;
    public $content;
    public $modulDir;
    public $page_layout;
    public $temaAktif = '';
    public $fileContent = '';
    public $selectedFile = null;
    public array $moduleFiles = [];

    public function mount(): void
    {
        $this->temaAktif = config('tema.aktif', 'default');
        $this->modulDir = $this->getModulDir();
        $this->loadModuleFiles();

        // Cek apakah ini adalah permintaan AJAX untuk menyimpan file
        if (request()->ajax() && request()->isMethod('post') && request()->input('action') === 'save-file') {
            $this->handleAjaxSave();
            return;
        }

        $namaModul = request()->get('nama');

        if ($namaModul) {
            // Normalisasi path
            $filePath = $this->modulDir . DIRECTORY_SEPARATOR . $namaModul . '.blade.php';

            $this->filePath = $filePath;
            $this->fileName = basename($filePath);
            $this->selectedFile = $filePath;

            if (file_exists($filePath)) {
                $this->content = file_get_contents($filePath);
                $this->fileContent = $this->content;
                $this->page_layout = $this->content;
            } else {
                // Coba cari file dengan path relatif
                $relativePath = str_replace(public_path(), '', $filePath);
                $absolutePath = public_path(ltrim($relativePath, DIRECTORY_SEPARATOR));

                if (file_exists($absolutePath)) {
                    $this->filePath = $absolutePath;
                    $this->selectedFile = $absolutePath;
                    $this->content = file_get_contents($absolutePath);
                    $this->fileContent = $this->content;
                    $this->page_layout = $this->content;
                } else {
                    Notification::make()
                        ->title('File tidak ditemukan')
                        ->body('Path: ' . $filePath)
                        ->danger()
                        ->send();

                    redirect()->route('filament.admin.resources.cms.tema');
                    return;
                }
            }
        } else {
            redirect()->route('filament.admin.resources.cms.tema');
            return;
        }

        $this->form->fill([
            'page_layout' => $this->page_layout,
        ]);
    }

    /**
     * Menangani permintaan AJAX untuk menyimpan file
     */
    protected function handleAjaxSave()
    {
        // Ambil konten dari permintaan
        $content = request()->input('content');

        // Cek apakah ada parameter nama
        $namaModul = request()->get('nama');

        if (!$namaModul) {
            return response()->json([
                'success' => false,
                'message' => 'Nama modul tidak ditemukan'
            ]);
        }

        $filePath = $this->modulDir . DIRECTORY_SEPARATOR . $namaModul . '.blade.php';

        // Cek apakah file ada
        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'File ' . $namaModul . ' tidak ditemukan di direktori modul'
            ]);
        }

        try {
            // Simpan konten ke file
            File::put($filePath, $content);

            // Log untuk debugging
            Log::info('File berhasil disimpan via AJAX', [
                'filePath' => $filePath,
                'contentLength' => strlen($content)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File berhasil disimpan'
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error saat menyimpan file via AJAX', [
                'filePath' => $filePath,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    protected function loadModuleFiles(): void
    {
        $this->moduleFiles = [];

        if (!File::exists($this->modulDir)) {
            return;
        }

        $allFiles = $this->getAllFiles($this->modulDir);

        foreach ($allFiles as $file) {
            if (str_ends_with($file, '.blade.php')) {
                $relativePath = str_replace($this->modulDir . '/', '', $file);
                $this->moduleFiles[] = [
                    'path' => $file,
                    'name' => $relativePath,
                    'relativePath' => $relativePath,
                ];
            }
        }
    }

    protected function getAllFiles($dir)
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    public function getModulDir()
    {
        $modulDir = public_path('tema' . DIRECTORY_SEPARATOR . $this->temaAktif . DIRECTORY_SEPARATOR . 'modul');

        // Buat direktori jika belum ada
        if (!File::exists($modulDir)) {
            File::makeDirectory($modulDir, 0755, true);
        }

        return $modulDir;
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            // Form schema kosong karena kita menggunakan editor GrapesJS langsung di view
        ]);
    }

    /**
     * Fungsi untuk menyimpan konten modul ke file
     */
    public function save()
    {
        // Pastikan file yang dipilih ada
        if (!$this->filePath || !File::exists($this->filePath)) {
            Notification::make()
                ->title('File tidak ditemukan')
                ->body('File yang dipilih tidak ditemukan atau tidak valid')
                ->danger()
                ->send();
            return;
        }

        // Ambil nilai dari textarea page_layout
        $content = $this->page_layout;

        // Log konten untuk debugging
        Log::debug('Konten yang akan disimpan:', [
            'filePath' => $this->filePath,
            'panjang' => strlen($content),
            'preview' => substr($content, 0, 100)
        ]);

        // Pastikan konten tidak kosong
        if (!empty($content)) {
            try {
                // Simpan konten ke file
                File::put($this->filePath, $content);

                // Tampilkan notifikasi sukses
                Notification::make()
                    ->title('Modul berhasil disimpan')
                    ->body('File: ' . $this->fileName)
                    ->success()
                    ->send();
            } catch (\Exception $e) {
                // Log error
                Log::error('Error saat menyimpan file', [
                    'filePath' => $this->filePath,
                    'error' => $e->getMessage()
                ]);

                // Tampilkan notifikasi error
                Notification::make()
                    ->title('Gagal menyimpan modul')
                    ->body('Error: ' . $e->getMessage())
                    ->danger()
                    ->send();
            }
        } else {
            // Tampilkan notifikasi error jika konten kosong
            Notification::make()
                ->title('Gagal menyimpan modul')
                ->body('Konten modul kosong')
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save')
                ->label('Simpan')
                ->color('success')
                ->action(function () {
                    // Jika ini adalah permintaan AJAX, gunakan metode handleAjaxSave
                    if (request()->ajax()) {
                        return $this->handleAjaxSave();
                    }

                    // Jika bukan permintaan AJAX, gunakan metode save biasa
                    return $this->save();
                })
                ->disabled(fn() => $this->filePath === null)
                ->extraAttributes([
                    'id' => 'header-save-button',
                    'onclick' => "document.dispatchEvent(new CustomEvent('modul-editor-save'))"
                ]),

            Action::make('refresh')
                ->label('Refresh')
                ->color('gray')
                ->action(fn() => $this->loadModuleFiles()),
        ];
    }
}

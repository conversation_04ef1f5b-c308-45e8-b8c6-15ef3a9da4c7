<?php 

namespace App\Filament\Resources;
 
use Datlechin\FilamentMenuBuilder\Resources\MenuResource as BaseMenuResource;
 
class MenuWebsiteResource extends BaseMenuResource
{    
 
 
 
    // protected static bool $shouldRegisterNavigation = false;
    // protected static ?string $navigationGroup = 'Cms';

    protected static ?int $navigationSort = 3;


    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::count());
    }

    public static function getNavigationGroup(): ?string
    {
        return 'Cms';
    }
}
<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperPrinterSetting
 */
class PrinterSetting extends Model
{
    use HasFactory;

    protected $table = 'printer_settings';

    protected $fillable = [
        'nama_printer',
        'jenis_koneksi',
        'alamat_printer',
        'keterangan',
        'aktif',
        'default',
    ];

    protected $casts = [
        'aktif' => 'boolean',
        'default' => 'boolean',
    ];
    
    /**
     * Mengatur printer ini sebagai default dan menghapus default dari printer lain
     * 
     * @return bool
     */
    public function setAsDefault()
    {
        // Hapus status default dari semua printer
        self::where('default', true)->update(['default' => false]);
        
        // Set printer ini sebagai default
        return $this->update(['default' => true]);
    }
    
    /**
     * Mengambil printer yang diset sebagai default
     * 
     * @return PrinterSetting|null
     */
    public static function getDefault()
    {
        return self::where('default', true)->first();
    }
}
<?php

namespace App\Models;

use App\Traits\PakaiJcol;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Modules\RajaJson\Models\Rajajson;

/**
 * @mixin IdeHelperKonfig
 */
class Konfig extends Model
{

    use PakaiJcol;
    
    public $timestamps = true;

    public function getTable()
    {
        return config('tabel.t_konfig.nama_tabel', 'konfig');
    }


    public function getFillable()
    {
        // Ambil kolom dari konfigurasi dengan nilai default array kosong
        $kolom = config('tabel.t_konfig.kolom', []);

        // Nilai default jika konfigurasi tidak tersedia atau tidak valid
        $defaultFillable = [
            'id',
            'nama',
            'slug',
            'jenis',
            'toko_id',
            'kode',
            'isi',
            'inijson',
            'custom_value',
            'custom_value_isi',
            'isi_json',
            'created_at',
            'updated_at',
        ];

        // Pastikan $kolom adalah array
        if (!is_array($kolom) || empty($kolom)) {
            return $defaultFillable;
        }

        // Hapus kolom 'id' dari fillable karena biasanya ID tidak perlu fillable
        return array_values(array_filter($kolom, function ($item) {
            return $item !== 'id';
        }));
    }

    protected $casts = [

        'json' => 'array',
    ];

 

    public static function bacaJson(string $nama, bool $useCache = true): Collection
    {
        // Buat kunci cache berdasarkan nama konfigurasi
        $cacheKey = "konfig_json:{$nama}";

        // Jika diminta fresh atau tidak ada cache
        if (! $useCache || ! Cache::has($cacheKey)) {
            // Ambil data konfigurasi dari database
            $konfig = self::where('nama', $nama)->first();

            // Jika konfigurasi tidak ditemukan atau isi_json kosong, kembalikan koleksi kosong
            if (! $konfig || empty($konfig->isi_json)) {
                return collect([]);
            }

            // Decode JSON ke array asosiatif
            $jsonData = is_array($konfig->isi_json)
                ? $konfig->isi_json
                : json_decode($konfig->isi_json, true);

            // Jika JSON tidak valid, kembalikan koleksi kosong
            if (json_last_error() !== JSON_ERROR_NONE || ! is_array($jsonData)) {
                return collect([]);
            }

            // Format data berdasarkan struktur JSON (1D, 2D, atau 3D)
            $collection = self::formatDataBerdasarkanStruktur($jsonData, $konfig->inijson);

            // Simpan ke cache selama 1 jam
            if ($useCache) {
                Cache::put($cacheKey, $collection, now()->addHour());
            }

            return $collection;
        }

        // Kembalikan data dari cache
        return Cache::get($cacheKey);
    }


    protected static function formatDataBerdasarkanStruktur($jsonData, $struktur): Collection
    {
        // Untuk array 1D (inijson=1)
        if ($struktur == 1) {
            return collect($jsonData)->map(function ($item, $key) {
                return [
                    'id' => $key,
                    'key' => (string) $key,
                    'value' => $item,
                ];
            });
        }

        // Untuk array 2D/key-value (inijson=2)
        if ($struktur == 2) {
            return collect($jsonData)->map(function ($item, $key) {
                return [
                    'id' => $key,
                    'key' => $key,
                    'value' => $item,
                ];
            });
        }

        // Untuk array 3D/complex (inijson=3)
        if ($struktur == 3) {
            return collect($jsonData);
        }

        // Default: kembalikan data asli sebagai collection
        return collect($jsonData);
    }

    /**
     * Mendapatkan konfigurasi dengan format yang sudah di-cache
     *
     * @param string $nama Nama konfigurasi
     * @param bool $fresh Paksa ambil data baru
     * @return mixed
     */
    public static function getCachedConfig($nama, $fresh = false)
    {
        $cacheKey = "konfig:{$nama}";

        // Jika diminta fresh atau tidak ada cache
        if ($fresh || ! Cache::has($cacheKey)) {
            $konfig = self::where('nama', $nama)->first();

            if (! $konfig) {
                return null;
            }

            $data = $konfig->inijson
                ? json_decode($konfig->isi_json, true)
                : $konfig->isi;

            // Cache selama 1 jam
            Cache::put($cacheKey, $data, now()->addHour());
        }

        return Cache::get($cacheKey);
    }



    public static function isi($nama)
    {
        $default = "-";
        $konfig = self::where('nama', $nama)->first();
        $value = $konfig ? $konfig->isi : $default;

        // Pastikan nilai yang dikembalikan selalu string
        return is_string($value) ? $value : strval($value);
    }


    public static function jsonKu($nama): Collection
    {
        $konfig = self::where('nama', $nama)->first();

        if (! $konfig || empty($konfig->isi_json)) {
            return collect([]);
        }

        // Cek apakah $konfig->isi_json sudah array
        $data = is_array($konfig->isi_json)
            ? $konfig->isi_json
            : json_decode($konfig->isi_json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return collect([]);
        }

        return collect($data)->map(function ($item, $key) {
            return [
                'id' => $key,
                'key' => $key,
                'value' => $item,
                'name' => $item,
                'status' => true
            ];
        });
    }

 

    // Metode validasi JSON
    public function setIsiJsonAttribute($value)
    {
        // Jika value adalah array, proses setiap elemen untuk menangani FileUpload
        if (is_array($value)) {
            // Proses setiap elemen untuk menangani FileUpload
            foreach ($value as $key => $item) {
                // Jika item adalah array dengan format FileUpload (UUID => filename)
                if (is_array($item) && count($item) === 1) {
                    // Ambil nilai pertama dari array
                    $value[$key] = reset($item);
                }
            }

            // Encode array ke JSON
            $this->attributes['isi_json'] = json_encode($value);
        } else {
            // Jika value adalah string JSON, decode, proses, dan encode kembali
            if (is_string($value) && (str_starts_with($value, '{') || str_starts_with($value, '['))) {
                $data = json_decode($value, true);

                // Jika decode berhasil dan hasilnya array
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Proses setiap elemen untuk menangani FileUpload
                    foreach ($data as $key => $item) {
                        // Jika item adalah array dengan format FileUpload (UUID => filename)
                        if (is_array($item) && count($item) === 1) {
                            // Ambil nilai pertama dari array
                            $data[$key] = reset($item);
                        }
                    }

                    // Encode array ke JSON
                    $this->attributes['isi_json'] = json_encode($data);
                    return;
                }
            }

            // Jika bukan array atau JSON valid, simpan apa adanya
            $this->attributes['isi_json'] = $value;
        }
    }
    // Tambahkan mutator untuk json
    public function setJsonAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['json'] = json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        } elseif (is_string($value) && (str_starts_with($value, '{') || str_starts_with($value, '['))) {
            // Jika sudah string JSON, decode dulu lalu encode ulang dengan flag yang benar
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->attributes['json'] = json_encode($decoded, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            } else {
                $this->attributes['json'] = $value;
            }
        } else {
            $this->attributes['json'] = $value;
        }
    }

    public static function jcol($slug, $jsonkey=false)
    {
        $konfig = self::where('slug', $slug)->first();
        if (! $konfig || empty($konfig->json)) {
            return null;
        }

        $data = is_array($konfig->json)
            ? $konfig->json
            : json_decode($konfig->json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return null;
        }

        if ($jsonkey) {
            return $data[$jsonkey] ?? null;
        }

        return collect($data); // Mengembalikan Collection daripada array biasa
    }

    /**
     * Method untuk mendapatkan konfigurasi sebagai object yang bisa diakses dengan property
     * Contoh: $k = Konfig::jcolObject('website'); echo $k->judul;
     */
    public static function jcolObject($slug, $jsonkey=false)
    {
        $konfig = self::where('slug', $slug)->first();
        if (! $konfig || empty($konfig->json)) {
            return (object) [];
        }

        $data = is_array($konfig->json)
            ? $konfig->json
            : json_decode($konfig->json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return (object) [];
        }

        if ($jsonkey) {
            return $data[$jsonkey] ?? null;
        }

        return (object) $data; // Mengembalikan object yang bisa diakses dengan property
    }

    /**
     * Method untuk mendapatkan konfigurasi sebagai array
     * Contoh: $k = Konfig::jcolArray('website'); echo $k['judul'];
     */
    public static function jcolArray($slug, $jsonkey=false)
    {
        $konfig = self::where('slug', $slug)->first();
        if (! $konfig || empty($konfig->json)) {
            return [];
        }

        $data = is_array($konfig->json)
            ? $konfig->json
            : json_decode($konfig->json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return [];
        }

        if ($jsonkey) {
            return $data[$jsonkey] ?? null;
        }

        return $data; // Mengembalikan array biasa
    }

    /**
     * Method untuk mendapatkan konfigurasi sebagai Collection yang dapat menggunakan pluck
     * Contoh: $k = Konfig::jcolCollection('jenis-kategori', 'isi'); $k->pluck('nama');
     * Untuk data array sederhana: $k = Konfig::jcolCollection('jenis-kategori', 'isi'); // Collection dari array
     */
    public static function jcolCollection($slug, $jsonkey=false)
    {
        $konfig = self::where('slug', $slug)->first();
        if (! $konfig || empty($konfig->json)) {
            return collect([]);
        }

        $data = is_array($konfig->json)
            ? $konfig->json
            : json_decode($konfig->json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return collect([]);
        }

        if ($jsonkey) {
            $targetData = $data[$jsonkey] ?? [];

            // Jika data adalah array sederhana (seperti ["ARTIKEL", "WIDGET"])
            if (is_array($targetData) && !empty($targetData) && !is_array(reset($targetData))) {
                // Konversi ke format yang bisa di-pluck: [['value' => 'ARTIKEL'], ['value' => 'WIDGET']]
                return collect($targetData)->map(function($item, $key) {
                    return [
                        'id' => $key,
                        'key' => $key,
                        'value' => $item,
                        'name' => $item,
                        'label' => $item
                    ];
                });
            }

            // Jika data sudah dalam format object/array associative
            return collect($targetData);
        }

        // Jika tidak ada jsonkey, kembalikan seluruh data sebagai Collection
        return collect($data);
    }


    // Metode untuk mendapatkan konfigurasi spesifik dengan tipe
    // public static function getConfigByType($jenis)
    // {
    //     return self::where('jenis', $jenis)->get();
    // }

    // Cara 1: Memodifikasi fungsi jsonKu untuk mengembalikan format asli
    public static function jsonKuRaw($nama)
    {
        $konfig = self::where('nama', $nama)->first();

        if (! $konfig || empty($konfig->isi_json)) {
            return null;
        }

        // Decode JSON ke array asosiatif
        $data = is_array($konfig->isi_json)
            ? $konfig->isi_json
            : json_decode($konfig->isi_json, true);

        if (json_last_error() !== JSON_ERROR_NONE && ! is_array($data)) {
            return null;
        }

        return $data; // Mengembalikan data asli tanpa transformasi
    }

    /**
     * Mendapatkan konfigurasi printer kasir
     *
     * @return array
     */
    public static function dapatkanKonfigurasiPrinter(): array
    {
        $konfig = self::where('nama', 'printer_kasir')->first();

        if (! $konfig || empty($konfig->isi_json)) {
            return [
                'printer_type' => 'windows',
                'printer_name' => 'struk',
            ];
        }

        return is_array($konfig->isi_json)
            ? $konfig->isi_json
            : json_decode($konfig->isi_json, true);
    }

    public static function simpanKonfigurasiPrinter(string $printerType, string $printerName): self
    {
        $konfigurasi = [
            'printer_type' => $printerType,
            'printer_name' => $printerName,
        ];

        return self::updateOrCreate(
            ['nama' => 'printer_kasir'],
            [
                'jenis' => 'sistem',
                'isi_json' => json_encode($konfigurasi),
                'inijson' => true,
            ]
        );
    }


    public static function dapatkanInstansiPrinter()
    {
        $config = self::dapatkanKonfigurasiPrinter();

        if (empty($config['printer_type']) || empty($config['printer_name'])) {
            throw new \Exception("Konfigurasi printer belum diatur.");
        }

        $connector = null;

        switch ($config['printer_type']) {
            case 'windows':
                $connector = new \Mike42\Escpos\PrintConnectors\WindowsPrintConnector($config['printer_name']);
                break;
            case 'network':
                $parts = explode(':', $config['printer_name']);
                $ip = $parts[0];
                $port = $parts[1] ?? 9100;
                $connector = new \Mike42\Escpos\PrintConnectors\NetworkPrintConnector($ip, $port);
                break;
            case 'file':
                $connector = new \Mike42\Escpos\PrintConnectors\FilePrintConnector($config['printer_name']);
                break;
            default:
                throw new \Exception("Tipe printer tidak valid.");
        }

        return new \Mike42\Escpos\Printer($connector);
    }
}

<?php

namespace Modules\Rajapicker\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Exception;

class WebPConverterService
{
    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Konversi gambar ke format WebP
     *
     * @param string $sourcePath Path file sumber
     * @param string $destinationPath Path file tujuan (opsional, jika tidak disediakan akan overwrite)
     * @param array $options Opsi konversi
     * @return array|null Array berisi informasi hasil konversi atau null jika gagal
     */
    public function convertToWebP(string $sourcePath, ?string $destinationPath = null, array $options = []): ?array
    {
        try {
            // Validasi file sumber
            if (!file_exists($sourcePath)) {
                throw new Exception("File sumber tidak ditemukan: {$sourcePath}");
            }

            // Validasi tipe file
            $mimeType = mime_content_type($sourcePath);
            if (!in_array($mimeType, ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'])) {
                throw new Exception("Tipe file tidak didukung untuk konversi WebP: {$mimeType}");
            }

            // Set default options
            $defaultOptions = [
                'quality' => 80,
                'width' => null,
                'height' => null,
                'maintain_aspect_ratio' => true,
                'background_color' => '#FFFFFF',
                'preserve_metadata' => false,
                'compression_method' => 6, // 0-6, semakin tinggi semakin lambat tapi lebih kecil
            ];

            $options = array_merge($defaultOptions, $options);

            // Buat instance Image
            $image = $this->imageManager->read($sourcePath);

            // Resize jika diperlukan
            if ($options['width'] || $options['height']) {
                $image = $this->resizeImage($image, $options);
            }

            // Tentukan path tujuan
            if ($destinationPath === null) {
                $pathInfo = pathinfo($sourcePath);
                $destinationPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
            }

            // Konversi ke WebP dan simpan
            $image->toWebp($options['quality'])->save($destinationPath);

            // Hitung ukuran file
            $originalSize = filesize($sourcePath);
            $webpSize = filesize($destinationPath);
            $compressionRatio = round((($originalSize - $webpSize) / $originalSize) * 100, 2);

            return [
                'success' => true,
                'original_path' => $sourcePath,
                'webp_path' => $destinationPath,
                'original_size' => $this->formatBytes($originalSize),
                'webp_size' => $this->formatBytes($webpSize),
                'compression_ratio' => $compressionRatio,
                'quality' => $options['quality'],
                'dimensions' => [
                    'width' => $image->width(),
                    'height' => $image->height(),
                ]
            ];

        } catch (Exception $e) {
            // Cek apakah Laravel app sudah bootstrap sebelum menggunakan Log
            if (app()->bound('log')) {
                Log::error('WebP conversion failed', [
                    'source_path' => $sourcePath,
                    'destination_path' => $destinationPath,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_path' => $sourcePath,
                'destination_path' => $destinationPath
            ];
        }
    }

    /**
     * Konversi multiple gambar ke WebP
     *
     * @param array $sourcePaths Array path file sumber
     * @param string $destinationDirectory Directory tujuan (opsional)
     * @param array $options Opsi konversi
     * @return array Array hasil konversi untuk setiap file
     */
    public function convertMultipleToWebP(array $sourcePaths, ?string $destinationDirectory = null, array $options = []): array
    {
        $results = [];

        foreach ($sourcePaths as $sourcePath) {
            $destinationPath = null;
            
            if ($destinationDirectory) {
                $filename = pathinfo($sourcePath, PATHINFO_FILENAME) . '.webp';
                $destinationPath = rtrim($destinationDirectory, '/') . '/' . $filename;
            }

            $results[] = $this->convertToWebP($sourcePath, $destinationPath, $options);
        }

        return $results;
    }

    /**
     * Konversi gambar dari URL ke WebP
     *
     * @param string $imageUrl URL gambar
     * @param string $destinationPath Path tujuan
     * @param array $options Opsi konversi
     * @return array|null Hasil konversi
     */
    public function convertUrlToWebP(string $imageUrl, string $destinationPath, array $options = []): ?array
    {
        try {
            // Download gambar dari URL
            $tempPath = $this->downloadImage($imageUrl);
            
            if (!$tempPath) {
                throw new Exception("Gagal mengunduh gambar dari URL: {$imageUrl}");
            }

            // Konversi ke WebP
            $result = $this->convertToWebP($tempPath, $destinationPath, $options);

            // Hapus file temporary
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }

            return $result;

        } catch (Exception $e) {
            // Cek apakah Laravel app sudah bootstrap sebelum menggunakan Log
            if (app()->bound('log')) {
                Log::error('URL to WebP conversion failed', [
                    'image_url' => $imageUrl,
                    'destination_path' => $destinationPath,
                    'error' => $e->getMessage()
                ]);
            }

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'image_url' => $imageUrl,
                'destination_path' => $destinationPath
            ];
        }
    }

    /**
     * Resize gambar sesuai opsi yang diberikan
     *
     * @param \Intervention\Image\Image $image Instance gambar
     * @param array $options Opsi resize
     * @return \Intervention\Image\Image
     */
    private function resizeImage($image, array $options)
    {
        $width = $options['width'];
        $height = $options['height'];
        $maintainAspectRatio = $options['maintain_aspect_ratio'];
        $backgroundColor = $options['background_color'];

        if ($width && $height) {
            if ($maintainAspectRatio) {
                $image->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            } else {
                $image->resize($width, $height);
            }
        } elseif ($width) {
            $image->resize($width, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        } elseif ($height) {
            $image->resize(null, $height, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        return $image;
    }

    /**
     * Download gambar dari URL
     *
     * @param string $url URL gambar
     * @return string|false Path file temporary atau false jika gagal
     */
    private function downloadImage(string $url)
    {
        try {
            $tempPath = tempnam(sys_get_temp_dir(), 'webp_converter_');
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $imageData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200 || !$imageData) {
                throw new Exception("Gagal mengunduh gambar. HTTP Code: {$httpCode}");
            }

            file_put_contents($tempPath, $imageData);
            
            return $tempPath;

        } catch (Exception $e) {
            // Cek apakah Laravel app sudah bootstrap sebelum menggunakan Log
            if (app()->bound('log')) {
                Log::error('Image download failed', [
                    'url' => $url,
                    'error' => $e->getMessage()
                ]);
            }
            
            return false;
        }
    }

    /**
     * Format bytes ke human readable format
     *
     * @param int $bytes Jumlah bytes
     * @param int $precision Presisi desimal
     * @return string
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Cek apakah WebP didukung oleh server
     *
     * @return bool
     */
    public function isWebPSupported(): bool
    {
        return extension_loaded('gd') && function_exists('imagewebp');
    }

    /**
     * Cek apakah file adalah gambar yang valid
     *
     * @param string $filePath Path file
     * @return bool
     */
    public function isValidImage(string $filePath): bool
    {
        if (!file_exists($filePath)) {
            return false;
        }

        $mimeType = mime_content_type($filePath);
        $supportedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];

        return in_array($mimeType, $supportedTypes);
    }

    /**
     * Dapatkan informasi gambar
     *
     * @param string $filePath Path file
     * @return array|null
     */
    public function getImageInfo(string $filePath): ?array
    {
        try {
            if (!file_exists($filePath)) {
                return null;
            }

            $image = $this->imageManager->read($filePath);
            
            $info = [
                'width' => $image->width(),
                'height' => $image->height(),
                'mime_type' => mime_content_type($filePath),
                'file_size' => $this->formatBytes(filesize($filePath)),
                'file_size_bytes' => filesize($filePath),
                'aspect_ratio' => round($image->width() / $image->height(), 2),
            ];

            return $info;

        } catch (Exception $e) {
            // Cek apakah Laravel app sudah bootstrap sebelum menggunakan Log
            if (app()->bound('log')) {
                Log::error('Failed to get image info', [
                    'file_path' => $filePath,
                    'error' => $e->getMessage()
                ]);
            }

            return null;
        }
    }

    /**
     * Konversi file upload ke WebP (khusus untuk RajaPicker)
     *
     * @param \Illuminate\Http\UploadedFile $uploadedFile File yang diupload
     * @param array $options Opsi konversi
     * @return array|null Hasil konversi
     */
    public function convertUploadedFileToWebP($uploadedFile, array $options = []): ?array
    {
        try {
            // Validasi file
            if (!$uploadedFile->isValid()) {
                throw new Exception("File upload tidak valid");
            }

            // Validasi tipe file
            $mimeType = $uploadedFile->getMimeType();
            if (!in_array($mimeType, ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'])) {
                throw new Exception("Tipe file tidak didukung untuk konversi WebP: {$mimeType}");
            }

            // Set default options
            $defaultOptions = [
                'quality' => 80,
                'width' => null,
                'height' => null,
                'maintain_aspect_ratio' => true,
                'background_color' => '#FFFFFF',
                'preserve_metadata' => false,
                'compression_method' => 6,
            ];

            $options = array_merge($defaultOptions, $options);

            // Buat instance Image dari file upload
            $image = $this->imageManager->read($uploadedFile->getRealPath());

            // Resize jika diperlukan
            if ($options['width'] || $options['height']) {
                $image = $this->resizeImage($image, $options);
            }

            // Buat temporary file untuk WebP
            $tempDir = sys_get_temp_dir();
            $webpFileName = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME) . '_' . time() . '.webp';
            $webpPath = $tempDir . '/' . $webpFileName;

            // Konversi ke WebP dan simpan
            $image->toWebp($options['quality'])->save($webpPath);

            // Hitung ukuran file
            $originalSize = $uploadedFile->getSize();
            $webpSize = filesize($webpPath);
            $compressionRatio = round((($originalSize - $webpSize) / $originalSize) * 100, 2);

            return [
                'success' => true,
                'original_path' => $uploadedFile->getRealPath(),
                'webp_path' => $webpPath,
                'webp_filename' => $webpFileName,
                'original_size' => $this->formatBytes($originalSize),
                'webp_size' => $this->formatBytes($webpSize),
                'compression_ratio' => $compressionRatio,
                'quality' => $options['quality'],
                'dimensions' => [
                    'width' => $image->width(),
                    'height' => $image->height(),
                ]
            ];

        } catch (Exception $e) {
            // Cek apakah Laravel app sudah bootstrap sebelum menggunakan Log
            if (app()->bound('log')) {
                Log::error('WebP conversion failed for uploaded file', [
                    'original_name' => $uploadedFile->getClientOriginalName(),
                    'mime_type' => $uploadedFile->getMimeType(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_name' => $uploadedFile->getClientOriginalName(),
                'mime_type' => $uploadedFile->getMimeType()
            ];
        }
    }
} 
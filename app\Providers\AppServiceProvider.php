<?php

namespace App\Providers;


use App\Components\BackupDestinationListRecordsWithRestore;
use App\Livewire\BackupTerminal;
use App\Services\LayananPrinter;
use Filament\Support\Assets\Js;
use Filament\Support\Facades\FilamentAsset;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Config;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;

class AppServiceProvider extends ServiceProvider
{
  /**
   * Register any application services.
   */
  public function register(): void
  {
    $loader = \Illuminate\Foundation\AliasLoader::getInstance();

    $this->app->singleton(LayananPrinter::class, function ($app) {
      return new LayananPrinter();
    });

  }


  public function boot(): void {
    // Daftarkan komponen Livewire kustom
    Livewire::component('backup-destination-list-records-with-restore', BackupDestinationListRecordsWithRestore::class);
    Livewire::component('backup-terminal', BackupTerminal::class);

   Gate::before(function ($user, $ability) {
        return $user->hasRole('super_admin') ? true : null;
    });
    // Daftarkan file JavaScript kustom untuk JsonColumn
    FilamentAsset::register([
        Js::make('custom-json-column', public_path('js/custom-json-column.js')),
    ]);

    // Nonaktifkan sanitasi HTML untuk Filament jika class ada
    // Kita akan menggunakan pendekatan lain untuk mengatasi masalah sanitasi HTML
    // Lihat DisableHtmlSanitization middleware

    // Register Filament page navigation after app is loaded
    Filament::serving(function () {
        // Force register Tema navigation
        Filament::registerNavigationGroups([
            NavigationGroup::make()
                ->label('Cms')
                ->collapsed(false),
        ]);
    });
  }
}

<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;


use \Guava\Tutorials\Selectors\Selector;
use App\Aplikasi\Hotel\Forms\Components\PengirimField;
use App\Aplikasi\Hotel\Forms\Components\PilihHarga;
use App\Aplikasi\Hotel\Forms\Components\PilihKamar;
use App\Aplikasi\Hotel\Forms\Components\PilihTamu;
use App\Aplikasi\Hotel\Livewire\CariFasilitas;
use App\Aplikasi\Hotel\Models\Fasilitas;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Models\MetodePembayaran;
use App\Aplikasi\Hotel\Models\Pembayaran;

use App\Aplikasi\Hotel\Models\Penjualan;
use App\Aplikasi\Hotel\Models\Transaksi;

use App\Aplikasi\Hotel\Resources\ReservasiResource;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Widgets\FasilitasWidget;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Widgets\KeranjangWidget;
use App\Aplikasi\Hotel\Services\ReservasiKeranjangService;
use App\Filament\Forms\Components\PembayaranForm;
use App\Filament\Forms\Components\Rupiah;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;

use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;

use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
 
use Guava\Tutorials\Contracts\HasTutorials;
use Guava\Tutorials\Selectors\ComponentSelector;
use Guava\Tutorials\Steps\Step;
use Guava\Tutorials\Tutorial;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Log;
use Str;


class CreateReservasi extends CreateRecord 
{
   

    protected static string $resource = ReservasiResource::class;
    // Properti untuk proses pembayaran
    public $metodePembayaran = null;
    public $totalPembayaran = 0;
    public $jumlahBayar = 0;
    public $kembalian = 0;
    public $totalKembalian = 0;
    public $catatanTransaksi = '';
    public $cetakStruk = true;
    public $totalAkhir = 0;
    public bool $isExpertMode = false;


    protected static ?string $createActionLabel = 'Tambah Reservasi';

    public function mount(): void
    {
        parent::mount();
    }



    public function form(Form $form): Form
    {


        $biayaLayanan = Konfig::jsonKuRaw('biaya_layanan');
        return $form

            ->statePath('data')
            ->schema([
                Grid::make()->columns(2)->columnSpan(2)
                    ->schema([
                        Section::make()->columns(2)->columnSpan(2)
                            ->schema([

                                DateTimePicker::make('check_in')
                                    ->label('Tanggal & Jam Check In')
                                    ->required()
                                    ->seconds(false)
                                    ->native(false)
                                    ->default(Carbon::now()->setTime(14, 0, 0))
                                    ->reactive(),

                                DateTimePicker::make('check_out')
                                    ->label('Tanggal & Jam Check Out')
                                    ->required()
                                    ->native(false)
                                    ->seconds(false)
                                    ->default(Carbon::now()->addDay()->setTime(12, 0, 0))
                                    ->reactive()
                                    ,




                                PilihTamu::make('tamu_id')
                                    ->live()
                                    ->required()
                                    ->visible($this->getVisibleWhen(fn(Get $get) => $get('kamar_id'))),

                                PilihHarga::make('harga_id')
                                    ->visible($this->getVisibleWhen(fn(Get $get) => $get('kamar_id'))),
                                PembayaranForm::make()
                                    ->visible($this->getVisibleWhen(fn(Get $get) => $get('tamu_id')))
                                    ->afterStateUpdated(function () {
                                        $this->hitungTotalPembayaran();
                                        $jumlah = $state['pembayaran'][0]['jumlah'] ?? 0;
                                        $this->dispatch('pembayaranUpdated', jumlah: $jumlah);
                                    }),




                            ])->columns(2)->columnSpan(2),




                        Section::make('Referensi')
                            ->visible($this->getVisibleWhen(fn(Get $get) => $get('tamu_id')))
                            ->collapsible()->collapsed()
                            ->schema([
                                Select::make('referensi')
                                    ->label('Referensi')
                                    ->options(function () {
                                        $konfig = Konfig::where('nama', 'referensi')->first();
                                        return json_decode($konfig->isi_json, true);
                                    })
                                    ->preload()
                                    ->searchable()
                                    ->placeholder('Pilih Referensi')

                                    ->createOptionForm([
                                        TextInput::make('name')
                                            ->label('Nama Referensi')
                                            ->required()

                                            ->maxLength(255)
                                            ->helperText('Referensi baru akan otomatis ditambahkan ke daftar')
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $konfig = Konfig::where('nama', 'referensi')->first();
                                        $existingOptions = json_decode($konfig->isi_json, true);
                                        $newValue = strtoupper($data['name']);
                                        $existingOptions[] = $newValue;
                                        $konfig->update([
                                            'isi_json' => json_encode($existingOptions)
                                        ]);

                                        return $newValue;
                                    })
                                    ->createOptionModalHeading('Tambah Referensi Baru')
                                    ->live(),

                                Textarea::make('ket_referensi')
                                    ->label('Keterangan Referensi')
                                    ->placeholder('Masukkan keterangan untuk referensi')
                                    ->rows(3)
                                    ->visible(fn(Get $get): bool => filled($get('referensi')))
                                    ->columnSpan(1),



                            ])->columns(2)->columnSpan(2),

                        Radio::make('status_reservasi')
                            ->visible($this->getVisibleWhen(fn(Get $get) => $get('kamar_id')))
                            ->label('Status Reservasi')
                            ->options([
                                'SCI' => 'CHECK IN',
                                'BK' => 'BOKING',
                            ])
                            ->inline()
                            ->required()
                            ->live()
                            ->columnSpan(2)
                            ->extraAttributes([
                                'class' => 'reservasi-radio-button',
                            ]),




                    ]),


                Grid::make()->columns(1)->columnSpan(1)
                    ->schema([

                        Livewire::make(KeranjangWidget::class)->columnSpanFull()
                            ->visible(fn(Get $get) => $get('kamar_id')),

                        PilihKamar::make('kamar_id')
                            ->id('tutorial_pilihkamar')
                            ->label('')
                            ->live()
                            ->required()
                            ->columnSpanFull(),


                        ViewField::make('fasilitas')
                            ->view('hotel::reservasi.cari-fasilitas')
                            ->visible($this->getVisibleWhen(fn(Get $get) => $get('kamar_id')))
                            ->viewData(['produk' => Fasilitas::where('tampil', 1)->get()]),
                        // Livewire::make(FasilitasWidget::class)

                        // ->columnSpanFull(),


                        Section::make('Diskon')->collapsible()->collapsed()
                            ->visible($this->getVisibleWhen(fn(Get $get) => $get('kamar_id')))
                            ->compact()
                            ->schema([
                                TextInput::make('pajak')
                                    ->numeric()
                                    ->label('Pajak %')
                                    ->extraInputAttributes(['class' => 'text-xs  h-8'])
                                    ->disabled()
                                    ->default($biayaLayanan['pajak'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        // Simpan nilai ke session
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['pajak'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        // Gunakan $livewire untuk dispatch event
                                        $livewire->dispatch('keranjang-updated');
                                    }),

                                TextInput::make('servis')
                                    ->numeric()
                                    ->label('Servis %')
                                    ->extraInputAttributes(['class' => 'text-xs   h-8'])
                                    ->disabled()
                                    ->default($biayaLayanan['servis'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['servis'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        $livewire->dispatch('keranjang-updated');
                                    }),

                                TextInput::make('diskon')
                                    ->numeric()
                                    ->label('Diskon %')
                                    ->extraInputAttributes(['class' => 'text-xs  h-8'])
                                    ->default($biayaLayanan['diskon'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['diskon'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        $livewire->dispatch('keranjang-updated');
                                    }),
                            ])->columns(3)->columnSpan(1),






                        Hidden::make('no_invoice')->default('INVH#' . uniqid()),
                        Hidden::make('karyawan_id')->default(Auth::user()->id),
                    ]),







            ])->columns(3);
    }

    protected function beforeCreate(): void
    {
        // dd($this->data);
    }

    protected function afterCreate(): void
    {
        // Ambil data dari session
        $keranjangBelanja = Session::get('keranjang_hotel', collect());
        $biayaTambahan = Session::get('biaya_tambahan', [
            'diskon' => 0,
            'pajak' => 0,
            'servis' => 0
        ]);

        // Ambil data dari form
        $data = $this->data;
        $record = $this->record;

        // dd($data);

        DB::beginTransaction();
        try {
            // Buat record Penjualan dengan detail dari session
            $record->update([


                'diskon' => $biayaTambahan['diskon'] ?? 0,
                'pajak' => $biayaTambahan['pajak'] ?? 0,
                'servis' => $biayaTambahan['servis'] ?? 0,
                // 'karyawan_id' => auth()->user()->id,



            ]);

            // Proses setiap item dalam keranjang
            foreach ($keranjangBelanja as $item) {
                $transaksi = Transaksi::create([
                    'reservasi_id' => $record->id,
                    'produk_id' => $item['produk_id'],
                    'nama_item' => $item['nama_item'],
                    'harga' => $item['harga'],
                    'harga_modal' => $item['harga_modal'],
                    'jumlah' => $item['jumlah'],
                    'jenis' => $item['jenis'] ?? '-',
                    'transaksi_invoice' => $record->no_invoice
                ]);
            }



            DB::commit();

            // Bersihkan session
            Session::forget('keranjang_hotel');
            Session::forget('biaya_tambahan');
        } catch (\Exception $e) {
            DB::rollBack();

            \Filament\Notifications\Notification::make()
                ->title('Gagal Membuat Reservasi')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();

            // Log error untuk debugging
            // \Log::error('Reservasi Creation Error: '.$e->getMessage());
        }
    }


 

    public function bukaTutorial()
    {

        $this->mount($this->mountTutorial());
    }


    public function hitungTotalPembayaran()
    {
        $this->totalPembayaran = 0;

        if (isset($this->data['pembayaran']) && is_array($this->data['pembayaran'])) {
            foreach ($this->data['pembayaran'] as $pembayaran) {
                if (isset($pembayaran['jumlah']) && ! empty($pembayaran['jumlah'])) {
                    // Handle berbagai format nilai pembayaran
                    $jumlahBayar = $pembayaran['jumlah'];

                    // Jika dalam bentuk string (format Rupiah)
                    if (is_string($jumlahBayar)) {
                        // Hapus Rp, titik sebagai pemisah ribuan, dan ubah koma ke titik untuk desimal
                        $jumlahBayar = floatval(str_replace(['Rp', '.', ','], ['', '', '.'], $jumlahBayar));
                    } else {
                        $jumlahBayar = floatval($jumlahBayar);
                    }

                    // Tambahkan ke total pembayaran jika nilai valid
                    if ($jumlahBayar > 0) {
                        $this->totalPembayaran += $jumlahBayar;
                    }
                }
            }
        }

        // Hitung kembalian
        $this->totalKembalian = max(0, $this->totalPembayaran - $this->totalAkhir);

        // Simpan nilai untuk komponen FooterPembayaran
        session(['total_pembayaran_temp' => (int)$this->totalPembayaran]);

        // Dispatch event untuk memperbarui footer
        $this->dispatch('pembayaran-updated');
    }

    protected function processJsonField($field)
    {
        if (is_string($field) && $this->isJson($field)) {
            return json_decode($field, true);
        }
        return $field ?? [];
    }

    protected function isJson($string)
    {
        if (! is_string($string))
            return false;
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    public function getKamarTersedia($checkIn, $checkOut)
    {
        if (empty($checkIn) || empty($checkOut)) {
            return collect([]);
        }

        $checkInDate = Carbon::parse($checkIn);
        $checkOutDate = Carbon::parse($checkOut);

        $tipeKamars = KamarTipe::all();
        $result = collect();

        foreach ($tipeKamars as $tipe) {
            $kamars = Kamar::where('kategori_id', $tipe->id)->get();

            if ($kamars->count() > 0) {
                // Ambil informasi dari kamar pertama
                $firstKamar = $kamars->first();
                $spesifikasi = $this->processJsonField($firstKamar->spesifikasi);

                // Transformasi koleksi kamar
                $mappedKamars = $kamars->map(function ($kamar) use ($checkInDate, $checkOutDate) {
                    return [
                        'id' => $kamar->id,
                        'nama' => $kamar->nama,
                        'tersedia' => $kamar->isAvailable($checkInDate, $checkOutDate),
                    ];
                });

                $result->push([
                    'tipe_id' => $tipe->id,
                    'tipe_nama' => $tipe->nama,
                    'harga' => $firstKamar->harga, // Mengambil harga dari kamar pertama
                    'fasilitas' => $firstKamar->fasilitas,
                    'spesifikasi' => $spesifikasi,
                    'kamars' => $mappedKamars
                ]);
            }
        }

        return $result;
    }

    public static function getWidgets(): array
    {
        return [
            KeranjangWidget::class,
        ];
    }


    public function tambahKeKeranjang(int $kamarId, string $checkIn, string $checkOut): void
    {
        $service = app(ReservasiKeranjangService::class);
        $service->tambahKamar($kamarId, $checkIn, $checkOut);
    }

    public function tambahKeKeranjangFasilitas($produkId)
    {
        $service = app(ReservasiKeranjangService::class);
        $service->tambahFasilitas($produkId);

        Notification::make()
            ->title('berhasil ditambahkan')
            ->success()
            ->send();
        // Tutup modal setelah menambahkan produk
        $this->dispatch('keranjang-updated');
        $this->dispatch('close-modal', id: 'carifasilitas');
    }
    public function updateHargaKamar(int $kamarId, int $harga): void
    {
        $service = app(ReservasiKeranjangService::class);
        $service->updateHargaKamar($kamarId, $harga);
        $this->dispatch('keranjang-updated');
    }


    // [DIUBAH] Mendaftarkan listener yang memicu event biaya-tambahan-updated
    public function setupBiayaTambahanListeners(): void
    {
        $this->registerListeners([
            'data.pajak' => [
                function () {
                    $this->dispatchBiayaTambahan();
                    $this->dispatch('keranjang-updated');
                },
            ],
            'data.servis' => [
                function () {
                    $this->dispatchBiayaTambahan();
                    $this->dispatch('keranjang-updated');
                },
            ],
            'data.diskon' => [
                function () {
                    $this->dispatchBiayaTambahan();
                    $this->dispatch('keranjang-updated');
                },
            ],
        ]);
    }

    protected function dispatchBiayaTambahan(): void
    {
        $formData = $this->data ?? [];

        $this->dispatch('biaya-tambahan-updated', [
            'pajak' => $formData['pajak'] ?? 0,
            'servis' => $formData['servis'] ?? 0,
            'diskon' => $formData['diskon'] ?? 0,
        ]);
    }

    protected function afterMount(): void
    {
        // Inisialisasi nilai biaya tambahan dari data form
        $formData = $this->data ?? [];

        session(['biaya_tambahan' => [
            'pajak' => $formData['pajak'] ?? 0,
            'servis' => $formData['servis'] ?? 0,
            'diskon' => $formData['diskon'] ?? 0,
        ]]);
    }
    // Di method mount() (tambahkan jika belum ada)


    public function boot()
    {

        $this->isExpertMode = session()->get('expert_mode', false);


        // Inisialisasi nilai biaya tambahan dari konfigurasi
        $biayaLayanan = Konfig::jsonKuRaw('biaya_layanan');

        // Simpan ke session
        session(['biaya_tambahan' => [
            'pajak' => $biayaLayanan['pajak'] ?? 0,
            'servis' => $biayaLayanan['servis'] ?? 0,
            'diskon' => $biayaLayanan['diskon'] ?? 0,
        ]]);
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('create')
                ->label('SIMPAN RESERVASI')
                ->icon('heroicon-o-plus')
                ->action('create')
                // ->visible(fn() => isset($this->data['kamar_id']))
                ->visible($this->getVisibleWhen(fn() => isset($this->data['kamar_id'])))
                ->extraAttributes(['class' => 'bg-primary-500 hover:bg-primary-600 text-white']),
        ];
    }
    // Menonaktifkan tombol Create Another
    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    // Menonaktifkan tombol Cancel
    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->hidden();
    }




    // Helper method untuk kondisi visible
    public function getVisibleWhen(callable $condition): callable
    {
        return function (Get $get) use ($condition) {
            if ($this->isExpertMode) {
                return true;
            }

            return $condition($get);
        };
    }

    // Tambahkan watcher untuk isExpertMode
    public function updatedIsExpertMode($value): void
    {
        // Simpan nilai ke session
        session()->put('expert_mode', $value);
    }
    // Method untuk toggle mode expert
    public function toggleExpertMode(): void
    {
        $this->isExpertMode = !$this->isExpertMode;
        session()->put('expert_mode', $this->isExpertMode);
    }

    // Perbarui method getHeader()
    public function getHeader(): ?View
    {
        return view('hotel::reservasi.header', [
            'isExpertMode' => $this->isExpertMode,
        ]);
    }


    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('invoice', ['record' => $this->record]);
    }
}

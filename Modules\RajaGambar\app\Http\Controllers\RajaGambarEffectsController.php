<?php

namespace Modules\RajaGambar\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RajaGambar\Services\RajaGambarService;
use Illuminate\Support\Facades\Validator;
use Exception;

class RajaGambarEffectsController extends Controller
{
    protected $rajaGambarService;

    public function __construct(RajaGambarService $rajaGambarService)
    {
        $this->rajaGambarService = $rajaGambarService;
    }

    /**
     * Set background color
     */
    public function background(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->background($request->color)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Background gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'background_color' => $request->color ?? '#ffffff'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add border
     */
    public function border(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'nullable|integer|min:1|max:100',
            'border_type' => 'nullable|string|in:overlay,shrink,expand',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->border($request->width, $request->border_type, $request->color)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Border berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'border_width' => $request->width ?? 1,
                    'border_type' => $request->border_type ?? 'overlay',
                    'border_color' => $request->color ?? '#000000'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rotate gambar
     */
    public function orientation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'orientation' => 'required|string|in:rotate0,rotate90,rotate180,rotate270',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->orientation($request->orientation)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Orientasi gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'orientation' => $request->orientation
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Flip gambar
     */
    public function flip(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'direction' => 'required|string|in:horizontal,vertical,both',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->flip($request->direction)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-flip',
                'data' => [
                    'url' => $result,
                    'flip_direction' => $request->direction
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add watermark
     */
    public function watermark(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'watermark' => 'required|file|image|max:5120',
            'position' => 'nullable|string|in:top-left,top-right,bottom-left,bottom-right,center',
            'opacity' => 'nullable|integer|min:1|max:100',
            'padding' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            $watermarkPath = $request->file('watermark')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->watermark($watermarkPath, $request->position, $request->opacity, $request->padding)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Watermark berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'watermark_position' => $request->position ?? 'bottom-right',
                    'watermark_opacity' => $request->opacity ?? 50,
                    'watermark_padding' => $request->padding ?? 10
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add text overlay
     */
    public function text(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'text' => 'required|string|max:255',
            'position' => 'nullable|string|in:top-left,top-right,bottom-left,bottom-right,center',
            'font_size' => 'nullable|integer|min:8|max:72',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'padding' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->text($request->text, $request->position, $request->font_size, $request->color, $request->padding)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Text overlay berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'text' => $request->text,
                    'text_position' => $request->position ?? 'bottom-right',
                    'font_size' => $request->font_size ?? 16,
                    'text_color' => $request->color ?? '#000000',
                    'text_padding' => $request->padding ?? 10
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize gambar
     */
    public function optimize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil dioptimasi',
                'data' => [
                    'url' => $result
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }
}

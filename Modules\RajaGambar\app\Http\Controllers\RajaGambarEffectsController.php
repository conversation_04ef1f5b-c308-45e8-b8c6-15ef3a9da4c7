<?php

namespace Modules\RajaGambar\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RajaGambar\Services\RajaGambarService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class RajaGambarEffectsController extends Controller
{
    protected $rajaGambarService;

    public function __construct(RajaGambarService $rajaGambarService)
    {
        $this->rajaGambarService = $rajaGambarService;
    }

    /**
     * Set background color
     */
    public function background(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->background($request->color)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Background gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'background_color' => $request->color ?? '#ffffff'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add border
     */
    public function border(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'nullable|integer|min:1|max:100',
            'border_type' => 'nullable|string|in:overlay,shrink,expand',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->border($request->width, $request->border_type, $request->color)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Border berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'border_width' => $request->width ?? 1,
                    'border_type' => $request->border_type ?? 'overlay',
                    'border_color' => $request->color ?? '#000000'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rotate gambar
     */
    public function orientation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'orientation' => 'required|string|in:rotate0,rotate90,rotate180,rotate270',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->orientation($request->orientation)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Orientasi gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'orientation' => $request->orientation
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Flip gambar
     */
    public function flip(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'direction' => 'required|string|in:horizontal,vertical,both',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->flip($request->direction)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-flip',
                'data' => [
                    'url' => $result,
                    'flip_direction' => $request->direction
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add watermark
     */
    public function watermark(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'watermark' => 'required|file|image|max:5120',
            'position' => 'nullable|string|in:top-left,top-right,bottom-left,bottom-right,center',
            'opacity' => 'nullable|integer|min:1|max:100',
            'padding' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            $watermarkPath = $request->file('watermark')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->watermark($watermarkPath, $request->position, $request->opacity, $request->padding)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Watermark berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'watermark_position' => $request->position ?? 'bottom-right',
                    'watermark_opacity' => $request->opacity ?? 50,
                    'watermark_padding' => $request->padding ?? 10
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add text overlay
     */
    public function text(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'text' => 'required|string|max:255',
            'position' => 'nullable|string|in:top-left,top-right,bottom-left,bottom-right,center',
            'font_size' => 'nullable|integer|min:8|max:72',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'padding' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->text($request->text, $request->position, $request->font_size, $request->color, $request->padding)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Text overlay berhasil ditambahkan',
                'data' => [
                    'url' => $result,
                    'text' => $request->text,
                    'text_position' => $request->position ?? 'bottom-right',
                    'font_size' => $request->font_size ?? 16,
                    'text_color' => $request->color ?? '#000000',
                    'text_padding' => $request->padding ?? 10
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize gambar
     */
    public function optimize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil dioptimasi',
                'data' => [
                    'url' => $result
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload dan proses gambar untuk RajaUpload component
     */
    public function uploadProcess(Request $request): JsonResponse
    {
        // Debug logging
        Log::info('RajaUpload: Upload process started', [
            'request_data' => $request->all(),
            'files' => $request->allFiles(),
            'headers' => $request->headers->all()
        ]);

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|image|max:10240',
            'directory' => 'nullable|string',
            'auto_optimize' => 'nullable|boolean',
            'auto_resize' => 'nullable|json',
            'watermark_config' => 'nullable|json',
            'custom_effects' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            Log::error('RajaUpload: Validation failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $directory = $request->input('directory', 'raja-processed');
            $autoOptimize = $request->boolean('auto_optimize', false);
            $autoResize = $request->input('auto_resize') ? json_decode($request->input('auto_resize'), true) : null;
            $watermarkConfig = $request->input('watermark_config') ? json_decode($request->input('watermark_config'), true) : null;
            $customEffects = $request->input('custom_effects') ? json_decode($request->input('custom_effects'), true) : [];

            // Store original file temporarily
            $originalPath = $file->store('temp', 'public');
            $fullPath = storage_path('app/public/' . $originalPath);

            // Start processing
            $service = $this->rajaGambarService->load($fullPath);

            // Apply auto resize if configured
            if ($autoResize) {
                if (isset($autoResize['fit_method']) && $autoResize['fit_method']) {
                    $service->fit($autoResize['width'], $autoResize['height'], $autoResize['fit_method']);
                } else {
                    $service->resize($autoResize['width'], $autoResize['height']);
                }
            }

            // Apply custom effects
            foreach ($customEffects as $effect => $params) {
                switch ($effect) {
                    case 'brightness':
                        $service->brightness($params);
                        break;
                    case 'contrast':
                        $service->contrast($params);
                        break;
                    case 'gamma':
                        $service->gamma($params);
                        break;
                    case 'colorize':
                        $service->colorize($params['red'], $params['green'], $params['blue']);
                        break;
                    case 'background':
                        $service->background($params);
                        break;
                    case 'border':
                        $service->border($params['width'] ?? 1, $params['type'] ?? 'overlay', $params['color'] ?? '#000000');
                        break;
                    case 'orientation':
                        $service->orientation($params);
                        break;
                    case 'flip':
                        $service->flip($params);
                        break;
                }
            }

            // Apply watermark if configured
            if ($watermarkConfig) {
                $service->watermark(
                    $watermarkConfig['path'],
                    $watermarkConfig['position'] ?? 'bottom-right',
                    $watermarkConfig['opacity'] ?? 50,
                    $watermarkConfig['padding'] ?? 10
                );
            }

            // Apply optimization if enabled
            if ($autoOptimize) {
                $service->optimize();
            }

            // Generate processed filename
            $extension = $file->getClientOriginalExtension();
            $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $processedFilename = $filename . '_processed_' . time() . '.' . $extension;
            $processedPath = $directory . '/' . $processedFilename;
            $fullProcessedPath = storage_path('app/public/' . $processedPath);

            // Ensure directory exists
            $processedDir = dirname($fullProcessedPath);
            if (!file_exists($processedDir)) {
                mkdir($processedDir, 0755, true);
            }

            // Save processed image
            $result = $service->save($fullProcessedPath);

            // Clean up temporary file
            Storage::disk('public')->delete($originalPath);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Gambar berhasil diupload dan diproses',
                    'data' => [
                        'url' => $processedPath,
                        'full_url' => asset('storage/' . $processedPath),
                        'filename' => $processedFilename,
                        'directory' => $directory,
                        'processed' => true,
                        'auto_optimize' => $autoOptimize,
                        'auto_resize' => $autoResize,
                        'watermark_applied' => !empty($watermarkConfig),
                        'effects_applied' => count($customEffects)
                    ]
                ]);
            } else {
                throw new Exception('Gagal menyimpan gambar yang diproses');
            }

        } catch (Exception $e) {
            // Clean up temporary file if exists
            if (isset($originalPath)) {
                Storage::disk('public')->delete($originalPath);
            }

            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Aplikasi\Hotel\Forms\Components;

use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Filament\Forms\Components\Rupiah;
use App\Models\HargaJual;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;

class PilihTamu
{
    public static function make(string $name): Select
    {
        return  Select::make('tamu_id')
            // ->default(1)

            ->label('Pilih Tamu')
            ->live()
            ->relationship('tamu', 'nama')
            ->getOptionLabelFromRecordUsing(
                fn($record) =>
                "{$record->nama} • {$record->telpon}  "
            )
            ->searchable(['nama', 'telpon', 'email'])
            ->preload()
            ->createOptionForm([
                Grid::make(2)
                    ->schema([
                        TextInput::make('nama')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('email')
                            ->email()
                            ->maxLength(255),
                        TextInput::make('telpon')
                            ->tel()
                            ->maxLength(255),
                        Select::make('jenis_identitas')
                            ->options(Konfig::jsonKuRaw('jenis_identitas'))
                            ,
                        TextInput::make('no_identitas')
                            ->maxLength(100),
                        FileUpload::make('foto_identitas')
                            ->directory('tamu'),
                    ]),



            ])->allowHtml()
            ->optionsLimit(15)
            ->required();
    }
}

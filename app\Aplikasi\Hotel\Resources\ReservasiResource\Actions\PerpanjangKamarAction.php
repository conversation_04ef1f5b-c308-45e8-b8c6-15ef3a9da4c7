<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Actions;

use App\Aplikasi\Hotel\Forms\Components\PengirimField;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Models\Pembayaran;
use App\Aplikasi\Hotel\Models\MetodePembayaran;
use App\Aplikasi\Hotel\Models\Transaksi;
use App\Filament\Forms\Components\Rupiah;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Section as FormsSection;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use NunoMaduro\Collision\Adapters\Phpunit\State;

class PerpanjangKamarAction
{
    public static function make(): Action
    {
        return Action::make('perpanjangKamar')

            ->label('Perpanjang Kamar')
            // ->color('secondary')
            ->icon('heroicon-o-calendar')
            ->modalWidth('lg')
            ->modalHeading('Perpanjang Kamar')
            ->form([
                DateTimePicker::make('check_out_baru')
                    ->label('Pilih Tanggal Check Out Baru')
                    ->required()
                    ->default(fn (Reservasi $record) => $record->check_out)
                    ->minDate(fn (Reservasi $record) => $record->check_out)
                    ->weekStartsOnSunday()
                    ->native(false)
                    ->icon('heroicon-o-calendar')
                    ->columnSpan('full'),

                // FormsSection::make('Informasi Pembayaran')
                //     ->description('Masukkan detail pembayaran perpanjangan')
                //     ->columns(2)
                //     ->schema([
                //         Select::make('metode_pembayaran_id')
                //             // ->visible(fn (Get $get) => $get('tamu_id'))
                //             ->label('Metode Pembayaran')
                //             // ->required()
                //             ->options(MetodePembayaran::all()->pluck('nama', 'id'))
                //             ->live()
                //             ->afterStateUpdated(function (Get $get, Set $set) {
                //                 $set('jumlah_pembayaran', null);
                //                 $set('nama_pemilik_kartu', null);
                //                 $set('no_kartu', null);
                //                 $metodePembayaran = MetodePembayaran::find($get('metode_pembayaran_id'));
                //                 $set('jenis_pembayaran', $metodePembayaran->jenis);
                //                 $set('tujuan', $metodePembayaran->info_tujuan);

                //             }),
                //         TextInput::make('jenis_pembayaran')->hidden(),

                //         Rupiah::make('jumlah')
                //             ->label('Nominal Pembayaran')
                //             ->numeric()
                //             ->visible(fn (Get $get): bool => in_array(
                //                 $get('jenis_pembayaran'),
                //                 ['cash', 'qris', 'transfer', 'kartukredit', 'debit', 'tunai']
                //             ))->required(),

                //         RichEditor::make('tujuan')
                //             //  ->disabled()
                //             ->toolbarButtons([])
                //             ->label('informasi tujuan')
                //             ->visible(fn (Get $get): bool => in_array(
                //                 $get('jenis_pembayaran'),
                //                 ['transfer']
                //             )),
                //         PengirimField::make('pengirim')
                //             ->label('Informasi Pengirim')
                //             ->jenisPembayaran(fn (Get $get) => $get('jenis_pembayaran'))
                //             ->teksDataKosong('Data pengirim kosong, tambahkan sekarang')
                //             // ->required()
                //             ->visible(fn (Get $get): bool => in_array(
                //                 $get('jenis_pembayaran'),
                //                 ['transfer', 'kartukredit', 'debit', 'qris']
                //             )),

                //         FileUpload::make('bukti')
                //             ->label('Bukti Transfer')
                //             ->image()
                //             ->visible(fn (Get $get): bool => in_array(
                //                 $get('jenis_pembayaran'),
                //                 ['qris', 'transfer']
                //             )),


                //         // Select::make('metode_pembayaran_id')
                //         //     ->label('Metode Pembayaran')
                //         //     ->options(MetodePembayaran::where('status', 1)->pluck('nama', 'id'))
                //         //     ->required()
                //         //     ->searchable()
                //         //     ->preload(),

                //         // TextInput::make('jumlah')
                //         //     ->label('Jumlah Pembayaran')
                //         //     ->required()
                //         //     ->numeric()
                //         //     ->prefix('Rp')
                //         //     ->mask('999.999.999.999'),

                //         // Fieldset::make('Detail Bank/Rekening')
                //         //     ->schema([
                //         //         TextInput::make('pengirim')
                //         //             ->label('Pengirim')
                //         //             ->placeholder('Nama Pengirim'),

                //         //         TextInput::make('tujuan')
                //         //             ->label('Tujuan')
                //         //             ->placeholder('Nomor Rekening / Tujuan'),
                //         //     ]),

                //         // FileUpload::make('bukti')
                //         //     ->label('Bukti Pembayaran')
                //         //     ->directory('pembayaran')
                //         //     ->preserveFilenames()
                //         //     ->acceptedFileTypes(['image/*', 'application/pdf'])
                //         //     ->maxSize(2048)
                //         //     ->columnSpan(1),

                //         // Textarea::make('ket')
                //         //     ->label('Keterangan')
                //         //     ->placeholder('Keterangan tambahan')
                //         //     ->rows(3)
                //         //     ->columnSpan('full'),
                //     ]),
            ])
            ->action(function (array $data, Reservasi $record): void {
                DB::beginTransaction();
                try {
                    // Simpan checkout lama untuk keperluan perhitungan
                    $checkoutLama = Carbon::parse($record->check_out);
                    $checkoutBaru = Carbon::parse($data['check_out_baru']);

                    // Pastikan checkout baru lebih besar dari checkout lama
                    if (!$checkoutBaru->greaterThan($checkoutLama)) {
                        Notification::make()
                            ->title('Tanggal checkout baru harus lebih besar dari tanggal checkout lama')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Hitung selisih hari dengan memastikan minimal 1 hari
                    $selisihHari = max(1, $checkoutBaru->diffInDays($checkoutLama));

                    // Update tanggal checkout reservasi
                    $record->update([
                        'check_out' => $data['check_out_baru'],
                    ]);

                    // Cari transaksi kamar
                    $transaksiKamar = Transaksi::where('reservasi_id', $record->id)
                        ->where('produk_id', $record->kamar_id)
                        ->first();

                    $hargaPerHari = $transaksiKamar ? $transaksiKamar->harga : 0;
                    $totalHarga = $hargaPerHari * $selisihHari;

                    // Tambahkan transaksi baru untuk perpanjangan
                    Transaksi::create([
                        'toko_id' => $record->toko_id,
                        'reservasi_id' => $record->id,
                        'produk_id' => $record->kamar_id,
                        'nama_item' => 'Perpanjangan Kamar: '.$selisihHari.' hari',
                        'harga_modal' => $transaksiKamar->harga_modal ?? 0,
                        'harga' => $totalHarga,
                        'jumlah' => $selisihHari,
                        'ket' => 'Perpanjangan dari '.$checkoutLama->format('d/m/Y H:i').' ke '.$checkoutBaru->format('d/m/Y H:i'),
                    ]);

                    // Tambahkan pembayaran
                    // if(!isset($data['metode_pembayaran_id']) || !isset($data['jumlah'])) {
                    //      Pembayaran::create([
                    //     'reservasi_id' => $record->id,
                    //     'metode_pembayaran_id' => $data['metode_pembayaran_id'],
                    //     'jumlah' => $data['jumlah'],
                    //     'pengirim' => $data['pengirim'] ?? '',
                    //     'tujuan' => $data['tujuan'] ?? '',
                    //     'bukti' => $data['bukti'] ?? null,
                    //     'status' => 'LUNAS',
                    //     'ket' => $data['ket'] ?? 'Pembayaran perpanjangan kamar '.$selisihHari.' hari',
                    //     'nama' => 'Perpanjangan Kamar'
                    // ]);

                    // }
                   
                    DB::commit();

                    Notification::make()
                        ->title('Perpanjangan kamar berhasil')
                        ->success()
                        ->duration(5000)
                        ->send();

                } catch (\Exception $e) {
                    DB::rollBack();
                    Notification::make()
                        ->title('Terjadi kesalahan: '.$e->getMessage())
                        ->danger()
                        ->send();
                }
            });
    }
}
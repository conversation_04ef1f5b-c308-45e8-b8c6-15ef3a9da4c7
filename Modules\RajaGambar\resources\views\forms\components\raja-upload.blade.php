@php
    $id = $getId();
    $isDisabled = $isDisabled();
    $statePath = $getStatePath();
    $isMultiple = $isMultiple();
    $acceptedFileTypes = $getAcceptedFileTypes();
    $maxFiles = $getMaxFiles();
    $maxSize = $getMaxSize();
    $outputDirectory = $getOutputDirectory();
    $autoOptimize = $getAutoOptimize();
    $autoResizeConfig = $getAutoResizeConfig();
    $watermarkConfig = $getWatermarkConfig();
    $customEffects = $getCustomEffects();
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div
        x-data="rajaUploadComponent({
            state: $wire.{{ $applyStateBindingModifiers("\$entangle('{$statePath}')") }},
            isMultiple: @js($isMultiple),
            acceptedFileTypes: @js($acceptedFileTypes),
            maxFiles: @js($maxFiles),
            maxSize: @js($maxSize),
            outputDirectory: @js($outputDirectory),
            autoOptimize: @js($autoOptimize),
            autoResizeConfig: @js($autoResizeConfig),
            watermarkConfig: @js($watermarkConfig),
            customEffects: @js($customEffects),
            isDisabled: @js($isDisabled),
        })"
        class="raja-upload-wrapper"
    >
        <!-- Upload Area -->
        <div 
            class="raja-upload-area border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center transition-colors duration-200"
            :class="{
                'border-primary-500 bg-primary-50 dark:bg-primary-950': isDragOver,
                'border-gray-300 dark:border-gray-600': !isDragOver,
                'opacity-50 cursor-not-allowed': isDisabled
            }"
            @dragover.prevent="isDragOver = true"
            @dragleave.prevent="isDragOver = false"
            @drop.prevent="handleDrop($event)"
            @click="!isDisabled && $refs.fileInput.click()"
        >
            <!-- Upload Icon -->
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>

            <!-- Upload Text -->
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <span class="font-medium text-primary-600 dark:text-primary-400 cursor-pointer">
                    {{ $isMultiple ? 'Upload gambar' : 'Upload gambar' }}
                </span>
                atau drag & drop
            </div>
            
            <!-- File Info -->
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                <div>Format: JPG, PNG, GIF, WEBP, BMP</div>
                <div>Maksimal: {{ number_format($maxSize / 1024, 0) }}MB per file</div>
                @if($isMultiple && $maxFiles)
                    <div>Maksimal {{ $maxFiles }} file</div>
                @endif
            </div>

            <!-- Processing Info -->
            @if($autoOptimize || $autoResizeConfig || $watermarkConfig || !empty($customEffects))
                <div class="mt-3 p-2 bg-blue-50 dark:bg-blue-950 rounded text-xs text-blue-700 dark:text-blue-300">
                    <div class="font-medium mb-1">Pemrosesan Otomatis:</div>
                    <div class="space-y-1">
                        @if($autoOptimize)
                            <div>✓ Optimasi gambar</div>
                        @endif
                        @if($autoResizeConfig)
                            <div>✓ Resize ke {{ $autoResizeConfig['width'] }}x{{ $autoResizeConfig['height'] }}</div>
                        @endif
                        @if($watermarkConfig)
                            <div>✓ Watermark ({{ $watermarkConfig['position'] }})</div>
                        @endif
                        @if(!empty($customEffects))
                            <div>✓ Efek kustom ({{ count($customEffects) }} efek)</div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- File Input -->
        <input
            x-ref="fileInput"
            type="file"
            class="hidden"
            :multiple="isMultiple"
            :accept="acceptedFileTypes.join(',')"
            :disabled="isDisabled"
            @change="handleFileSelect($event)"
        />

        <!-- Preview Area -->
        <div x-show="files.length > 0" class="mt-4 space-y-3">
            <template x-for="(file, index) in files" :key="index">
                <div class="raja-upload-preview bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-start space-x-4">
                        <!-- Image Preview -->
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                                <img 
                                    :src="file.preview" 
                                    :alt="file.name"
                                    class="w-full h-full object-cover"
                                    x-show="file.preview"
                                />
                                <div x-show="!file.preview" class="w-full h-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- File Info -->
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate" x-text="file.name"></div>
                            <div class="text-xs text-gray-500 dark:text-gray-400" x-text="formatFileSize(file.size)"></div>
                            
                            <!-- Processing Status -->
                            <div class="mt-2">
                                <div x-show="file.status === 'uploading'" class="flex items-center space-x-2">
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" :style="`width: ${file.progress}%`"></div>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400" x-text="`${file.progress}%`"></span>
                                </div>
                                
                                <div x-show="file.status === 'processing'" class="flex items-center space-x-2 text-xs text-blue-600 dark:text-blue-400">
                                    <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Memproses gambar...</span>
                                </div>
                                
                                <div x-show="file.status === 'completed'" class="flex items-center space-x-2 text-xs text-green-600 dark:text-green-400">
                                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    <span>Selesai diproses</span>
                                </div>
                                
                                <div x-show="file.status === 'error'" class="flex items-center space-x-2 text-xs text-red-600 dark:text-red-400">
                                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <span x-text="file.error || 'Error memproses file'"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Remove Button -->
                        <button
                            type="button"
                            @click="removeFile(index)"
                            :disabled="isDisabled"
                            class="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Error Messages -->
        <div x-show="errors.length > 0" class="mt-3">
            <template x-for="error in errors" :key="error">
                <div class="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950 p-2 rounded" x-text="error"></div>
            </template>
        </div>
    </div>
</x-dynamic-component>

@push('scripts')
<script>
function rajaUploadComponent(config) {
    return {
        state: config.state,
        isMultiple: config.isMultiple,
        acceptedFileTypes: config.acceptedFileTypes,
        maxFiles: config.maxFiles,
        maxSize: config.maxSize,
        outputDirectory: config.outputDirectory,
        autoOptimize: config.autoOptimize,
        autoResizeConfig: config.autoResizeConfig,
        watermarkConfig: config.watermarkConfig,
        customEffects: config.customEffects,
        isDisabled: config.isDisabled,
        
        files: [],
        errors: [],
        isDragOver: false,
        
        init() {
            // Initialize with existing state
            if (this.state) {
                const existingFiles = Array.isArray(this.state) ? this.state : [this.state];
                this.files = existingFiles.map(path => ({
                    name: path.split('/').pop(),
                    path: path,
                    preview: `/storage/${path}`,
                    status: 'completed',
                    progress: 100,
                    size: 0
                }));
            }
        },
        
        handleDrop(event) {
            this.isDragOver = false;
            if (this.isDisabled) return;
            
            const files = Array.from(event.dataTransfer.files);
            this.processFiles(files);
        },
        
        handleFileSelect(event) {
            if (this.isDisabled) return;
            
            const files = Array.from(event.target.files);
            this.processFiles(files);
            
            // Reset input
            event.target.value = '';
        },
        
        processFiles(files) {
            this.errors = [];
            
            // Validate files
            const validFiles = [];
            for (const file of files) {
                if (!this.validateFile(file)) continue;
                validFiles.push(file);
            }
            
            if (validFiles.length === 0) return;
            
            // Check max files limit
            if (this.maxFiles && (this.files.length + validFiles.length) > this.maxFiles) {
                this.errors.push(`Maksimal ${this.maxFiles} file yang dapat diupload`);
                return;
            }
            
            // Add files to queue
            for (const file of validFiles) {
                this.addFile(file);
            }
        },
        
        validateFile(file) {
            // Check file type
            if (this.acceptedFileTypes.length > 0 && !this.acceptedFileTypes.includes(file.type)) {
                this.errors.push(`Format file ${file.name} tidak didukung`);
                return false;
            }
            
            // Check file size
            if (this.maxSize && file.size > this.maxSize * 1024) {
                this.errors.push(`File ${file.name} terlalu besar (maksimal ${Math.round(this.maxSize / 1024)}MB)`);
                return false;
            }
            
            return true;
        },
        
        addFile(file) {
            const fileObj = {
                name: file.name,
                size: file.size,
                file: file,
                preview: null,
                status: 'uploading',
                progress: 0,
                path: null,
                error: null
            };
            
            // Create preview
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileObj.preview = e.target.result;
                };
                reader.readAsDataURL(file);
            }
            
            this.files.push(fileObj);
            this.uploadFile(fileObj);
        },
        
        async uploadFile(fileObj) {
            try {
                const formData = new FormData();
                formData.append('file', fileObj.file);
                formData.append('directory', this.outputDirectory);
                formData.append('auto_optimize', this.autoOptimize);
                
                if (this.autoResizeConfig) {
                    formData.append('auto_resize', JSON.stringify(this.autoResizeConfig));
                }
                
                if (this.watermarkConfig) {
                    formData.append('watermark_config', JSON.stringify(this.watermarkConfig));
                }
                
                if (this.customEffects && Object.keys(this.customEffects).length > 0) {
                    formData.append('custom_effects', JSON.stringify(this.customEffects));
                }
                
                // Simulate upload progress
                fileObj.status = 'uploading';
                const progressInterval = setInterval(() => {
                    if (fileObj.progress < 90) {
                        fileObj.progress += Math.random() * 10;
                    }
                }, 100);
                
                // Upload file (you'll need to implement this endpoint)
                const response = await fetch('/api/rajagambar/upload-process', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                clearInterval(progressInterval);
                
                if (response.ok) {
                    const result = await response.json();
                    fileObj.progress = 100;
                    fileObj.status = 'processing';
                    
                    // Simulate processing time
                    setTimeout(() => {
                        fileObj.status = 'completed';
                        fileObj.path = result.data.url;
                        fileObj.preview = `/storage/${result.data.url}`;
                        this.updateState();
                    }, 1000);
                } else {
                    throw new Error('Upload failed');
                }
                
            } catch (error) {
                fileObj.status = 'error';
                fileObj.error = error.message;
                console.error('Upload error:', error);
            }
        },
        
        removeFile(index) {
            this.files.splice(index, 1);
            this.updateState();
        },
        
        updateState() {
            const completedFiles = this.files
                .filter(file => file.status === 'completed' && file.path)
                .map(file => file.path);
                
            if (this.isMultiple) {
                this.state = completedFiles;
            } else {
                this.state = completedFiles[0] || null;
            }
        },
        
        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    }
}
</script>
@endpush

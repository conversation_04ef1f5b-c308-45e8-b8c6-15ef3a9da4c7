<?php

namespace App\Aplikasi\Hotel\Resources\KamarResource\Pages;

use App\Aplikasi\Hotel\Resources\KamarResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewKamar extends ViewRecord
{
    protected static string $resource = KamarResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperPenjualanShift
 */
class PenjualanShift extends Model
{
  protected $table = 'penjualan_shift';

  protected $fillable = [
    'toko_id',
    'jenis',
    'jadwal',
    'karyawan_id',
    'buka',
    'tutup',
    'saldo_awal',
    'saldo_akhir',
    'selisih_saldo',
    'diserahkan',
    'ket',
    'status'
  ];

  protected $casts = [
    'buka' => 'datetime',
    'tutup' => 'datetime',
    'saldo_awal' => 'integer',
    'saldo_akhir' => 'integer',
    'selisih_saldo' => 'integer',
    'diserahkan' => 'integer',
  ];


  protected static function booted()
  {


    static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
      $builder->where('jenis', 'KASIR');
    });


    static::creating(function ($model) {
      $model->jenis = 'KASIR';
    });

    static::updating(function ($model) {
      $model->jenis = 'KASIR';
    });
  }


  public function penjualan()
  {
    return $this->hasMany(Penjualan::class, 'shift_id');
  }

  public function totalPenjualan()
  {
    return $this->penjualan()
      ->where('status', 'SELESAI')
      ->sum(function ($penjualan) {
        return $penjualan->grandTotal();
      });
  }

  public function totalPenjualanShift(?int $shift_id = null): int
  {
    // Jika shift_id tidak diberikan, gunakan ID dari objek ini
    $shift_id = $shift_id ?? $this->id;

    // Ambil semua penjualan dengan status SELESAI untuk shift ini
    $penjualanList = Penjualan::where('shift_id', $shift_id)
      ->where('status', 'SELESAI')
      ->get();

    // Hitung total dengan menjumlahkan grandTotal() dari setiap penjualan
    $total = 0;
    foreach ($penjualanList as $penjualan) {
      $total += $penjualan->grandTotal();
    }

  return (int)$total;
  }

  public function diserahkan(): BelongsTo
  {
    return $this->belongsTo(User::class, 'diserahkan');
  }

  // Tambahkan relasi untuk toko
  public function toko(): BelongsTo
  {
    return $this->belongsTo(\App\Models\Toko::class, 'toko_id');
  }

  // Tambahkan relasi untuk karyawan
  public function karyawan(): BelongsTo
  {
    return $this->belongsTo(User::class, 'karyawan_id');
  }

  public function user(): BelongsTo
  {
    return $this->belongsTo(User::class, 'karyawan_id');
  }

  public function statusShift()
  {
    $find = self::where('karyawan_id', auth()->id())->orderByDesc('id')->first();
    return $find->status;
  }

}
<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;

use App\Aplikasi\Hotel\Forms\Components\PilihKamar;
use App\Aplikasi\Hotel\Resources\ReservasiResource;
use App\Models\KonfigUtama as Konfig;
use App\Aplikasi\Hotel\Services\ReservasiKeranjangService;
use Filament\Forms\Components\RichEditor;
use Filament\Resources\Pages\EditRecord;
use Carbon\Carbon;
use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Models\Kamar;

use Closure;
use Filament\Forms;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Livewire;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\View\LegacyComponents\Widget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Filament\Support\RawJs;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
 
use App\Filament\Forms\Components\Rupiah;
use App\Filament\Resources\ReservasiResource\RelationManagers\PembayaranRelationManager;
use App\Filament\Widgets\KeranjangReservasiWidget;
use App\Models\MetodePembayaran;
use Filament\Support\Enums\MaxWidth;

class EditReservasi extends EditRecord
{
    protected static string $resource = ReservasiResource::class;


    public function form(Form $form): Form
    {


        $biayaLayanan = Konfig::jsonKuRaw('biaya_layanan');
        return $form

            ->statePath('data')
            ->schema([

                Grid::make()->columns(2)->columnSpan(2)
                    ->schema([
                        Section::make()->columns(2)->columnSpan(2)
                            ->schema([

                                DateTimePicker::make('check_in')
                                    ->label('Tanggal & Jam Check In')
                                    ->required()
                                    ->displayFormat('d / m / Y H:i')
                                    ->disabled()
                                    ->reactive(),

                                DateTimePicker::make('check_out')
                                    ->label('Tanggal & Jam Check Out')
                                    ->required()
                                    ->displayFormat('d / m / Y H:i')
                                
                                    ->reactive(),

                                Select::make('tamu_id')
                                    ->label('Pilih Tamu')
                                    ->relationship('tamu', 'nama')
                                    ->getOptionLabelFromRecordUsing(fn ($record) =>
                                        "{$record->nama} • {$record->telpon}  "
                                    )
                                    ->disabled()
                                    ->required(),


                            ]),

             

                        Section::make()
                            ->schema([


                                Select::make('referensi')
                                    ->label('Referensi')
                                    ->options(function () {
                                        $konfig = Konfig::where('nama', 'referensi')->first();
                                        return json_decode($konfig->isi_json, true);
                                    })
                                    ->preload()
                                    ->searchable()
                                    ->placeholder('Pilih Referensi')

                                    ->createOptionForm([
                                        TextInput::make('name')
                                            ->label('Nama Referensi')
                                            ->required()

                                            ->maxLength(255)
                                            ->helperText('Referensi baru akan otomatis ditambahkan ke daftar')
                                    ])
                                    ->createOptionUsing(function (array $data) {
                                        $konfig = Konfig::where('nama', 'referensi')->first();
                                        $existingOptions = json_decode($konfig->isi_json, true);
                                        $newValue = strtoupper($data['name']);
                                        $existingOptions[] = $newValue;
                                        $konfig->update([
                                            'isi_json' => json_encode($existingOptions)
                                        ]);

                                        return $newValue;
                                    })
                                    ->createOptionModalHeading('Tambah Referensi Baru')
                                    ->live(),

                                Textarea::make('ket_referensi')
                                    ->label('Keterangan Referensi')
                                    ->placeholder('Masukkan keterangan untuk referensi')
                                    ->rows(3)
                                    ->visible(fn (Get $get): bool => filled($get('referensi')))
                                    ->columnSpan(1),


                                Radio::make('status_reservasi')
                                    ->label('Status Reservasi')
                                    ->options([
                                        'SCI' => 'CHECK IN',
                                        'SCO' => 'CHECK OUT',
                                    ])->inline()
                                    ->columnSpan(2),



                                TextInput::make('no_invoice')->default('INVH#'.uniqid())->columnSpan(1)->hidden(),
                            ])->columns(2)->columnSpan(2),



                    ]),


                Grid::make()->columns(1)->columnSpan(1)
                    ->schema([

                        // Livewire::make(KeranjangWidget::class)->columnSpanFull(),

                        PilihKamar::make('kamar_id')
                            ->label('')
                            ->required()
                            ->columnSpanFull(),

                        Section::make('Diskon')->collapsible()->collapsed()
                            ->compact()
                            ->schema([
                                TextInput::make('pajak')
                                    ->numeric()
                                    ->label('Pajak %')
                                    ->extraInputAttributes(['class' => 'text-xs  h-8'])
                                    ->disabled()
                                    ->default($biayaLayanan['pajak'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        // Simpan nilai ke session
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['pajak'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        // Gunakan $livewire untuk dispatch event
                                        $livewire->dispatch('keranjang-updated');
                                    }),

                                TextInput::make('servis')
                                    ->numeric()
                                    ->label('Servis %')
                                    ->extraInputAttributes(['class' => 'text-xs   h-8'])
                                    ->disabled()
                                    ->default($biayaLayanan['servis'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['servis'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        $livewire->dispatch('keranjang-updated');
                                    }),

                                TextInput::make('diskon')
                                    ->numeric()
                                    ->label('Diskon %')
                                    ->extraInputAttributes(['class' => 'text-xs  h-8'])
                                    ->default($biayaLayanan['diskon'])
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, $livewire) {
                                        $biayaTambahan = session('biaya_tambahan', []);
                                        $biayaTambahan['diskon'] = $state;
                                        session(['biaya_tambahan' => $biayaTambahan]);

                                        $livewire->dispatch('keranjang-updated');
                                    }),
                            ])->columns(3)->columnSpan(1),

                    ]),







            ])->columns(3);
    }



    protected function processJsonField($field)
    {
        if (is_string($field) && $this->isJson($field)) {
            return json_decode($field, true);
        }
        return $field ?? [];
    }

    protected function isJson($string)
    {
        if (! is_string($string))
            return false;
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    public function getKamarTersedia($checkIn, $checkOut)
    {
        if (empty($checkIn) || empty($checkOut)) {
            return collect([]);
        }

        $checkInDate = Carbon::parse($checkIn);
        $checkOutDate = Carbon::parse($checkOut);
        $currentKamarId = $this->record->kamar_id;

        $tipeKamars = KamarTipe::all();
        $result = collect();

        foreach ($tipeKamars as $tipe) {
            $kamars = Kamar::where('kategori_id', $tipe->id)->get();

            if ($kamars->count() > 0) {
                // Ambil informasi dari kamar pertama
                $firstKamar = $kamars->first();
                $spesifikasi = $this->processJsonField($firstKamar->spesifikasi);

                // Transformasi koleksi kamar
                $mappedKamars = $kamars->map(function ($kamar) use ($checkInDate, $checkOutDate, $currentKamarId) {
                    // Kamar saat ini selalu tersedia untuk dirinya sendiri
                    $isAvailable = ($kamar->id == $currentKamarId) ? true : $kamar->isAvailable($checkInDate, $checkOutDate);

                    return [
                        'id' => $kamar->id,
                        'nama' => $kamar->nama,
                        'tersedia' => $isAvailable,
                    ];
                });

                $result->push([
                    'tipe_id' => $tipe->id,
                    'tipe_nama' => $tipe->nama,
                    'harga' => $firstKamar->harga, // Mengambil harga dari kamar pertama
                    'fasilitas' => $firstKamar->fasilitas,
                    'spesifikasi' => $spesifikasi,
                    'kamars' => $mappedKamars
                ]);
            }
        }

        return $result;
    }


    public function updateHargaKamar(int $kamarId, int $harga): void
    {
        $service = app(ReservasiKeranjangService::class);
        $service->updateHargaKamar($kamarId, $harga);
        $this->dispatch('keranjang-updated');
    }
    public function tambahKeKeranjang(int $kamarId, string $checkIn, string $checkOut): void
    {
        $service = app(ReservasiKeranjangService::class);
        $service->tambahKamar($kamarId, $checkIn, $checkOut);
    }
}
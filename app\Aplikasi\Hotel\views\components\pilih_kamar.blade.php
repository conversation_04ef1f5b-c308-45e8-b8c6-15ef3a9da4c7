<x-dynamic-component :component="$getFieldWrapperView()" :id="$getId()" :label="$getLabel()" :label-sr-only="$isLabelHidden()" :helper-text="$getHelperText()"
 :hint="$getHint()" :hint-icon="$getHintIcon()" :required="$isRequired()" :state-path="$getStatePath()">
 <div x-data="{
     state: $wire.entangle('{{ $getStatePath() }}'),
     checkIn: null,
     checkOut: null,
     kamarGroups: [],
     selectedGroup: null,
     showModal: false,
     init() {
         this.checkIn = $wire.entangle('{{ $getStatePath(true) }}').defer;
         this.checkOut = $wire.entangle('{{ $getStatePath(true) }}').defer;
 
         this.$watch('$wire.data.check_in', value => {
             if (value && $wire.data.check_out) this.refreshKamar();
         });
 
         this.$watch('$wire.data.check_out', value => {
             if (value && $wire.data.check_in) this.refreshKamar();
         });
 
         if ($wire.data.check_in && $wire.data.check_out) {
             this.refreshKamar();
         }
     },
     refreshKamar() {
         $wire.call('getKamarTersedia', $wire.data.check_in, $wire.data.check_out)
             .then(result => this.kamarGroups = result);
     },
     selectKamar(kamarId, isAvailable) {
         if (isAvailable) {
             this.state = kamarId;
             this.tambahKamarKeKeranjang(kamarId);
         }
     },
     tambahKamarKeKeranjang(kamarId) {
         if (!kamarId) return;
         
         $wire.call('tambahKeKeranjang', kamarId, $wire.data.check_in, $wire.data.check_out)
             .then(() => {
                 $dispatch('keranjang-updated');
                 this.showFeedback('Kamar berhasil ditambahkan ke keranjang');
             })
             .catch(error => {
                 console.error('Error menambahkan kamar:', error);
                 this.showFeedback('Gagal menambahkan kamar ke keranjang', true);
             });
     },
     showFeedback(message, isError = false) {
         $dispatch('notify', {
             style: isError ? 'error' : 'success',
             message: message,
             timeout: 3000
         });
     },
     openModal(group) {
         this.selectedGroup = group;
         this.showModal = true;
     },
     closeModal() {
         this.showModal = false;
         setTimeout(() => {
             this.selectedGroup = null;
         }, 300);
     },
     formatSpesifikasi(spesifikasi) {
         if (!spesifikasi || typeof spesifikasi !== 'object') return '';
         return Object.entries(spesifikasi)
             .map(([key, value]) => `${this.capitalizeFirst(key)}: ${value}`)
             .join(', ');
     },
     capitalizeFirst(string) {
         if (typeof string !== 'string') return string;
         return string.charAt(0).toUpperCase() + string.slice(1);
     }
 }" class="space-y-1" @keydown.escape.window="closeModal()">
  <!-- Pesan saat belum memilih tanggal -->
  <div class="rounded-lg  bg-neutral-50 p-4 shadow-sm border border-neutral-200" x-show="!$wire.data.check_in || !$wire.data.check_out">
   <div class="flex items-center space-x-3">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
    <div class="text-sm text-neutral-600">
     Silakan pilih tanggal check-in dan check-out terlebih dahulu untuk melihat kamar yang tersedia.
    </div>
   </div>
  </div>

  <!-- Daftar kamar -->
  <div x-show="$wire.data.check_in && $wire.data.check_out" id="tutorial_kamar">
   <!-- Loading state -->
   <template x-if="kamarGroups.length === 0">
    <div class="rounded-lg bg-info-50 p-4 text-info-700 flex items-center space-x-3 border border-info-100">
     <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
     </svg>
     <div>Sedang memuat data kamar...</div>
    </div>
   </template>

   <!-- Daftar grup kamar -->
   <template x-for="(group, groupIndex) in kamarGroups" :key="'group-' + groupIndex">
    <div class="mb-3">
      <button @click.prevent="openModal(group)" 
              class="tombol tombol-penuh  shadow-sm transition hover:shadow-md overflow-hidden rounded-lg border border-dotted border-abu-700 p-0 flex flex-col w-full hover:border-merah-500 ">
        <div class="w-full px-4 py-1">
          <div class="flex items-center justify-between">
            <div class="flex-grow-0">
              <h3 class="text-md font-medium text-slate-700 group-hover:text-red-500  transition-colors duration-200"  x-text="group.tipe_nama"></h3>
            </div>
  
            <div class="flex-grow text-center">
                <div class="text-slate-500 text-sm font-medium">
                  <span x-text="group.kamars.filter(k => k.tersedia).length"></span> kamar tersedia
                </div>
              </div>
  
            <div class="flex-grow-0 text-right">
              <div class="text-slate-700 text-md font-semibold" x-text="'Rp ' + Number(group.harga).toLocaleString('id-ID')">
              </div>
            </div>
          </div>
        </div>
      </button>
    </div>
  </template>

  </div>

  <!-- Modal Kamar -->
  <div x-show="showModal" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
   x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
   x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 z-50 overflow-y-auto"
   style="display: none;">
   <div class="flex min-h-screen items-center justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
    <!-- Background overlay dengan efek blur -->
    <div class="fixed inset-0 bg-neutral-800 bg-opacity-75 backdrop-blur-sm transition-opacity" @click="closeModal()"></div>

    <!-- Modal panel dengan lebar 1/2 layar -->
    <div x-show="showModal" x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
     class="inline-block max-h-[80vh] h-[80%] w-full transform overflow-hidden overflow-y-auto rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-[70%] sm:align-middle">
     
     <!-- Header Modal -->
     <div class="bg-white px-6 py-4 border-b border-neutral-200">
      <div class="flex items-center justify-between">
       <h3 class="text-xl font-semibold text-primary-800" x-text="selectedGroup?.tipe_nama"></h3>
       <button @click="closeModal()" class="text-neutral-400 hover:text-neutral-500 focus:outline-none">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
       </button>
      </div>
     </div>

     <!-- Konten Modal -->
     <div class="bg-white px-6 py-4">
      <!-- Layout Grid 2 Kolom -->
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
       <!-- Kolom Kiri: Informasi Kamar -->
       <div class="space-y-5 border-r-0 pr-0 md:border-r md:border-neutral-200 md:pr-5">
        <!-- Gambar Kamar -->
        <div class="overflow-hidden rounded-lg bg-neutral-100">
         <div class="flex h-48 items-center justify-center bg-neutral-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
         </div>
        </div>

        <!-- Harga -->
        <div class="mb-4">
         <h4 class="text-sm font-medium uppercase tracking-wider text-neutral-500">Harga per malam:</h4>
         <p class="text-primary-700 text-2xl font-bold"
          x-text="'Rp ' + Number(selectedGroup?.harga).toLocaleString('id-ID')"></p>
        </div>

        <!-- Fasilitas Kamar -->
        <template x-if="selectedGroup?.fasilitas && selectedGroup.fasilitas.length > 0">
         <div class="mb-4">
          <h4 class="mb-2 text-sm font-medium uppercase tracking-wider text-neutral-500">Fasilitas:</h4>
          <ul class="grid grid-cols-1 gap-2 sm:grid-cols-2">
           <template x-for="(fasilitas, index) in selectedGroup.fasilitas" :key="index">
            <li class="flex items-center text-sm text-neutral-600">
             <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
             </svg>
             <span x-text="fasilitas"></span>
            </li>
           </template>
          </ul>
         </div>
        </template>

        <!-- Spesifikasi Kamar -->
        <template x-if="selectedGroup?.spesifikasi && Object.keys(selectedGroup.spesifikasi).length > 0">
         <div class="mb-4">
          <h4 class="mb-2 text-sm font-medium uppercase tracking-wider text-neutral-500">Spesifikasi:</h4>
          <div class="grid grid-cols-2 gap-2 text-sm">
           <template x-for="(value, key) in selectedGroup.spesifikasi" :key="key">
            <div class="rounded-md bg-neutral-50 p-2 border border-neutral-100">
             <span class="font-medium text-neutral-700" x-text="capitalizeFirst(key) + ':'"></span>
             <span class="ml-1 text-neutral-600" x-text="value"></span>
            </div>
           </template>
          </div>
         </div>
        </template>
       </div>

       <!-- Kolom Kanan: Daftar Kamar -->
       <div class="space-y-3">
        <h4 class="mb-3 text-sm font-medium uppercase tracking-wider text-neutral-500">Pilih Kamar:</h4>
        <div class="max-h-[calc(80vh-250px)] space-y-3 overflow-y-auto pr-2">
         <template x-for="kamar in selectedGroup?.kamars" :key="kamar.id">
          <!-- Item kamar dengan desain yang lebih modern -->
          <div class="rounded-lg border border-neutral-200 p-3 transition hover:bg-neutral-50"
           :class="{
               'bg-primary-50 border-primary-500 border-2': state == kamar.id,
               'opacity-60 cursor-not-allowed': !kamar.tersedia,
               'cursor-pointer': kamar.tersedia
           }"
           @click="kamar.tersedia && selectKamar(kamar.id, kamar.tersedia)">
           <div class="flex items-center">
            <!-- Radio button dengan styling yang lebih modern -->
            <div class="relative flex h-6 w-6 flex-shrink-0 items-center justify-center">
             <input type="radio" :id="'modal-kamar-' + kamar.id" :name="'{{ $getId() }}-modal'"
              :value="kamar.id" x-model="state" :disabled="!kamar.tersedia"
              class="text-primary-600 focus:ring-primary-500 h-4 w-4 border-neutral-300"
              @click.stop="kamar.tersedia && tambahKamarKeKeranjang(kamar.id)" />
            </div>

            <!-- Informasi kamar -->
            <div class="ml-4 flex flex-1 items-center justify-between">
             <label :for="'modal-kamar-' + kamar.id" class="flex cursor-pointer items-center font-medium"
              :class="{ 'text-neutral-400': !kamar.tersedia, 'text-neutral-700': kamar.tersedia }">
              <!-- Nama kamar dalam badge modern -->
              <span class="rounded-md bg-neutral-100 px-2 py-1 text-sm text-neutral-800" x-text="kamar.nama"></span>
             </label>

             <!-- Status ketersediaan dengan badge yang lebih kontras -->
             <div>
              <span x-show="kamar.tersedia" class="rounded-full bg-success-100 px-2 py-1 text-xs font-medium text-success-700">
               Tersedia
              </span>

              <span x-show="!kamar.tersedia" class="rounded-full bg-danger-100 px-2 py-1 text-xs font-medium text-danger-700">
               Tidak Tersedia
              </span>
             </div>
            </div>
           </div>
          </div>
         </template>
        </div>
       </div>
      </div>
     </div>

     <!-- Footer Modal -->
     <div class="bg-neutral-50 px-6 py-4 border-t border-neutral-200 flex justify-end">
      <button type="button"
       class="inline-flex items-center rounded-md border border-neutral-300 bg-white px-4 py-2 text-sm font-medium text-neutral-700 shadow-sm hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
       @click="closeModal()">
       Tutup
      </button>
     </div>
    </div>
   </div>
  </div>
 </div>
</x-dynamic-component>
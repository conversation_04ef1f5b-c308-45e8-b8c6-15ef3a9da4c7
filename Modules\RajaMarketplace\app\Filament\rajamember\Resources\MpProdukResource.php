<?php

namespace Modules\RajaMarketplace\Filament\rajamember\Resources;

use App\Filament\Forms\Components\Rupiah;
use Filament\Forms;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Modules\RajaMarketplace\Filament\rajamember\Resources\MpProdukResource\Pages;
use Modules\RajaMarketplace\Filament\rajamember\Resources\MpProdukResource\RelationManagers;
use Modules\RajaMarketplace\Models\MpProdukMember as MpProduk;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

class MpProdukResource extends Resource
{
    protected static ?string $model = MpProduk::class;


    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';
    protected static ?string $navigationGroup = 'Marketplace';

    protected static ?string $navigationLabel = 'Produk';
    protected static ?string $slug = 'marketplace/produk';
    protected static bool $shouldRegisterNavigation = false;
    
    

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Split::make([
                    Section::make([
                        Select::make('kategori_id')->required()->relationship('kategori', 'nama'),

                        Forms\Components\TextInput::make('nama')
                            ->required()
                            ->maxLength(50)
                            ->minLength(5)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn(Set $set, ?string $state) => $set('slug', Str::slug($state))),


                        Rupiah::make('harga'),
                        Forms\Components\TextInput::make('stok')
                            ->numeric()
                            ->default(0),
                        RichEditor::make('ket')->label('informasi produk')
                            ->toolbarButtons([
                              
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ])
                            ->columnSpanFull(),

                        Toggle::make('tampil')

                            ->default(1),

                    ])->columns(2),
                    Section::make([
                        RajaPicker::make('gambar')->byUserId(),
                    ])->grow(false),
                ])->from('lg'),





            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            // ->modifyQueryUsing(fn ($query) => $query->where('toko_id', \Illuminate\Support\Facades\Auth::user()->toko_id))
            ->columns([

                TextColumn::make('kategori.nama')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('nama')
                    ->searchable(),

                Tables\Columns\TextColumn::make('harga')
                    ->numeric()
                    ->sortable(),

                ImageColumn::make('gambar')
                    ->square(),

                ToggleColumn::make('tampil'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMpProduks::route('/'),
            'create' => Pages\CreateMpProduk::route('/create'),
            'edit' => Pages\EditMpProduk::route('/{record}/edit'),
        ];
    }
}

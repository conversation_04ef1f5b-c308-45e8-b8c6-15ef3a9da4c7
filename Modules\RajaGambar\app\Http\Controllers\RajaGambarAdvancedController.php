<?php

namespace Modules\RajaGambar\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RajaGambar\Services\RajaGambarService;
use Illuminate\Support\Facades\Validator;
use Exception;

class RajaGambarAdvancedController extends Controller
{
    protected $rajaGambarService;

    public function __construct(RajaGambarService $rajaGambarService)
    {
        $this->rajaGambarService = $rajaGambarService;
    }

    /**
     * Focal crop dengan titik fokus
     */
    public function focalCrop(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'required|integer|min:1|max:4000',
            'height' => 'required|integer|min:1|max:4000',
            'focal_x' => 'required|integer|min:0',
            'focal_y' => 'required|integer|min:0',
            'zoom' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            $zoom = $request->zoom ?? 1;
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->focalCrop($request->width, $request->height, $request->focal_x, $request->focal_y, $zoom)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-focal crop',
                'data' => [
                    'url' => $result,
                    'width' => $request->width,
                    'height' => $request->height,
                    'focal_x' => $request->focal_x,
                    'focal_y' => $request->focal_y,
                    'zoom' => $zoom
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manual crop dengan koordinat spesifik
     */
    public function manualCrop(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'start_x' => 'required|integer|min:0',
            'start_y' => 'required|integer|min:0',
            'width' => 'required|integer|min:1|max:4000',
            'height' => 'required|integer|min:1|max:4000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->manualCrop($request->start_x, $request->start_y, $request->width, $request->height)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-manual crop',
                'data' => [
                    'url' => $result,
                    'start_x' => $request->start_x,
                    'start_y' => $request->start_y,
                    'width' => $request->width,
                    'height' => $request->height
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Adjust brightness
     */
    public function brightness(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'brightness' => 'required|integer|min:-100|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->brightness($request->brightness)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Brightness gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'brightness' => $request->brightness
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Adjust contrast
     */
    public function contrast(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'contrast' => 'required|integer|min:-100|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->contrast($request->contrast)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Contrast gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'contrast' => $request->contrast
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Adjust gamma
     */
    public function gamma(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'gamma' => 'required|numeric|min:0.1|max:9.99',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->gamma($request->gamma)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gamma gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'gamma' => $request->gamma
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Colorize gambar
     */
    public function colorize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'red' => 'required|integer|min:-100|max:100',
            'green' => 'required|integer|min:-100|max:100',
            'blue' => 'required|integer|min:-100|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();
            
            $result = $this->rajaGambarService
                ->load($imagePath)
                ->colorize($request->red, $request->green, $request->blue)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-colorize',
                'data' => [
                    'url' => $result,
                    'red' => $request->red,
                    'green' => $request->green,
                    'blue' => $request->blue
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }
}

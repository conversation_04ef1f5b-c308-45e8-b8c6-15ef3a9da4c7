<?php

namespace Modules\RajaMarketplace\Filament\rajamember\Pages;

use Filament\Pages\Page;
use Modules\RajaMarketplace\Models\MpProduk;
use Modules\RajaMarketplace\Models\MpToko;
use Illuminate\Support\Facades\Auth;
 
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;

/**
 * Halaman Dashboard khusus untuk modul RajaMarketplace.
 * Menampilkan ringkasan statistik seperti jumlah produk dan jumlah toko.
 */
class MarketplaceDashboard extends Page
{
    use HasPageShield;
    /** Ikon menu. */
    public static ?string $navigationIcon = 'heroicon-o-building-storefront';

    /** Teks label yang tampil di menu.
     *  Gunakan Bahasa Indonesia sesuai preferensi pengguna.
     */
    public static ?string $navigationLabel = 'MarketPlace';

    /** Kelompok menu agar terpisah di sidebar Filament. */
    // public static ?string $navigationGroup = 'Marketplace';

    /** Slug URL relatif terhadap prefix panel (mis. /admin). */
    public static ?string $slug = 'marketplace';

    /** Apakah item ini harus muncul di navigasi? */
    protected static bool $shouldRegisterNavigation = true;

    /** View Blade yang digunakan untuk merender halaman. */
    public static string $view = 'rajamarketplace::filament.pages.dashboard-admin';

    // ===== Variabel publik agar dapat diakses oleh view =====
    /** Total keseluruhan produk marketplace. */
    public int $totalProduk;

    /** Daftar produk terbaru milik toko pengguna (max 5). */
    public $produkTerbaru = [];

    /** Model toko milik user (jika ada). */
    public ?MpToko $toko = null;

    /**
     * Lifecycle method mount() dipanggil saat halaman diinisialisasi.
     * Di sini kita mengambil data statistik dasar.
     */
    public function mount(): void
    {
        // Ambil toko milik user
        $userId = Auth::id();
        $this->toko = MpToko::where('user_id', $userId)->first();

        // Hitung hanya produk milik toko user
        if ($this->toko) {
            $this->totalProduk = MpProduk::where('toko_id', $this->toko->id)->count();
            // Ambil 5 produk terbaru
            $this->produkTerbaru = MpProduk::where('toko_id', $this->toko->id)
                ->latest()
                ->take(5)
                ->get();
        } else {
            $this->totalProduk = 0;
            $this->produkTerbaru = collect();
        }
    }

    /**
     * Daftar aksi di header halaman.
     * Menambahkan tombol "Produk" untuk menuju ke halaman daftar produk.
     */
    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('produk')
                ->label('Produk')
                ->icon('heroicon-o-archive-box')
                ->url(route('filament.rajamember.resources.marketplace.produk.index')),
        ];
    }
} 
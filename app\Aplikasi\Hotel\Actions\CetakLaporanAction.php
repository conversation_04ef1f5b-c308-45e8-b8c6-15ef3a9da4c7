<?php

namespace App\Aplikasi\Hotel\Actions;

use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Models\Tamu;
use App\Models\User;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Filament\Notifications\Actions\Action as NotificationAction;


class CetakLaporanAction
{
   /**
    * Daftar kolom yang bisa dipilih untuk laporan
    */
   protected static array $columnOptions = [
      'id' => 'ID Reservasi',
      'tamu' => 'Nama Tamu',
      'durasi' => 'Durasi Menginap',
      'pajak' => 'Pajak',
      'diskon' => 'Diskon',
      'servis' => 'Servis',
      'modal' => 'Modal',
      'bersih' => 'Harga Bersih',
      'keuntungan' => 'Keuntungan',
      'total_akhir' => 'Total Tagihan',
      'pembayaran' => 'Total Pembayaran',
      'sisa_tagihan' => 'Sisa Tagihan',
      'status' => 'Status Reservasi',
      'pegawai' => 'Petugas',
      'tanggal' => 'Tanggal Reservasi',
   ];

   /**
    * Membuat Action untuk cetak laporan langsung
    * 
    * @return \Filament\Actions\Action
    */
   public static function make(): Action
   {
      return Action::make('cetakLaporanLangsung')
         ->label('Cetak Laporan')
         ->color('success')
         ->icon('heroicon-o-printer')
         ->modalWidth('6xl')
         ->modalHeading('Cetak Laporan Reservasi')
         ->modalSubmitActionLabel('Cetak Sekarang')
         ->form([
            Section::make('Filter Tanggal Reservasi')
               ->description('Filter berdasarkan tanggal, bulan, atau tahun pembuatan reservasi')
               ->schema([
                  Grid::make(3)
                     ->schema([
                        DatePicker::make('tanggal_mulai')
                           ->label('Tanggal Mulai'),

                        DatePicker::make('tanggal_akhir')
                           ->label('Tanggal Akhir'),

                        Select::make('bulan')
                           ->label('Bulan Reservasi')
                           ->options([
                              '1' => 'Januari',
                              '2' => 'Februari',
                              '3' => 'Maret',
                              '4' => 'April',
                              '5' => 'Mei',
                              '6' => 'Juni',
                              '7' => 'Juli',
                              '8' => 'Agustus',
                              '9' => 'September',
                              '10' => 'Oktober',
                              '11' => 'November',
                              '12' => 'Desember',
                           ])
                           ->placeholder('Semua Bulan'),

                        Select::make('tahun')
                           ->label('Tahun Reservasi')
                           ->options(function () {
                              $tahunSekarang = date('Y');
                              $options = [];
                              for ($i = 0; $i < 10; $i++) {
                                 $tahun = (int) $tahunSekarang - $i;
                                 $options[$tahun] = (string) $tahun;
                              }
                              return $options;
                           })
                           ->placeholder('Semua Tahun'),
                     ]),
               ]),

            Section::make('Filter Check-in dan Check-out')
               ->schema([
                  Grid::make(2)
                     ->schema([
                        DatePicker::make('checkin_dari')
                           ->label('Check-in Dari'),

                        DatePicker::make('checkin_sampai')
                           ->label('Check-in Sampai'),

                        DatePicker::make('checkout_dari')
                           ->label('Check-out Dari'),

                        DatePicker::make('checkout_sampai')
                           ->label('Check-out Sampai'),
                     ]),
               ]),

            Section::make('Filter Status')
               ->schema([
                  Grid::make(3)
                     ->schema([
                        Select::make('status_reservasi')
                           ->label('Status Reservasi')
                           ->options(Konfig::jsonKuRaw('reservasi_status'))
                           ->placeholder('Semua Status')
                           ->searchable(),

                        Select::make('status_pembayaran')
                           ->label('Status Pembayaran')
                           ->options([
                              'lunas' => 'Lunas',
                              'belum_lunas' => 'Belum Lunas',
                           ])
                           ->placeholder('Semua Status Pembayaran'),
                     ]),
               ]),

            Section::make('Filter Pengguna')
               ->schema([
                  Grid::make(2)
                     ->schema([
                        Select::make('tamu_id')
                           ->label('Tamu')
                           ->options(Tamu::all()->pluck('nama', 'id'))
                           ->placeholder('Semua Tamu')
                           ->searchable(),

                        Select::make('karyawan_id')
                           ->label('Petugas')
                           ->options(User::all()->pluck('name', 'id'))
                           ->placeholder('Semua Petugas')
                           ->searchable(),
                     ]),
               ]),

            Section::make('Pilihan Kolom')
               ->description('Pilih kolom yang ingin ditampilkan dalam laporan.')
               ->schema(self::generateColumnToggles())
               ->columns(4),


         ])
         ->action(function (array $data) {
            // Buat query untuk data laporan berdasarkan filter
            $query = Reservasi::query();

            // Filter berdasarkan tanggal reservasi (created_at)
            if (isset($data['tanggal_mulai'])) {
               $query->whereDate('created_at', '>=', $data['tanggal_mulai']);
            }

            if (isset($data['tanggal_akhir'])) {
               $query->whereDate('created_at', '<=', $data['tanggal_akhir']);
            }

            // Filter berdasarkan bulan reservasi
            if (isset($data['bulan']) && ! empty($data['bulan'])) {
               $query->whereMonth('created_at', $data['bulan']);
            }

            // Filter berdasarkan tahun reservasi
            if (isset($data['tahun']) && ! empty($data['tahun'])) {
               $query->whereYear('created_at', $data['tahun']);
            }

            // Filter berdasarkan tanggal check-in
            if (isset($data['checkin_dari'])) {
               $query->whereDate('check_in', '>=', $data['checkin_dari']);
            }

            if (isset($data['checkin_sampai'])) {
               $query->whereDate('check_in', '<=', $data['checkin_sampai']);
            }

            // Filter berdasarkan tanggal check-out
            if (isset($data['checkout_dari'])) {
               $query->whereDate('check_out', '>=', $data['checkout_dari']);
            }

            if (isset($data['checkout_sampai'])) {
               $query->whereDate('check_out', '<=', $data['checkout_sampai']);
            }

            // Filter berdasarkan status reservasi
            if (isset($data['status_reservasi']) && $data['status_reservasi'] !== '') {
               $query->where('status_reservasi', $data['status_reservasi']);
            }

            // Filter berdasarkan status pembayaran (lunas/belum lunas)
            if (isset($data['status_pembayaran']) && ! empty($data['status_pembayaran'])) {
               // Karena status pembayaran tidak ada di tabel, kita perlu melakukan filter manual
               // Ambil semua data reservasi dulu
               $allReservasi = $query->get();

               // Filter reservasi berdasarkan status pembayaran
               $filteredIds = $allReservasi->filter(function ($reservasi) use ($data) {
                  $sisaTagihan = $reservasi->hitungSisaPembayaran();

                  if ($data['status_pembayaran'] === 'lunas') {
                     return $sisaTagihan < 1;
                  } elseif ($data['status_pembayaran'] === 'belum_lunas') {
                     return $sisaTagihan >= 1;
                  }

                  return true;
               })->pluck('id')->toArray();

               // Reset query dan terapkan filter ID
               $query = Reservasi::whereIn('id', $filteredIds);
            }

            // Filter berdasarkan tamu
            if (isset($data['tamu_id']) && $data['tamu_id'] !== '') {
               $query->where('tamu_id', $data['tamu_id']);
            }

            // Filter berdasarkan petugas
            if (isset($data['karyawan_id']) && $data['karyawan_id'] !== '') {
               $query->where('karyawan_id', $data['karyawan_id']);
            }

            // Ambil data reservasi
            $reservasis = $query->orderBy('created_at', 'desc')->get();

            // Siapkan data untuk laporan
            $laporanData = self::prepareLaporanData($reservasis, $data);

            // Simpan data laporan di session
            session(['laporan_data' => $laporanData]);

            // Tampilkan notifikasi
            // Notification::make()
            //    ->title('Silahkan klik tombol cetak')
            //    ->success()
            //    ->send();
            $printUrl = route('filament.hotel.resources.laporan.print');
         
    
   
            Notification::make()
               ->title('Laporan siap dicetak')
               ->body('Klik tombol di bawah untuk membuka dan mencetak laporan')
               ->success()
               ->persistent()
               ->actions([
                  NotificationAction::make('buka')
                     ->button()
                     ->label('Buka Laporan')
                     ->url($printUrl, shouldOpenInNewTab: true),
               ])
               ->send();
            // return redirect()->to($printUrl);
         })
      ;
   }

   protected static function generateColumnToggles(): array
   {
      $toggles = [];

      foreach (self::$columnOptions as $key => $label) {
         $toggles[] = Toggle::make('tampilkan_'.$key)
            ->label($label)
            ->default(true);
      }

      return $toggles;
   }


   protected static function prepareLaporanData(Collection $reservasis, array $formData): array
   {
      $rows = [];

      // Total summary
      $summary = [
         'durasi' => 0,
         'pajak' => 0,
         'diskon' => 0,
         'servis' => 0,
         'modal' => 0,
         'bersih' => 0,
         'keuntungan' => 0,
         'total_akhir' => 0,
         'pembayaran' => 0,
         'sisa_tagihan' => 0,
      ];

      // Tampilkan kolom2 yang dipilih saja
      $columns = [];

      foreach (self::$columnOptions as $key => $label) {
         if (isset($formData['tampilkan_'.$key]) && $formData['tampilkan_'.$key]) {
            $columns[$key] = $label;
         }
      }

      foreach ($reservasis as $reservasi) {
         $totalHargaBersih = $reservasi->totalHargaBersih();
         $totalHargaAkhir = $reservasi->hitungTotalHargaAkhir();
         $totalPembayaran = $reservasi->hitungTotalPembayaran();
         $sisaPembayaran = $reservasi->hitungSisaPembayaran();
         $nilaiPajak = $reservasi->hitungNilaiPajak();
         $nilaiDiskon = $reservasi->hitungNilaiDiskon();
         $nilaiServis = $reservasi->hitungNilaiServis();
         $durasi = Carbon::parse($reservasi->check_in)->diffInDays($reservasi->check_out);

         // Hitung total modal
         $totalModal = $reservasi->transaksi->sum(function ($transaksi) {
            return $transaksi->hitungTotalModal();
         });

         // Hitung keuntungan
         $keuntungan = $totalHargaBersih - $totalModal;

         // Tambahkan ke summary
         $summary['durasi'] += $durasi;
         $summary['pajak'] += $nilaiPajak;
         $summary['diskon'] += $nilaiDiskon;
         $summary['servis'] += $nilaiServis;
         $summary['modal'] += $totalModal;
         $summary['bersih'] += $totalHargaBersih;
         $summary['keuntungan'] += $keuntungan;
         $summary['total_akhir'] += $totalHargaAkhir;
         $summary['pembayaran'] += $totalPembayaran;
         $summary['sisa_tagihan'] += max(0, $sisaPembayaran);

         // Siapkan data untuk row
         $row = [];

         if (isset($columns['id'])) {
            $row['id'] = $reservasi->id;
         }

         if (isset($columns['tamu'])) {
            $row['tamu'] = $reservasi->tamu->nama ?? '-';
         }

         if (isset($columns['durasi'])) {
            $row['durasi'] = $durasi.' Malam';
         }

         if (isset($columns['pajak'])) {
            $row['pajak'] = 'Rp. '.number_format($nilaiPajak, 0, ',', '.');
         }

         if (isset($columns['diskon'])) {
            $row['diskon'] = 'Rp. '.number_format($nilaiDiskon, 0, ',', '.');
         }

         if (isset($columns['servis'])) {
            $row['servis'] = 'Rp. '.number_format($nilaiServis, 0, ',', '.');
         }

         if (isset($columns['modal'])) {
            $row['modal'] = 'Rp. '.number_format($totalModal, 0, ',', '.');
         }

         if (isset($columns['bersih'])) {
            $row['bersih'] = 'Rp. '.number_format($totalHargaBersih, 0, ',', '.');
         }

         if (isset($columns['keuntungan'])) {
            $row['keuntungan'] = 'Rp. '.number_format($keuntungan, 0, ',', '.');
         }

         if (isset($columns['total_akhir'])) {
            $row['total_akhir'] = 'Rp. '.number_format($totalHargaAkhir, 0, ',', '.');
         }

         if (isset($columns['pembayaran'])) {
            $row['pembayaran'] = 'Rp. '.number_format($totalPembayaran, 0, ',', '.');
         }

         if (isset($columns['sisa_tagihan'])) {
            $row['sisa_tagihan'] = $sisaPembayaran < 1
               ? 'LUNAS'
               : 'Rp. '.number_format($sisaPembayaran, 0, ',', '.');
         }

         if (isset($columns['status'])) {
            $row['status'] = $reservasi->statusReservasi();
         }

         if (isset($columns['pegawai'])) {
            $row['pegawai'] = $reservasi->user->name ?? '-';
         }

         if (isset($columns['tanggal'])) {
            $row['tanggal'] = $reservasi->created_at->format('d/m/Y');
         }

         $rows[] = $row;
      }

      // Format summary
      foreach ($summary as $key => $value) {
         if (in_array($key, ['pajak', 'diskon', 'servis', 'modal', 'bersih', 'keuntungan', 'total_akhir', 'pembayaran', 'sisa_tagihan'])) {
            $summary[$key] = 'Rp. '.number_format($value, 0, ',', '.');
         } elseif ($key === 'durasi') {
            $summary[$key] = $value.' Malam';
         }
      }

      // Menyiapkan informasi filter untuk ditampilkan di laporan
      $filterInfo = [
         'tanggal_mulai' => isset($formData['tanggal_mulai'])
            ? date('d/m/Y', strtotime($formData['tanggal_mulai']))
            : '-',
         'tanggal_akhir' => isset($formData['tanggal_akhir'])
            ? date('d/m/Y', strtotime($formData['tanggal_akhir']))
            : '-',
         'bulan' => isset($formData['bulan']) && ! empty($formData['bulan'])
            ? self::getNamaBulan($formData['bulan'])
            : 'Semua Bulan',
         'tahun' => isset($formData['tahun']) && ! empty($formData['tahun'])
            ? $formData['tahun']
            : 'Semua Tahun',
         'checkin_dari' => isset($formData['checkin_dari']) && ! empty($formData['checkin_dari'])
            ? date('d/m/Y', strtotime($formData['checkin_dari']))
            : '-',
         'checkin_sampai' => isset($formData['checkin_sampai']) && ! empty($formData['checkin_sampai'])
            ? date('d/m/Y', strtotime($formData['checkin_sampai']))
            : '-',
         'checkout_dari' => isset($formData['checkout_dari']) && ! empty($formData['checkout_dari'])
            ? date('d/m/Y', strtotime($formData['checkout_dari']))
            : '-',
         'checkout_sampai' => isset($formData['checkout_sampai']) && ! empty($formData['checkout_sampai'])
            ? date('d/m/Y', strtotime($formData['checkout_sampai']))
            : '-',
         'status_reservasi' => isset($formData['status_reservasi']) && $formData['status_reservasi'] !== ''
            ? (Konfig::jsonKuRaw('reservasi_status')[$formData['status_reservasi']] ?? $formData['status_reservasi'])
            : 'Semua Status',
         'status_pembayaran' => isset($formData['status_pembayaran']) && ! empty($formData['status_pembayaran'])
            ? ($formData['status_pembayaran'] === 'lunas' ? 'Lunas' : 'Belum Lunas')
            : 'Semua Status',
         'tamu' => isset($formData['tamu_id']) && $formData['tamu_id'] !== ''
            ? (Tamu::find($formData['tamu_id'])?->nama ?? '-')
            : 'Semua Tamu',
         'petugas' => isset($formData['karyawan_id']) && $formData['karyawan_id'] !== ''
            ? (User::find($formData['karyawan_id'])?->name ?? '-')
            : 'Semua Petugas',
      ];

      return [
         'columns' => $columns,
         'rows' => $rows,
         'summary' => $summary,
         'filters' => $filterInfo,
      ];
   }


   protected static function getNamaBulan($bulan): string
   {
      $namaBulan = [
         '1' => 'Januari',
         '2' => 'Februari',
         '3' => 'Maret',
         '4' => 'April',
         '5' => 'Mei',
         '6' => 'Juni',
         '7' => 'Juli',
         '8' => 'Agustus',
         '9' => 'September',
         '10' => 'Oktober',
         '11' => 'November',
         '12' => 'Desember',
      ];

      return $namaBulan[$bulan] ?? $bulan;
   }
}
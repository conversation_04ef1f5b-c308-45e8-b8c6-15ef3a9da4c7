<?php

namespace Modules\RajaCms\View\Components;

use Modules\RajaCms\Models\Cms as CmsModel;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Cms extends Component
{
    public $type = "home";
    public $by = "status";
    public $key = "home";
    public $param = [];

    public function __construct($type, $by = null, $key = null, $param = null)
    {
        // type tersedia
        // home  -> getHome()
        // artikel -> getArtikel()
        // artikel-list -> getArtikelList()
        // halaman -> getHalaman()

        $this->by = $by;
        $this->key = $key;
        $this->type = $type;
        $this->param = $param;
    }

    public function getArtikelList()
    {
        $kue = CmsModel::where('jenis', 'ARTIKEL');

        // param tersedia
        // pagination = true/false  // default = true
        // perhalaman = 10  // default = 10
        // orderby = created_at // default = created_at
        // order = desc // default = desc
        // kategori = null // default = null / semua

        if ($this->param) {
            $perhalaman = $this->param['perhalaman'] ?? 10;
            $orderby = $this->param['orderby'] ?? 'created_at';
            $order = $this->param['order'] ?? 'desc';
            $kategori = $this->param['kategori'] ?? null;

            if ($perhalaman) {
                $kue->limit($perhalaman);
            }
            if ($orderby) {
                $kue->orderBy($orderby, $order);
            }
            if ($kategori) {
                $kue->where('kategori_id', $kategori);
            }
            if ($this->param['judul']) {
                $judul = $this->param['judul'];
            } else {
                $judul = 'Artikel Terbaru';
            }
        }

        $data = $kue->get();

        return view('tema::modul.cms.artikel-list', [
            'data' => $data,
            'judul' => $judul ?? 'Artikel Terbaru'
        ]);
    }

    public function getArtikel()
    {
        $data = CmsModel::where($this->by, $this->key)
            ->where('jenis', 'ARTIKEL')
            ->first();
        return view('tema::modul.cms.artikel', ['data' => $data]);
    }

    public function getHome()
    {
        $data = CmsModel::where('status', 'home')->first();
        return view('tema::modul.cms.home', ['data' => $data]);
    }

    public function getHalaman()
    {
        $data = CmsModel::where($this->by, $this->key)
            ->where('jenis', 'halaman')
            ->first();
        return view('tema::modul.cms.halaman', ['data' => $data]);
    }

    public function render(): View|Closure|string
    {
        if ($this->type === 'home') {
            return $this->getHome();
        } elseif ($this->type === 'artikel') {
            return $this->getArtikel();
        } elseif ($this->type === 'artikel-list') {
            return $this->getArtikelList();
        } elseif ($this->type === 'halaman') {
            return $this->getHalaman();
        }

        return view('components.kosong');
    }
} 
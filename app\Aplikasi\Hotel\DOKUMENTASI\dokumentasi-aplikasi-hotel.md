# Dokumentasi Aplikasi Hotel dengan Filament PHP

## Daftar Isi
- [Pendahuluan](#pendahuluan)
- [Persyaratan Sistem](#persyaratan-sistem)
- [Instalasi](#instalasi)
- [Struktur Aplikasi](#struktur-aplikasi)
- [Fi<PERSON> Utama](#fitur-utama)
  - [Dashboard](#dashboard)
  - [Manajemen Kamar](#manajemen-kamar)
  - [Manajemen Reservasi](#manajemen-reservasi)
  - [Manajemen Tamu](#manajemen-tamu)
  - [Transaksi](#transaksi)
  - [La<PERSON>an](#laporan)
- [Panduan Penggunaan](#panduan-penggunaan)
- [Pengembangan Lanjutan](#pengembangan-lanjutan)
  - [Menambahkan Resource Baru](#menambahkan-resource-baru)
  - [Membuat Widget Kustom](#membuat-widget-kustom)
  - [Membuat <PERSON>](#membuat-halaman-kustom)
  - [Livewire Components](#livewire-components)
- [Referensi API](#referensi-api)
- [Troubleshooting](#troubleshooting)
- [FAQ](#faq)

## Pendahuluan

Aplikasi Hotel adalah sistem manajemen perhotelan berbasis web yang dibangun menggunakan Laravel 10 dan Filament PHP v3.2. Aplikasi ini dirancang untuk memudahkan pengelolaan operasional hotel sehari-hari, termasuk manajemen kamar, reservasi, tamu, serta pencatatan transaksi dan laporan.

Dengan antarmuka yang intuitif dan responsif, aplikasi ini cocok untuk berbagai jenis properti penginapan seperti hotel, guest house, villa, atau homestay. Aplikasi ini menggunakan struktur direktori kustom yang memisahkan komponen berdasarkan fungsionalitasnya, sehingga lebih mudah dikelola dan dikembangkan.

## Persyaratan Sistem

Berikut persyaratan sistem untuk menjalankan Aplikasi Hotel:

- PHP 8.2 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi / MariaDB 10.3 atau lebih tinggi
- Composer 2.0 atau lebih tinggi
- Node.js 16.0 atau lebih tinggi (untuk kompilasi aset)
- Laravel 10.x
- Web server (Apache/Nginx)
- Ekstensi PHP: BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD

## Instalasi

Berikut adalah langkah-langkah untuk menginstal Aplikasi Hotel:

### 1. Clone Repositori

```bash
git clone https://repository-url/hotel-app.git
cd hotel-app
```

### 2. Instalasi Dependensi

```bash
composer install
npm install
npm run build
```

### 3. Konfigurasi Lingkungan

```bash
cp .env.example .env
php artisan key:generate
```

Edit file `.env` dan sesuaikan konfigurasi database:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hotel_db
DB_USERNAME=root
DB_PASSWORD=
```

### 4. Migrasi dan Seeding Database

```bash
php artisan migrate --seed
```

### 5. Instalasi BladeUI Icons

```bash
composer require blade-ui-kit/blade-icons
composer require blade-ui-kit/blade-heroicons
```

### 6. Pengaturan Penyimpanan

```bash
php artisan storage:link
```

### 7. Pendaftaran Service Provider

Buka file `config/app.php` dan tambahkan service provider berikut di array `providers`:

```php
App\Aplikasi\Hotel\HotelServiceProvider::class,
```

### 8. Jalankan Aplikasi

```bash
php artisan serve
```

Akses aplikasi di `http://localhost:8000/hotel`.

### Akun Default

Setelah menjalankan seeder, akun default yang tersedia:

- **Email**: <EMAIL>
- **Password**: password

## Struktur Aplikasi

Aplikasi Hotel menggunakan struktur direktori kustom untuk mengorganisir komponen berdasarkan fungsionalitasnya:

```
app/
├── Aplikasi/
│   ├── Hotel/
│   │   ├── config/
│   │   │   └── aplikasi.php
│   │   ├── Livewire/
│   │   ├── Pages/
│   │   │   └── Dashboard.php
│   │   ├── Resources/
│   │   │   ├── KamarResource.php
│   │   │   ├── KamarResource/
│   │   │   │   ├── Pages/
│   │   │   │   └── RelationManagers/
│   │   │   ├── ReservasiResource.php
│   │   │   ├── ReservasiResource/
│   │   │   │   ├── Pages/
│   │   │   │   └── RelationManagers/
│   │   │   └── ...
│   │   ├── views/
│   │   │   ├── css/
│   │   │   ├── js/
│   │   │   └── dashboard.blade.php
│   │   ├── Widgets/
│   │   │   ├── LatestTransactions.php
│   │   │   ├── OccupancyChart.php
│   │   │   ├── RecentReservations.php
│   │   │   ├── RoomStatus.php
│   │   │   └── StatsOverview.php
│   │   ├── HotelPanelProvider.php
│   │   └── HotelServiceProvider.php
├── Console/
├── Exceptions/
├── Http/
├── Models/
│   ├── extend/
│   │   ├── Kamar.php
│   │   └── KamarTipe.php
│   ├── Produk.php
│   ├── ProdukKategori.php
│   ├── Reservasi.php
│   ├── Tamu.php
│   └── Transaksi.php
└── ...
```

Struktur ini memisahkan komponen aplikasi hotel dari sistem inti Laravel, sehingga lebih modular dan mudah dikelola.

## Fitur Utama

### Dashboard

Dashboard menyediakan gambaran umum tentang operasional hotel, menampilkan:

- Statistik utama (tamu check-in hari ini, kamar tersedia, pendapatan hari ini, reservasi bulan ini)
- Grafik okupansi kamar selama 7 hari terakhir
- Daftar reservasi terbaru
- Status kamar berdasarkan tipe
- Transaksi terbaru

### Manajemen Kamar

Fitur manajemen kamar mencakup:

- Pengelolaan tipe kamar (standar, deluxe, suite, dll.)
- Pengelolaan kamar individual
- Penetapan harga kamar
- Pengelolaan fasilitas kamar
- Melihat status ketersediaan kamar

### Manajemen Reservasi

Fitur manajemen reservasi mencakup:

- Pembuatan reservasi baru
- Pengecekan ketersediaan kamar
- Proses check-in dan check-out
- Pembaruan status reservasi
- Riwayat reservasi tamu

### Manajemen Tamu

Fitur manajemen tamu mencakup:

- Pendaftaran tamu baru
- Pengelolaan data tamu
- Riwayat menginap tamu
- Preferensi tamu

### Transaksi

Fitur transaksi mencakup:

- Pencatatan pembayaran
- Penambahan biaya tambahan
- Pembuatan invoice
- Riwayat transaksi

### Laporan

Fitur laporan mencakup:

- Laporan pendapatan
- Laporan okupansi
- Laporan tamu
- Laporan reservasi
- Filter dan ekspor laporan

## Panduan Penggunaan

### Login ke Aplikasi

1. Buka browser dan akses URL aplikasi (`http://yourdomain.com/hotel` atau `http://localhost:8000/hotel` jika menjalankan secara lokal)
2. Masukkan kredensial login (email dan password)
3. Klik tombol "Log in"

### Menggunakan Dashboard

Dashboard adalah halaman pertama yang Anda lihat setelah login. Dashboard menampilkan informasi penting tentang operasional hotel:

- **Statistik Utama**: Melihat jumlah tamu check-in hari ini, kamar tersedia, pendapatan hari ini, dan reservasi bulan ini
- **Grafik Okupansi**: Melihat persentase okupansi kamar selama 7 hari terakhir
- **Reservasi Terbaru**: Melihat daftar 10 reservasi terbaru
- **Status Kamar**: Melihat status kamar berdasarkan tipe (tersedia vs terpakai)
- **Transaksi Terbaru**: Melihat 5 transaksi terbaru

### Mengelola Kamar

#### Melihat Daftar Kamar

1. Klik "Kamar" di sidebar navigasi
2. Lihat daftar semua kamar dengan informasi dasar seperti nama, tipe, harga, dan status ketersediaan
3. Gunakan filter untuk menyaring kamar berdasarkan tipe atau status
4. Gunakan pencarian untuk menemukan kamar berdasarkan nama atau kriteria lainnya

#### Menambah Kamar Baru

1. Klik "Kamar" di sidebar navigasi
2. Klik tombol "Buat Kamar" di bagian atas halaman
3. Isi formulir dengan informasi kamar:
   - Tipe Kamar
   - Nama Kamar
   - Harga
   - Harga Modal
   - Jumlah Kamar
   - Gambar Kamar
   - Keterangan
   - Fasilitas
   - Tampilkan (toggle)
4. Klik tombol "Simpan" untuk menambahkan kamar baru

#### Mengubah Informasi Kamar

1. Klik "Kamar" di sidebar navigasi
2. Temukan kamar yang ingin diubah
3. Klik tombol "Edit" pada baris kamar tersebut
4. Perbarui informasi yang diperlukan
5. Klik tombol "Simpan" untuk menyimpan perubahan

#### Menghapus Kamar

1. Klik "Kamar" di sidebar navigasi
2. Temukan kamar yang ingin dihapus
3. Klik tombol "Hapus" pada baris kamar tersebut
4. Konfirmasi penghapusan

### Mengelola Reservasi

#### Membuat Reservasi Baru

1. Klik "Reservasi" di sidebar navigasi
2. Klik tombol "Buat Reservasi" di bagian atas halaman
3. Isi formulir reservasi:
   - Pilih Tamu (atau buat tamu baru)
   - Pilih Kamar
   - Tentukan tanggal Check-in dan Check-out
   - Tentukan Status Reservasi
   - Isi No. Invoice (atau gunakan yang otomatis)
   - Tambahkan keterangan jika diperlukan
4. Klik tombol "Simpan" untuk membuat reservasi baru

#### Melakukan Check-in

1. Klik "Reservasi" di sidebar navigasi
2. Temukan reservasi yang ingin di-check-in
3. Klik tombol "Check-in" pada baris reservasi tersebut
4. Konfirmasi proses check-in
5. Status reservasi akan berubah menjadi "Check In" (CI)

#### Melakukan Check-out

1. Klik "Reservasi" di sidebar navigasi
2. Temukan reservasi yang ingin di-check-out
3. Klik tombol "Check-out" pada baris reservasi tersebut
4. Konfirmasi proses check-out
5. Status reservasi akan berubah menjadi "Check Out" (CO)
6. Setelah pembayaran selesai, ubah status menjadi "Successful Check Out" (SCO)

### Mengelola Tamu

#### Menambah Tamu Baru

1. Klik "Tamu" di sidebar navigasi
2. Klik tombol "Buat Tamu" di bagian atas halaman
3. Isi formulir dengan informasi tamu:
   - Nama
   - Alamat
   - Telepon
   - Email
   - Nomor Identitas
   - Jenis Identitas
   - Keterangan
4. Klik tombol "Simpan" untuk menambahkan tamu baru

#### Melihat Riwayat Tamu

1. Klik "Tamu" di sidebar navigasi
2. Temukan tamu yang ingin dilihat riwayatnya
3. Klik tombol "Lihat" pada baris tamu tersebut
4. Buka tab "Reservasi" untuk melihat riwayat menginap tamu

### Mencatat Transaksi

#### Menambah Transaksi Baru

1. Klik "Transaksi" di sidebar navigasi
2. Klik tombol "Buat Transaksi" di bagian atas halaman
3. Isi formulir transaksi:
   - Pilih Reservasi
   - Nama Item
   - Jumlah
   - Harga
   - Metode Pembayaran
   - Catatan
4. Klik tombol "Simpan" untuk mencatat transaksi baru

### Melihat Laporan

1. Klik "Laporan" di sidebar navigasi
2. Pilih jenis laporan yang ingin dilihat (Pendapatan, Okupansi, Tamu, Reservasi)
3. Atur filter tanggal dan parameter lainnya
4. Klik tombol "Terapkan Filter" untuk menampilkan laporan
5. Gunakan tombol "Ekspor" untuk mengunduh laporan dalam format Excel atau PDF

## Pengembangan Lanjutan

### Menambahkan Resource Baru

Untuk menambahkan resource baru ke aplikasi Hotel, ikuti langkah-langkah berikut:

1. Jalankan perintah Artisan untuk membuat resource baru:

```bash
php artisan make:filament-resource NamaResource --generate --simple --path=App/Aplikasi/Hotel/Resources
```

Contoh membuat resource untuk Layanan Hotel:

```bash
php artisan make:filament-resource LayananResource --generate --simple --path=App/Aplikasi/Hotel/Resources
```

2. Sesuaikan model untuk resource baru:

```php
// app/Models/Layanan.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Layanan extends Model
{
    protected $table = 'layanan';
    
    protected $fillable = [
        'nama',
        'deskripsi',
        'harga',
        'status',
    ];
    
    // Relasi dan metode lainnya
}
```

3. Sesuaikan resource yang sudah dibuat:

```php
// app/Aplikasi/Hotel/Resources/LayananResource.php
namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel\Resources\LayananResource\Pages;
use App\Models\Layanan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LayananResource extends Resource
{
    protected static ?string $model = Layanan::class;

    protected static ?string $navigationIcon = 'heroicon-o-sparkles';

    protected static ?string $navigationGroup = 'Pengelolaan Hotel';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nama')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('deskripsi')
                    ->maxLength(65535),
                Forms\Components\TextInput::make('harga')
                    ->required()
                    ->numeric(),
                Forms\Components\Toggle::make('status')
                    ->required()
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->searchable(),
                Tables\Columns\TextColumn::make('harga')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLayanans::route('/'),
            'create' => Pages\CreateLayanan::route('/create'),
            'view' => Pages\ViewLayanan::route('/{record}'),
            'edit' => Pages\EditLayanan::route('/{record}/edit'),
        ];
    }
}
```

4. Buat migrasi untuk model baru:

```bash
php artisan make:migration create_layanan_table
```

```php
// database/migrations/xxxx_xx_xx_create_layanan_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('layanan', function (Blueprint $table) {
            $table->id();
            $table->string('nama');
            $table->text('deskripsi')->nullable();
            $table->decimal('harga', 10, 2);
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('layanan');
    }
};
```

5. Jalankan migrasi:

```bash
php artisan migrate
```

### Membuat Widget Kustom

Untuk membuat widget kustom, ikuti langkah-langkah berikut:

1. Jalankan perintah Artisan untuk membuat widget baru:

```bash
php artisan make:filament-widget NamaWidget --path=App/Aplikasi/Hotel/Widgets
```

Contoh membuat widget untuk Service Rating:

```bash
php artisan make:filament-widget ServiceRating --path=App/Aplikasi/Hotel/Widgets
```

2. Sesuaikan widget yang sudah dibuat:

```php
// app/Aplikasi/Hotel/Widgets/ServiceRating.php
namespace App\Aplikasi\Hotel\Widgets;

use App\Models\Ulasan;
use Filament\Widgets\ChartWidget;

class ServiceRating extends ChartWidget
{
    protected static ?string $heading = 'Rating Layanan Hotel';
    
    protected static ?int $sort = 6;
    
    protected function getData(): array
    {
        $ratingsData = Ulasan::selectRaw('ROUND(rating) as rating_rounded, COUNT(*) as count')
            ->groupBy('rating_rounded')
            ->orderBy('rating_rounded')
            ->pluck('count', 'rating_rounded')
            ->toArray();
        
        $labels = [];
        $data = [];
        
        for ($i = 1; $i <= 5; $i++) {
            $labels[] = "$i Bintang";
            $data[] = $ratingsData[$i] ?? 0;
        }
        
        return [
            'datasets' => [
                [
                    'label' => 'Jumlah Rating',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 205, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }
    
    protected function getType(): string
    {
        return 'pie';
    }
}
```

3. Tambahkan widget baru ke Dashboard:

```php
// app/Aplikasi/Hotel/Pages/Dashboard.php
namespace App\Aplikasi\Hotel\Pages;

use App\Aplikasi\Hotel\Widgets\ServiceRating;
// Import widget lainnya...
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    // Konfigurasi lainnya...
    
    protected function getWidgets(): array
    {
        return [
            StatsOverview::class,
            OccupancyChart::class,
            RecentReservations::class,
            RoomStatus::class,
            LatestTransactions::class,
            ServiceRating::class, // Tambahkan widget baru
        ];
    }
}
```

### Membuat Halaman Kustom

Untuk membuat halaman kustom, ikuti langkah-langkah berikut:

1. Jalankan perintah Artisan untuk membuat halaman baru:

```bash
php artisan make:filament-page NamaHalaman --path=App/Aplikasi/Hotel/Pages
```

Contoh membuat halaman untuk Analisis Pendapatan:

```bash
php artisan make:filament-page RevenueAnalysis --path=App/Aplikasi/Hotel/Pages
```

2. Sesuaikan halaman yang sudah dibuat:

```php
// app/Aplikasi/Hotel/Pages/RevenueAnalysis.php
namespace App\Aplikasi\Hotel\Pages;

use App\Models\Transaksi;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;

class RevenueAnalysis extends Page implements HasForms
{
    use InteractsWithForms;
    
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    
    protected static ?string $navigationLabel = 'Analisis Pendapatan';
    
    protected static ?string $navigationGroup = 'Laporan';
    
    protected static string $view = 'hotel::pages.revenue-analysis';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'period' => 'bulan',
            'date_start' => Carbon::now()->startOfMonth()->format('Y-m-d'),
            'date_end' => Carbon::now()->endOfMonth()->format('Y-m-d'),
        ]);
        
        $this->generateReport();
    }
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Filter Laporan')
                    ->schema([
                        Select::make('period')
                            ->label('Periode')
                            ->options([
                                'hari' => 'Harian',
                                'bulan' => 'Bulanan',
                                'tahun' => 'Tahunan',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function () {
                                $this->generateReport();
                            }),
                        DatePicker::make('date_start')
                            ->label('Tanggal Mulai')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function () {
                                $this->generateReport();
                            }),
                        DatePicker::make('date_end')
                            ->label('Tanggal Akhir')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function () {
                                $this->generateReport();
                            }),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }
    
    public function generateReport(): void
    {
        if (empty($this->data)) {
            return;
        }
        
        $period = $this->data['period'] ?? 'bulan';
        $dateStart = $this->data['date_start'] ?? Carbon::now()->startOfMonth();
        $dateEnd = $this->data['date_end'] ?? Carbon::now()->endOfMonth();
        
        // Query untuk mendapatkan data pendapatan
        $format = match ($period) {
            'hari' => '%Y-%m-%d',
            'bulan' => '%Y-%m',
            'tahun' => '%Y',
            default => '%Y-%m',
        };
        
        $dateLabel = match ($period) {
            'hari' => 'date_format(created_at, "%d %b %Y")',
            'bulan' => 'date_format(created_at, "%b %Y")',
            'tahun' => 'date_format(created_at, "%Y")',
            default => 'date_format(created_at, "%b %Y")',
        };
        
        $this->revenueData = Transaksi::whereBetween('created_at', [$dateStart, $dateEnd])
            ->selectRaw("$dateLabel as date")
            ->selectRaw('date_format(created_at, ?) as period', [$format])
            ->selectRaw('sum(harga) as total')
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->toArray();
    }
    
    public function getViewData(): array
    {
        return [
            'revenueData' => $this->revenueData ?? [],
        ];
    }
}
```

3. Buat file view untuk halaman kustom:

```php
// app/Aplikasi/Hotel/views/pages/revenue-analysis.blade.php
<x-filament-panels::page>
    <div class="space-y-6">
        {{ $this->form }}
        
        @if(!empty($revenueData))
            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-lg font-medium mb-4">Grafik Pendapatan</h2>
                <div id="revenue-chart" style="height: 300px;"></div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-4">
                <h2 class="text-lg font-medium mb-4">Tabel Pendapatan</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Periode</th>
                                <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Pendapatan</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($revenueData as $item)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['date'] }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">Rp {{ number_format($item['total'], 0, ',', '.') }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Total</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                                    Rp {{ number_format(collect($revenueData)->sum('total'), 0, ',', '.') }}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        @else
            <div class="bg-white rounded-lg shadow p-4 text-center text-gray-500">
                Tidak ada data untuk ditampilkan
            </div>
        @endif
    </div>
    
    @pushOnce('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('revenue-chart')) {
                const chartData = @json($revenueData);
                
                if (chartData.length === 0) return;
                
                const ctx = document.getElementById('revenue-chart').getContext('2d');
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: chartData.map(item => item.date),
                        datasets: [{
                            label: 'Pendapatan',
                            data: chartData.map(item => item.total),
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp ' + value.toLocaleString('id-ID');
                                    }
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return 'Pendapatan: Rp ' + context.raw.toLocaleString('id-ID');
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
    @endPushOnce
</x-filament-panels::page>
```

4. Daftarkan halaman di Panel Provider:

```php
// app/Aplikasi/Hotel/HotelPanelProvider.php
namespace App\Aplikasi\Hotel;

use App\Aplikasi\Hotel\Pages\Dashboard;
use App\Aplikasi\Hotel\Pages\RevenueAnalysis;
// Import lainnya...
use Filament\Panel;
use Filament\PanelProvider;

class HotelPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            // Konfigurasi lainnya...
            
            // Mendaftarkan halaman utama
            ->pages([
                Dashboard::class,
                RevenueAnalysis::class, // Tambahkan halaman baru
            ])
            
            // Konfigurasi lainnya...
        ;
    }
}
```

### Livewire Components

Untuk membuat komponen Livewire kustom, ikuti langkah-langkah berikut:

1. Jalankan perintah Artisan untuk membuat komponen Livewire baru:

```bash
php artisan make:livewire Hotel/BookingCalendar --path=App/Aplikasi/Hotel/Livewire
```

2. Sesuaikan komponen Livewire yang sudah dibuat:

```php
// app/Aplikasi/Hotel/Livewire/BookingCalendar.php
namespace App\Aplikasi\Hotel\Livewire;

use App\Models\Reservasi;
use App\Models\extend\Kamar;
use Carbon\Carbon;
use Livewire\Component;

class BookingCalendar extends Component
{
    public $month;
    public $year;
    public $kamar_id;
    public $kamarList;
    public $calendarData = [];
    
    public function mount()
    {
        $this->month = Carbon::now()->month;
        $this->year = Carbon::now()->year;
        $this->kamarList = Kamar::orderBy('nama')->get();
        $this->loadCalendarData();
    }
    
    public function nextMonth()
    {
        $date = Carbon::createFromDate($this->year, $this->month, 1)->addMonth();
        $this->month = $date->month;
        $this->year = $date->year;
        $this->loadCalendarData();
    }
    
    public function prevMonth()
    {
        $date = Carbon::createFromDate($this->year, $this->month, 1)->subMonth();
        $this->month = $date->month;
        $this->year = $date->year;
        $this->loadCalendarData();
    }
    
    public function updatedKamarId()
    {
        $this->loadCalendarData();
    }
    
    protected function loadCalendarData()
    {
        $date = Carbon::createFromDate($this->year, $this->month, 1);
        $daysInMonth = $date->daysInMonth;
        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();
        
        $this->calendarData = [];
        
        // Ambil data reservasi untuk bulan yang dipilih
        $query = Reservasi::whereBetween('check_in', [$startOfMonth, $endOfMonth])
            ->orWhereBetween('check_out', [$startOfMonth, $endOfMonth])
            ->with(['tamu', 'kamar']);
            
        if ($this->kamar_id) {
            $query->where('kamar_id', $this->kamar_id);
        }
        
        $reservations = $query->get();
        
        // Siapkan data kalender
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $currentDate = Carbon::createFromDate($this->year, $this->month, $day);
            
            $dayReservations = $reservations->filter(function ($reservation) use ($currentDate) {
                $checkIn = Carbon::parse($reservation->check_in);
                $checkOut = Carbon::parse($reservation->check_out);
                
                return $currentDate->between($checkIn, $checkOut) || 
                       $currentDate->isSameDay($checkIn) || 
                       $currentDate->isSameDay($checkOut);
            });
            
            $this->calendarData[] = [
                'date' => $currentDate->format('Y-m-d'),
                'day' => $day,
                'weekday' => $currentDate->format('D'),
                'reservations' => $dayReservations->toArray(),
            ];
        }
    }
    
    public function render()
    {
        return view('hotel::livewire.booking-calendar', [
            'currentMonth' => Carbon::createFromDate($this->year, $this->month, 1)->format('F Y'),
        ]);
    }
}
```

3. Buat file view untuk komponen Livewire:

```php
// app/Aplikasi/Hotel/views/livewire/booking-calendar.blade.php
<div class="space-y-4">
    <div class="flex justify-between items-center">
        <h2 class="text-lg font-semibold">{{ $currentMonth }}</h2>
        <div class="flex space-x-2">
            <button wire:click="prevMonth" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                <span class="sr-only">Bulan Sebelumnya</span>
                &larr;
            </button>
            <button wire:click="nextMonth" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                <span class="sr-only">Bulan Berikutnya</span>
                &rarr;
            </button>
        </div>
    </div>
    
    <div class="mb-4">
        <label for="kamar_id" class="block text-sm font-medium text-gray-700">Filter Kamar</label>
        <select id="kamar_id" wire:model.live="kamar_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="">Semua Kamar</option>
            @foreach($kamarList as $kamar)
                <option value="{{ $kamar->id }}">{{ $kamar->nama }}</option>
            @endforeach
        </select>
    </div>
    
    <div class="grid grid-cols-7 gap-2 font-medium text-center">
        <div>Min</div>
        <div>Sen</div>
        <div>Sel</div>
        <div>Rab</div>
        <div>Kam</div>
        <div>Jum</div>
        <div>Sab</div>
    </div>
    
    <div class="grid grid-cols-7 gap-2">
        @php
            $firstDay = \Carbon\Carbon::createFromDate($year, $month, 1);
            $startingDay = $firstDay->dayOfWeek;
            
            // Tambahkan sel kosong untuk hari sebelum awal bulan
            for ($i = 0; $i < $startingDay; $i++) {
                echo '<div class="border rounded-lg bg-gray-50 p-2 min-h-[100px]"></div>';
            }
        @endphp
        
        @foreach($calendarData as $day)
            <div class="border rounded-lg p-2 min-h-[100px] {{ \Carbon\Carbon::parse($day['date'])->isWeekend() ? 'bg-gray-50' : 'bg-white' }}">
                <div class="text-sm font-semibold">{{ $day['day'] }}</div>
                
                @if(count($day['reservations']) > 0)
                    <div class="mt-1 space-y-1">
                        @foreach($day['reservations'] as $reservation)
                            <div class="text-xs p-1 rounded-sm {{ $reservation['status_reservasi'] == 'CI' ? 'bg-blue-100' : 'bg-green-100' }}">
                                {{ $reservation['tamu']['nama'] ?? 'Tamu' }} - 
                                {{ $reservation['kamar']['nama'] ?? 'Kamar' }}
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        @endforeach
    </div>
</div>
```

4. Gunakan komponen Livewire di halaman lain:

```php
// app/Aplikasi/Hotel/Pages/BookingOverview.php
namespace App\Aplikasi\Hotel\Pages;

use Filament\Pages\Page;

class BookingOverview extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    
    protected static ?string $navigationLabel = 'Kalender Booking';
    
    protected static ?string $navigationGroup = 'Reservasi & Tamu';
    
    protected static string $view = 'hotel::pages.booking-overview';
}
```

```php
// app/Aplikasi/Hotel/views/pages/booking-overview.blade.php
<x-filament-panels::page>
    <div class="bg-white p-4 rounded-lg shadow">
        <livewire:app.aplikasi.hotel.livewire.booking-calendar />
    </div>
</x-filament-panels::page>
```

## Referensi API

### Model Kamar

```php
// Membuat kamar baru
$kamar = Kamar::create([
    'jenis' => 'KAMAR',
    'toko_id' => 1,
    'kategori_id' => $tipeKamar->id,
    'nama' => 'Kamar 101 - Deluxe',
    'harga' => 500000,
    'harga_modal' => 350000,
    'stok' => 1,
    'fasilitas' => ['wifi', 'ac', 'tv'],
    'spesifikasi' => ['luas' => '24m2', 'kapasitas' => '2 orang'],
    'tampil' => true,
]);

// Memeriksa ketersediaan kamar
$isAvailable = $kamar->isAvailable(
    Carbon::parse('2023-10-25 14:00:00'), 
    Carbon::parse('2023-10-28 12:00:00')
);
```

### Model Reservasi

```php
// Membuat reservasi baru
$reservasi = Reservasi::create([
    'tamu_id' => $tamu->id,
    'kamar_id' => $kamar->id,
    'check_in' => Carbon::parse('2023-10-25 14:00:00')->format('Y-m-d H:i'),
    'check_out' => Carbon::parse('2023-10-28 12:00:00')->format('Y-m-d H:i'),
    'status_reservasi' => 'CR',
    'no_invoice' => 'INV-' . Carbon::now()->format('Ymd') . '-' . rand(1000, 9999),
]);

// Mengubah status reservasi
$reservasi->update([
    'status_reservasi' => 'CI', // Check In
]);
```

### Helpers

```php
// Cek ketersediaan rute
if (route_exists('filament.hotel.resources.reservasi.view')) {
    // Route ada
}

// Format uang
function formatRupiah($angka) {
    return 'Rp ' . number_format($angka, 0, ',', '.');
}
```

## Troubleshooting

### Route not found error

**Problem**: `Route [filament.hotel.resources.reservasi.view] not defined.`

**Solusi**:
1. Pastikan resource `ReservasiResource` sudah terdaftar dengan benar di `HotelPanelProvider`
2. Pastikan slug resource didefinisikan dengan benar:
   ```php
   protected static ?string $slug = 'reservasi';
   ```
3. Tambahkan fungsi helper untuk cek keberadaan route:
   ```php
   function route_exists($name) {
       return app('router')->has($name);
   }
   ```
4. Gunakan pendekatan defensive saat menetapkan URL:
   ```php
   ->url(fn (Reservasi $record): string => 
       route_exists('filament.hotel.resources.reservasi.view') 
           ? route('filament.hotel.resources.reservasi.view', ['record' => $record->id])
           : '#')
   ```

### Widget tidak muncul di dashboard

**Problem**: Widget terdaftar tapi tidak muncul di dashboard.

**Solusi**:
1. Pastikan Anda menggunakan kelas dasar yang benar untuk Dashboard:
   ```php
   use Filament\Pages\Dashboard as BaseDashboard;
   
   class Dashboard extends BaseDashboard
   {
       // ...
   }
   ```
2. Pastikan widget terdaftar di metode `getWidgets()`:
   ```php
   protected function getWidgets(): array
   {
       return [
           StatsOverview::class,
           OccupancyChart::class,
           // Widget lainnya...
       ];
   }
   ```
3. Pastikan widget meng-extend kelas dasar yang benar:
   ```php
   // Untuk widget statistik
   use Filament\Widgets\StatsOverviewWidget as BaseWidget;
   
   // Untuk widget chart
   use Filament\Widgets\ChartWidget;
   
   // Untuk widget tabel
   use Filament\Widgets\TableWidget as BaseWidget;
   ```
4. Bersihkan cache:
   ```bash
   php artisan view:clear
   php artisan cache:clear
   php artisan config:clear
   php artisan optimize:clear
   ```

### Error migrasi database

**Problem**: Error saat menjalankan migrasi database.

**Solusi**:
1. Pastikan konfigurasi database di file `.env` benar
2. Periksa struktur migrasi untuk typo atau kesalahan lainnya
3. Coba reset migrasi dan jalankan ulang:
   ```bash
   php artisan migrate:fresh --seed
   ```
4. Jika menggunakan MySQL 5.7 ke bawah, pastikan panjang karakter pada migrasi tidak melebihi batasan:
   ```php
   // Sebelum menjalankan migrasi
   Schema::defaultStringLength(191);
   ```

### Masalah tampilan atau CSS/JS tidak dimuat

**Problem**: Tampilan tidak muncul dengan benar atau CSS/JS tidak dimuat.

**Solusi**:
1. Pastikan Anda menjalankan `npm run build` untuk mengkompilasi aset
2. Periksa apakah symlink storage sudah dibuat:
   ```bash
   php artisan storage:link
   ```
3. Pastikan path view benar di service provider:
   ```php
   $this->loadViewsFrom(__DIR__ . '/views', 'hotel');
   ```
4. Periksa console browser untuk error JavaScript
5. Pastikan template view menggunakan blade directive yang benar:
   ```php
   <x-filament-panels::page>
       {{-- Konten --}}
   </x-filament-panels::page>
   ```

## FAQ

### 1. Bagaimana cara mengubah logo aplikasi?

Anda dapat mengubah logo aplikasi dengan mengedit `HotelPanelProvider`:

```php
public function panel(Panel $panel): Panel
{
    return $panel
        // Konfigurasi lainnya...
        
        ->brandLogo(fn () => view('hotel::components.logo'))
        ->favicon(asset('images/favicon.ico'))
        
        // Konfigurasi lainnya...
    ;
}
```

Kemudian buat file view untuk logo:

```php
// app/Aplikasi/Hotel/views/components/logo.blade.php
<img src="{{ asset('images/hotel-logo.png') }}" alt="Hotel Logo" class="h-10">
```

### 2. Bagaimana cara mengubah tema warna aplikasi?

Anda dapat mengubah tema warna aplikasi dengan mengedit `HotelPanelProvider`:

```php
use Filament\Support\Colors\Color;

public function panel(Panel $panel): Panel
{
    return $panel
        // Konfigurasi lainnya...
        
        ->colors([
            'primary' => Color::Amber,
            'secondary' => Color::Gray,
            'success' => Color::Emerald,
            'danger' => Color::Rose,
            'warning' => Color::Orange,
            'info' => Color::Blue,
        ])
        
        // Konfigurasi lainnya...
    ;
}
```

### 3. Bagaimana cara menambahkan pengguna admin baru?

Anda dapat menambahkan pengguna admin baru melalui database seeder:

```php
// database/seeders/AdminSeeder.php
namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    public function run()
    {
        User::create([
            'name' => 'Admin Hotel',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }
}
```

Jalankan seeder:

```bash
php artisan db:seed --class=AdminSeeder
```

Atau melalui tinker:

```bash
php artisan tinker
```

```php
App\Models\User::create([
    'name' => 'Admin Hotel',
    'email' => '<EMAIL>',
    'password' => Hash::make('password'),
]);
```

### 4. Bagaimana cara mengeksport data ke Excel atau PDF?

Anda dapat menambahkan action eksport di resource:

```php
use Filament\Actions\ExportAction;
use Filament\Actions\Exports\Models\Export;
use Filament\Actions\Exports\Exporter;

class ReservasiExporter extends Exporter
{
    protected static ?string $model = Reservasi::class;
    
    public static function getColumns(): array
    {
        return [
            // Definisikan kolom yang akan diekspor
        ];
    }
}

// Di halaman ListReservasi
protected function getHeaderActions(): array
{
    return [
        ExportAction::make()
            ->exporter(ReservasiExporter::class),
        Actions\CreateAction::make(),
    ];
}
```

### 5. Bagaimana cara mengubah format tanggal/waktu?

Anda dapat mengubah format tanggal/waktu di resource:

```php
use Filament\Tables\Columns\TextColumn;

Tables\Columns\TextColumn::make('check_in')
    ->label('Check In')
    ->dateTime('d M Y, H:i') // Format tanggal/waktu
    ->sortable(),
```

Atau di model:

```php
protected $casts = [
    'check_in' => 'datetime:Y-m-d H:i',
    'check_out' => 'datetime:Y-m-d H:i',
];
```

Di bagian lain aplikasi, Anda dapat menggunakan Carbon:

```php
use Carbon\Carbon;

$formattedDate = Carbon::parse($date)->format('d M Y, H:i');
```

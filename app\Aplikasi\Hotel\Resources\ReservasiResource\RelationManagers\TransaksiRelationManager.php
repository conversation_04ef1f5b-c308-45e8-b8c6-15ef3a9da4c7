<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers;

use App\Filament\Forms\Components\Rupiah;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\Summarizers\Summarizer;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;


class TransaksiRelationManager extends RelationManager
{
    protected static string $relationship = 'transaksi';
    protected static ?string $title = 'Transaksi';
    protected static ?string $modelLabel = 'Transaksi Reservasi';
    protected static ?string $pluralModelLabel = 'Transaksi Reservasi';

    protected function getTableHeaderActions(): array
    {
        return [
            Tables\Actions\CreateAction::make(),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\TextInput::make('nama_item')->helperText('Nama item / produk, contoh : Pecel lele')
                    ->required()
                    ->maxLength(255),
                Rupiah::make('harga')->label('harga jual')
                    ->helperText('Harga jual item ke customer')
                    ->numeric()
                    ->required(),
                Rupiah::make('harga_modal')->label('harga modal')
                    ->helperText('Harga modal item')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('jumlah')
                    ->helperText('Jumlah item yang dibeli')
                    ->numeric()
                    ->required(),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaksi_invoice')
            ->columns([

                Tables\Columns\TextColumn::make('nama_item')
                    ->searchable(),
                Tables\Columns\TextColumn::make('harga')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('jumlah')
                    ->numeric()
                    ->sortable(),
               


              Tables\Columns\TextColumn::make('jenis')->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}

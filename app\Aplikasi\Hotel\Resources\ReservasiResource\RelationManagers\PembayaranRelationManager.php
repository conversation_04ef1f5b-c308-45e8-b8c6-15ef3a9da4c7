<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Table;


class PembayaranRelationManager extends RelationManager
{
    protected static string $relationship = 'pembayaran';

    protected static ?string $title = 'Pembayaran';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nama')
                    ->label('Nama Pembayaran')
                    ->default('Pembayaran kamar')
                    ->required(),
                Forms\Components\Select::make('metode_pembayaran_id')
                    ->relationship('metodePembayaran', 'nama')
                    ->required(),
                Forms\Components\TextInput::make('jumlah')
                    ->label('Jumlah Pembayaran')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('pengirim')
                    ->label('Pengirim')
                    ->placeholder('Informasi pengirim'),
                Forms\Components\TextInput::make('tujuan')
                    ->label('Tujuan')
                    ->placeholder('Informasi tujuan'),
                Forms\Components\Textarea::make('ket')
                    ->label('Keterangan')
                    ->placeholder('Keterangan tambahan'),
                Forms\Components\Hidden::make('reservasi_id')
                    ->default(fn ($livewire) => $livewire->ownerRecord->id)
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
          ->recordUrl(false)
            ->columns([
                Tables\Columns\TextColumn::make('pengirim')  ->label('pengirim') ->placeholder('-')
                  ->searchable(),
                Tables\Columns\TextColumn::make('tujuan')  ->label('tujuan')   ->searchable() ->html()->placeholder('-'),
                Tables\Columns\TextColumn::make('metodePembayaran.nama')
                    ->label('Metode Pembayaran')
                    ->searchable(),
                Tables\Columns\TextColumn::make('jumlah')
                    ->label('Jumlah')
                    ->numeric(decimalPlaces: 0)
                     ->summarize(Sum::make()->label('Total'))
                    ->sortable(),
                // Tables\Columns\BadgeColumn::make('status')
                //     ->colors([
                //         'success' => 'success',
                //         'pending' => 'warning',
                //         'failed' => 'danger'
                //     ])
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'success' => 'Success',
                        'failed' => 'Failed'
                    ])
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Pembayaran')
                    ->modalHeading('Tambah Pembayaran Baru')
                    ->modalWidth('lg')
                    ->successNotificationTitle('Pembayaran berhasil ditambahkan')
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


}
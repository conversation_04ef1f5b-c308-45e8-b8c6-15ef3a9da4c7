<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperPenjualan
 */
class Penjualan extends Model
{

  protected $fillable = [
    'id',
    'sub',
    'shift_id',
    'penjualan_invoice',
    'reservasi_id',
    'kategori',
    'nama',
    'nama_tamu',
    'tamu_id',
    'diskon',
    'pajak',
    'servis',
    'jenis_pesanan',
    'ket_pesanan',
    'karyawan_id',
    'lokasi',
    'status',
    'created_at',
    'updated_at'

  ];
  protected $table = 'penjualan';

  protected static function booted()
  {


    static::addGlobalScope('kategori', function (\Illuminate\Database\Eloquent\Builder $builder) {
      $builder->where('kategori', 'KASIR');
    });


    static::creating(function ($model) {
      $model->kategori = 'KASIR';
    });

    static::updating(function ($model) {
      $model->kategori = 'KASIR';
    });
  }

  public function metodepembayaran()
  {
    return $this->belongsTo(MetodePembayaran::class, 'id');
  }

  public function refund()
  {
    return $this->hasOne(Refund::class, 'penjualan_id', 'id');
  }

  public function transaksi()
  {
    return $this->hasMany(Transaksi::class, 'penjualan_id', 'id');
  }

  public function pembayaran()
  {
    return $this->hasMany(Pembayaran::class);
  }

  public function user()
  {
    return $this->belongsTo(User::class, 'karyawan_id');

  }

  public function member()
  {
    return $this->belongsTo(Tamu::class, 'tamu_id');
  }

  public function grandTotal(): float
  {
    // Hitung total harga dari semua item transaksi
    $totalItem = $this->transaksi->sum(function ($item) {
      return $item->jumlah * $item->harga;
    });

    // Tambahkan pajak dan biaya servis
    $totalSebelumDiskon = $totalItem +
      ($this->pajak ?? 0) +
      ($this->servis ?? 0);

    // Kurangi diskon
    $grandTotal = $totalSebelumDiskon - ($this->diskon ?? 0);

    // Pastikan total tidak negatif
    return max(0, $grandTotal);
  }


  public function getGrandTotalAttribute(): float
  {
    return $this->grandTotal();
  }



  public function hitungNilaiPajak(): float
  {

    return $this->totalHargaBersih() * ($this->pajak / 100);
  }

  public function hitungNilaiDiskon(): float
  {
    return $this->totalHargaBersih() * ($this->diskon / 100);
  }

  public function hitungNilaiServis(): float
  {
    return $this->totalHargaBersih() * ($this->servis / 100);
  }


  public function totalHarga()
  {
    // Menghitung subtotal dengan mengalikan jumlah * harga untuk setiap transaksi
    $subtotal = $this->transaksi->sum(function ($transaksi) {
      return $transaksi->jumlah * $transaksi->harga;
    });
    $totalSetelahDiskon = $subtotal - ($subtotal * ($this->diskon / 100));
    $totalSetelahPajak = $totalSetelahDiskon * (1 + ($this->pajak / 100));
    $totalAkhir = $totalSetelahPajak + ($subtotal * ($this->servis / 100));

    return $totalAkhir;
  }


  public function hitungSisaTagihan()
  {
    $pembayaran = $this->hitungTotalPembayaran();
    $tagihan = $this->totalHarga();
    $sisa = $tagihan - $pembayaran;
    return $sisa < 1 ? 0 : $sisa;
  }

  public function totalHargaBersih()
  {
    // Menghitung subtotal dengan mengalikan jumlah * harga untuk setiap transaksi
    $subtotal = $this->transaksi->sum(function ($transaksi) {
      return $transaksi->jumlah * $transaksi->harga;
    });


    return $subtotal;
  }


  public function hitungTotalHargaAkhir(): float
  {
    $subtotal = $this->totalHargaBersih();
    $nilaiPajak = $this->hitungNilaiPajak();
    $nilaiDiskon = $this->hitungNilaiDiskon();
    $nilaiServis = $this->hitungNilaiServis();

    return $subtotal + $nilaiPajak - $nilaiDiskon + $nilaiServis;
  }

  public function hitungTotalPembayaran(): float
  {
    return $this->pembayaran()
      ->sum('jumlah');
  }


  public function hitungSisaPembayaran(): float
  {
    return $this->totalHarga() - $this->hitungTotalPembayaran();
  }

  public function reservasi()
  {
    return $this->belongsTo(\App\Aplikasi\Hotel\Models\Reservasi::class, 'reservasi_id');
  }
}

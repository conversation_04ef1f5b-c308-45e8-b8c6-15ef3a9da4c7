<?php

namespace Modules\Rajapicker\Filament\admin\Resources\MediaResource\Pages;

use Modules\Rajapicker\Filament\admin\Resources\MediaResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMedia extends CreateRecord
{
    protected static string $resource = MediaResource::class;

    public function getTitle(): string
    {
        return 'Tambah Media Baru';
    }
 
    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Media berhasil ditambahkan';
    }

  
}

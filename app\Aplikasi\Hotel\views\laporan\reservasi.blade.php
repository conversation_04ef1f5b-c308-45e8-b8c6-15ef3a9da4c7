<!DOCTYPE html>
<html lang="id">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .info {
            margin-bottom: 15px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th {
            background-color: #f0f0f0;
            text-align: left;
            padding: 8px;
            font-size: 11px;
            border: 1px solid #ddd;
        }
        
        td {
            padding: 6px 8px;
            border: 1px solid #ddd;
            font-size: 10px;
        }
        
        .summary {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .summary-title {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 4px;
            font-size: 11px;
            border: none;
        }
        
        .summary-table .label {
            width: 30%;
            font-weight: bold;
        }
        
        .status-label {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .status-bk {
            background-color: #3b82f6;
            color: white;
        }
        
        .status-sci {
            background-color: #22c55e;
            color: white;
        }
        
        .status-sco {
            background-color: #ef4444;
            color: white;
        }
        
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
        }
        
        .page-number {
            text-align: center;
            font-size: 10px;
            margin-top: 20px;
        }
        
        .lunas {
            background-color: #22c55e;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .zebra-row:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">LAPORAN RESERVASI HOTEL</h1>
        <div class="subtitle">{{ date('d F Y', strtotime(now())) }}</div>
    </div>
    
    <div class="info">
        <div class="info-item"><strong>Periode:</strong> {{ $filters['tanggal_mulai'] }} s/d {{ $filters['tanggal_akhir'] }}</div>
        <div class="info-item"><strong>Status:</strong> {{ $filters['status_reservasi'] }}</div>
        <div class="info-item"><strong>Tamu:</strong> {{ $filters['tamu'] }}</div>
        <div class="info-item"><strong>Petugas:</strong> {{ $filters['petugas'] }}</div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th style="width: 30px;">No</th>
                @foreach($columns as $key => $label)
                    <th>{{ $label }}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @if(count($rows) > 0)
                @foreach($rows as $index => $row)
                    <tr class="zebra-row">
                        <td style="text-align: center;">{{ $index + 1 }}</td>
                        @foreach($columns as $key => $label)
                            @if($key == 'status')
                                <td>
                                    @if(isset($row[$key]))
                                        <span class="status-label {{ $row[$key] == 'BK' ? 'status-bk' : ($row[$key] == 'SCI' ? 'status-sci' : 'status-sco') }}">
                                            {{ $row[$key] }}
                                        </span>
                                    @else
                                        -
                                    @endif
                                </td>
                            @elseif($key == 'sisa_tagihan' && isset($row[$key]) && $row[$key] == 'LUNAS')
                                <td><span class="lunas">LUNAS</span></td>
                            @else
                                <td>{{ $row[$key] ?? '-' }}</td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="{{ count($columns) + 1 }}" style="text-align: center;">Tidak ada data yang ditemukan</td>
                </tr>
            @endif
        </tbody>
    </table>
    
    <div class="summary">
        <div class="summary-title">RINGKASAN</div>
        <table class="summary-table">
            <tr>
                <td class="label">Total Reservasi:</td>
                <td>{{ count($rows) }} Reservasi</td>
            </tr>
            @if(isset($columns['durasi']))
                <tr>
                    <td class="label">Total Durasi:</td>
                    <td>{{ $summary['durasi'] }}</td>
                </tr>
            @endif
            @if(isset($columns['pajak']))
                <tr>
                    <td class="label">Total Pajak:</td>
                    <td>{{ $summary['pajak'] }}</td>
                </tr>
            @endif
            @if(isset($columns['diskon']))
                <tr>
                    <td class="label">Total Diskon:</td>
                    <td>{{ $summary['diskon'] }}</td>
                </tr>
            @endif
            @if(isset($columns['servis']))
                <tr>
                    <td class="label">Total Servis:</td>
                    <td>{{ $summary['servis'] }}</td>
                </tr>
            @endif
            @if(isset($columns['modal']))
                <tr>
                    <td class="label">Total Modal:</td>
                    <td>{{ $summary['modal'] }}</td>
                </tr>
            @endif
            @if(isset($columns['bersih']))
                <tr>
                    <td class="label">Total Harga Bersih:</td>
                    <td>{{ $summary['bersih'] }}</td>
                </tr>
            @endif
            @if(isset($columns['keuntungan']))
                <tr>
                    <td class="label">Total Keuntungan:</td>
                    <td>{{ $summary['keuntungan'] }}</td>
                </tr>
            @endif
            @if(isset($columns['total_akhir']))
                <tr>
                    <td class="label">Total Tagihan:</td>
                    <td>{{ $summary['total_akhir'] }}</td>
                </tr>
            @endif
            @if(isset($columns['pembayaran']))
                <tr>
                    <td class="label">Total Pembayaran:</td>
                    <td>{{ $summary['pembayaran'] }}</td>
                </tr>
            @endif
            @if(isset($columns['sisa_tagihan']))
                <tr>
                    <td class="label">Total Sisa Tagihan:</td>
                    <td>{{ $summary['sisa_tagihan'] }}</td>
                </tr>
            @endif
        </table>
    </div>
    
    <div class="footer">
        <p>Dicetak oleh: {{ auth()->user()->name }} pada {{ date('d/m/Y H:i') }}</p>
    </div>
    
    <div class="page-number">
        Halaman 1
    </div>
</body>
</html>
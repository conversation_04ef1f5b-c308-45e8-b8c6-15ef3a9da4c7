<?php

namespace Mo<PERSON>les\Rajapicker\Filament\rajamember\Resources\MediaResource\Pages; 

use Mo<PERSON>les\Rajapicker\Filament\rajamember\Resources\MediaResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMedia extends CreateRecord
{
    protected static string $resource = MediaResource::class;

    public function getTitle(): string
    {
        return 'Tambah Media Baru';
    }
 
    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Media berhasil ditambahkan';
    }

  
}

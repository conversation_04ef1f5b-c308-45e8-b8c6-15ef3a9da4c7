# Modul RajaGambar

Modul RajaGambar adalah gateway untuk pemrosesan gambar yang menggunakan package Spatie Image sebagai engine utama. Modul ini menyediakan API endpoints dan service class yang mudah digunakan untuk berbagai kebutuhan pemrosesan gambar dalam aplikasi hotel.

## Fitur Utama

### 1. Dimensi & Resize
- **width()** - Mengubah lebar dengan mempertahankan aspect ratio
- **height()** - Mengubah tinggi dengan mempertahankan aspect ratio
- **resize()** - Mengubah width dan height bersamaan
- **fit()** - Menyesuaikan gambar dalam dimensi tertentu dengan fitMethod
- **crop()** - Memotong gambar dengan cropMethod tertentu
- **focalCrop()** - Crop berdasarkan titik fokus dengan zoom 1-100
- **manualCrop()** - Crop area spesifik dengan koordinat

### 2. Optimasi
- **optimize()** - Mengurangi ukuran file gambar

### 3. Penyesuaian Warna & Cahaya
- **brightness()** - Nilai -100 sampai 100
- **contrast()** - Nilai -100 sampai 100
- **gamma()** - Nilai 0.1 sampai 9.99
- **colorize()** - Nilai RGB masing-masing -100 sampai 100

### 4. Canvas & Border
- **background()** - Set background untuk gambar transparan
- **border()** - Tambah border dengan width, borderType, dan color

### 5. Orientasi
- **orientation()** - Rotasi gambar (rotate0/90/180/270)
- **flip()** - Mirror horizontal/vertical/both

### 6. Efek Tambahan
- **watermark()** - Tambah watermark
- **text()** - Tambah teks pada gambar

## Instalasi

Package sudah ter-install otomatis dengan composer. Pastikan modul RajaGambar sudah aktif:

```bash
php artisan module:enable RajaGambar
```

## Konfigurasi

Publish konfigurasi jika diperlukan:

```bash
php artisan vendor:publish --tag=rajagambar-config
```

File konfigurasi akan tersedia di `config/rajagambar.php`.

## Penggunaan Service

### Menggunakan Service Langsung

```php
use Modules\RajaGambar\Services\RajaGambarService;

// Melalui dependency injection
public function __construct(RajaGambarService $rajaGambar)
{
    $this->rajaGambar = $rajaGambar;
}

// Atau melalui app container
$rajaGambar = app(RajaGambarService::class);

// Contoh penggunaan
$result = $rajaGambar
    ->load('/path/to/image.jpg')
    ->resize(800, 600)
    ->brightness(10)
    ->optimize()
    ->save();
```

### Chain Methods

```php
$result = $rajaGambar
    ->load('/path/to/image.jpg')
    ->width(800)
    ->brightness(20)
    ->contrast(10)
    ->border(2, 'overlay', '#ff0000')
    ->optimize()
    ->save('/path/to/output.jpg');
```

## API Endpoints

Base URL: `https://hotel.rid/api/rajagambar`

### Basic Operations

#### Resize
```
POST /resize
Content-Type: multipart/form-data

Parameters:
- image: file (required, max 10MB)
- width: integer (required, 1-4000)
- height: integer (required, 1-4000)
```

#### Width
```
POST /width
Content-Type: multipart/form-data

Parameters:
- image: file (required, max 10MB)
- width: integer (required, 1-4000)
```

#### Height
```
POST /height
Content-Type: multipart/form-data

Parameters:
- image: file (required, max 10MB)
- height: integer (required, 1-4000)
```

#### Fit
```
POST /fit
Content-Type: multipart/form-data

Parameters:
- image: file (required, max 10MB)
- width: integer (required, 1-4000)
- height: integer (required, 1-4000)
- fit_method: string (optional, contain|max|fill|stretch|crop)
```

#### Crop
```
POST /crop
Content-Type: multipart/form-data

Parameters:
- image: file (required, max 10MB)
- width: integer (required, 1-4000)
- height: integer (required, 1-4000)
- crop_method: string (optional, crop-center|crop-top|crop-bottom|etc)
```

## Response Format

Semua API endpoint mengembalikan response dalam format JSON:

### Success Response
```json
{
    "success": true,
    "message": "Gambar berhasil diproses",
    "data": {
        "url": "https://hotel.rid/storage/rajagambar/processed_image.jpg",
        "width": 800,
        "height": 600
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Validasi gagal",
    "errors": {
        "image": ["The image field is required."],
        "width": ["The width must be at least 1."]
    }
}
```

## RajaUpload - Custom FilamentPHP Component

RajaGambar menyediakan custom field FilamentPHP bernama `RajaUpload` yang terintegrasi langsung dengan RajaGambarService untuk pemrosesan gambar otomatis.

### Fitur RajaUpload

- **Upload gambar** dengan drag & drop atau click
- **Validasi format** otomatis (JPG, JPEG, PNG, GIF, WEBP, BMP)
- **Pemrosesan otomatis** menggunakan RajaGambarService
- **Preview gambar** hasil processing
- **Progress upload** dengan status real-time
- **Multiple upload** dengan batch processing
- **Konfigurasi fleksibel** untuk resize, optimize, watermark, dan efek

### Penggunaan Dasar

```php
use Modules\RajaGambar\Forms\Components\RajaUpload;

// Basic usage
RajaUpload::make('featured_image')
    ->label('Gambar Utama')
    ->required()

// Multiple upload
RajaUpload::make('gallery_images')
    ->label('Galeri Gambar')
    ->multiple()
    ->maxFiles(5)
```

### Konfigurasi Auto Processing

```php
// Auto resize dengan optimize
RajaUpload::make('product_image')
    ->autoResize(800, 600)
    ->autoOptimize(true)
    ->outputDirectory('products')

// Dengan watermark
RajaUpload::make('banner_image')
    ->autoResize(1200, 400, 'crop')
    ->addWatermark('/path/to/watermark.png', 'bottom-right', 50)
    ->autoOptimize(true)

// Dengan efek kustom
RajaUpload::make('profile_photo')
    ->autoResize(300, 300, 'crop')
    ->applyEffects([
        'brightness' => 10,
        'contrast' => 5,
        'border' => [
            'width' => 2,
            'type' => 'overlay',
            'color' => '#ffffff'
        ]
    ])
    ->autoOptimize(true)
```

### Method Konfigurasi

#### `autoResize(width, height, fitMethod?)`
Mengatur resize otomatis setelah upload.

```php
RajaUpload::make('image')
    ->autoResize(800, 600)           // Resize ke 800x600
    ->autoResize(800, 600, 'crop')   // Resize dengan crop
    ->autoResize(800, 600, 'fit')    // Resize dengan fit
```

#### `autoOptimize(bool)`
Mengaktifkan optimasi otomatis untuk mengurangi ukuran file.

```php
RajaUpload::make('image')
    ->autoOptimize(true)   // Aktifkan optimasi
    ->autoOptimize(false)  // Nonaktifkan optimasi
```

#### `addWatermark(path, position, opacity, padding?)`
Menambahkan watermark otomatis.

```php
RajaUpload::make('image')
    ->addWatermark('/path/to/watermark.png', 'bottom-right', 50)
    ->addWatermark('/path/to/logo.png', 'top-left', 70, 20)
```

**Posisi watermark:**
- `top-left`, `top-center`, `top-right`
- `center-left`, `center`, `center-right`
- `bottom-left`, `bottom-center`, `bottom-right`

#### `applyEffects(array)`
Menerapkan efek kustom pada gambar.

```php
RajaUpload::make('image')
    ->applyEffects([
        'brightness' => 20,        // -100 to 100
        'contrast' => 10,          // -100 to 100
        'gamma' => 1.2,           // 0.1 to 9.99
        'colorize' => [
            'red' => 10,
            'green' => -5,
            'blue' => 15
        ],
        'background' => '#ffffff',
        'border' => [
            'width' => 3,
            'type' => 'overlay',
            'color' => '#000000'
        ],
        'orientation' => 90,       // 90, 180, 270
        'flip' => 'horizontal'     // horizontal, vertical, both
    ])
```

#### `outputDirectory(string)`
Menentukan direktori penyimpanan hasil processing.

```php
RajaUpload::make('image')
    ->outputDirectory('processed-images')
    ->outputDirectory('uploads/products')
```

### Contoh Lengkap dalam Form

```php
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Modules\RajaGambar\Forms\Components\RajaUpload;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Section::make('Informasi Produk')
                ->schema([
                    TextInput::make('name')
                        ->label('Nama Produk')
                        ->required(),

                    RajaUpload::make('featured_image')
                        ->label('Gambar Utama')
                        ->autoResize(800, 600, 'crop')
                        ->addWatermark(public_path('images/watermark.png'), 'bottom-right', 40)
                        ->autoOptimize(true)
                        ->outputDirectory('products/featured')
                        ->required(),

                    RajaUpload::make('gallery_images')
                        ->label('Galeri Produk')
                        ->multiple()
                        ->maxFiles(8)
                        ->autoResize(600, 400, 'fit')
                        ->applyEffects([
                            'brightness' => 5,
                            'contrast' => 3
                        ])
                        ->autoOptimize(true)
                        ->outputDirectory('products/gallery'),
                ])
        ]);
}
```

## Troubleshooting

### Error: "Class 'Spatie\Image\Image' not found"
Pastikan package spatie/image sudah terinstall:
```bash
composer require spatie/image
```

### Error: "Service not found"
Pastikan service provider sudah terdaftar dan cache sudah di-clear:
```bash
php artisan config:clear
php artisan cache:clear
```

### Error: "Route not found"
Pastikan routes sudah di-load:
```bash
php artisan route:clear
php artisan route:cache
```

### Error: "No hint path defined for [rajagambar]"
**Penyebab:** View namespace untuk module RajaGambar belum terdaftar dengan benar.

**Solusi:**
```bash
# 1. Pastikan module aktif
php artisan module:list
php artisan module:enable RajaGambar

# 2. Clear cache
php artisan view:clear
php artisan config:clear
php artisan route:clear
```

### Error: "RajaUpload component not found"
Pastikan module sudah di-load dan cache di-clear:
```bash
php artisan config:clear
php artisan view:clear
```

### Error: "Cannot read properties of null (reading 'join')" di JavaScript
**Penyebab:** `acceptedFileTypes` bernilai `null` di Alpine.js component.

**Solusi:** Sudah diperbaiki dengan:
1. Menambahkan null check di view template
2. Override method `getAcceptedFileTypes()` untuk memastikan selalu mengembalikan array
3. Menambahkan fallback values di JavaScript component

**Detail Perbaikan:**
- View template: `acceptedFileTypes && acceptedFileTypes.length > 0 ? acceptedFileTypes.join(',') : 'image/*'`
- Component: `acceptedFileTypes: config.acceptedFileTypes || []`
- Method override: `getAcceptedFileTypes()` selalu mengembalikan array valid

### Error: "POST /api/rajagambar/upload-process 422 (Unprocessable Content)"
**Penyebab:** Validasi gagal pada endpoint upload.

**Debugging:**
1. Buka browser console untuk melihat detail error
2. Periksa Laravel logs:
   ```bash
   tail -f storage/logs/laravel.log
   ```
3. Pastikan file yang diupload memenuhi kriteria:
   - Format: JPG, PNG, GIF, WEBP, BMP
   - Ukuran maksimal: 10MB
   - File tidak corrupt

**Solusi umum:**
- Pastikan file adalah gambar valid
- Periksa ukuran file tidak melebihi batas
- Pastikan storage directory dapat ditulis
- Clear cache jika diperlukan

### Module tidak aktif
```bash
# Cek status semua module
php artisan module:list

# Aktifkan module tertentu
php artisan module:enable RajaGambar

# Aktifkan semua module
php artisan module:enable
```

## Lisensi

MIT License

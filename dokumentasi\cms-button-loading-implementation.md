# Implementasi Indikator Loading pada Tombol Save CMS

## Deskripsi
Implementasi ini menambahkan indikator loading pada tombol save changes dan tombol save di atas form untuk halaman edit dan create CMS, serta mengatur pagination list menjadi 20 item per halaman.

## Perubahan yang Dilakukan

### 1. File yang Dimodifikasi

#### `Modules/RajaCms/app/Filament/Resources/CmsResource/Pages/EditCms.php`
- Menambahkan import `Filament\Actions`
- Menambahkan method `getHeaderActions()` untuk tombol save di atas form
- Menambahkan method `getFormActions()` untuk tombol save di bawah form
- Menambahkan method `getSaveFormAction()` dengan kustomisasi indikator loading

#### `Modules/RajaCms/app/Filament/Resources/CmsResource/Pages/CreateCmsHalaman.php`
- Menambahkan import `Filament\Actions`
- Menambahkan method `getHeaderActions()` untuk tombol save di atas form

#### `Modules/RajaCms/app/Filament/Resources/CmsResource/Pages/ListHalaman.php`
- Menambahkan pengaturan pagination: `->paginated([10, 20, 50, 100])`
- Mengatur default pagination: `->defaultPaginationPageOption(20)`

#### `resources/css/app.css`
- Menambahkan import CSS untuk button loading

### 2. Fitur yang Ditambahkan

#### Indikator Loading
- Tombol akan menampilkan teks "Tunggu..." saat dalam keadaan loading
- Icon akan berputar (spin animation) saat loading
- Tombol akan di-disable dan opacity berkurang saat loading
- Menggunakan class CSS `filament-button-tunggu`

#### Tombol Save di Header
- Tombol "Simpan Perubahan" (untuk edit) dan "Simpan Konten" (untuk create) di atas form
- Posisi di header halaman untuk akses cepat
- Menggunakan warna success (hijau) dengan icon check-circle

#### Tombol Save di Form Actions
- Tombol save di bagian bawah form dengan indikator loading
- Konsisten dengan tombol di header

#### Pagination List
- Default 20 item per halaman
- Opsi pagination: 10, 20, 50, 100 item per halaman
- Pengguna dapat memilih jumlah item yang ditampilkan

### 3. CSS Styling

File `resources/css/filament/admin/button-loading.css` berisi:
- Animasi spin untuk icon
- Teks "Tunggu..." yang muncul saat loading
- Opacity dan cursor styling untuk state disabled

### 4. Cara Kerja

1. **Saat tombol diklik**: 
   - Tombol akan di-disable
   - Teks "Tunggu..." muncul
   - Icon berputar
   - Opacity berkurang

2. **Saat proses selesai**:
   - Tombol kembali normal
   - Teks dan icon kembali ke state awal

3. **Pagination**:
   - List menampilkan 20 item secara default
   - Dropdown untuk memilih jumlah item per halaman
   - Navigasi halaman otomatis

### 5. Kompatibilitas

- Bekerja dengan semua jenis form di CMS (Artikel, Halaman, Acara, dll)
- Mendukung dark mode dan light mode
- Responsive design
- Menggunakan Filament Actions API
- Pagination bekerja dengan semua filter dan tab

## Penggunaan

### Untuk Halaman Edit
- Tombol "Simpan Perubahan" tersedia di header dan footer form
- Kedua tombol memiliki indikator loading yang sama

### Untuk Halaman Create
- Tombol "Simpan Konten" tersedia di header dan footer form
- Kedua tombol memiliki indikator loading yang sama

### Untuk List Halaman
- Default menampilkan 20 item per halaman
- Dapat diubah menjadi 10, 50, atau 100 item per halaman
- Pagination otomatis berdasarkan jumlah item

## Testing

1. Buka halaman edit atau create CMS
2. Klik tombol save di header atau footer
3. Pastikan indikator loading muncul
4. Pastikan tombol kembali normal setelah proses selesai
5. Buka halaman list CMS
6. Pastikan menampilkan 20 item per halaman secara default
7. Test opsi pagination lainnya

## Catatan Teknis

- Menggunakan Wire Loading Attributes dari Livewire
- CSS animation untuk efek visual yang smooth
- Mengikuti pola design Filament Admin Panel
- Mendukung semua browser modern
- Pagination menggunakan Filament Table API 
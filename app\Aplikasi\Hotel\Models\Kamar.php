<?php

namespace App\Aplikasi\Hotel\Models;


use App\Models\Produk;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperKamar
 */
class Kamar extends Produk
{
  

    protected $casts = [
        'check_in' => 'datetime:Y-m-d H:i',
        'check_out' => 'datetime:Y-m-d H:i',


        'fasilitas' => 'array',
        'spesifikasi' => 'array',
    ];

    protected $appends = ['is_available'];

    public function getIsAvailableAttribute()
    {

        return true; // Sesuaikan dengan logic Anda
    }
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'KAMAR');
        });

        static::creating(function ($model) {
            $model->jenis = 'KAMAR';
        });
        
        static::updating(function ($model) {
            $model->jenis = 'KAMAR';
        });
    }

    public function create(): void
    {
        $data = $this->form->getState();

        // Format dates before saving
        $data['check_in'] = Carbon::parse($data['check_in'])->format('Y-m-d H:i');
        $data['check_out'] = Carbon::parse($data['check_out'])->format('Y-m-d H:i');

        $reservasi = Reservasi::create($data);

        // ... rest of your creation logic
    }

    public function tipe()
    {
        return $this->belongsTo(KamarTipe::class, 'kategori_id');
    }

    public function reservasi()
    {
        return $this->hasMany(Reservasi::class, 'kamar_id');
    }

   public function reservasis()
    {
        return $this->hasMany(Reservasi::class, 'kamar_id');
    }

    public function isAvailable($checkIn, $checkOut)
    {
        if (! $checkIn instanceof Carbon) {
            $checkIn = Carbon::parse($checkIn);
        }

        if (! $checkOut instanceof Carbon) {
            $checkOut = Carbon::parse($checkOut);
        }

        return ! $this->reservasi()
            ->where(function ($query) use ($checkIn, $checkOut) {
                // Cek apakah ada reservasi yang bentrok
                // Kondisi: (checkout >= check_in_baru) && (checkin <= check_out_baru)
                $query->where('check_out', '>=', $checkIn->format('Y-m-d H:i:s'))
                    ->where('check_in', '<=', $checkOut->format('Y-m-d H:i:s'));
            })
            ->whereNotIn('status_reservasi', ['SCO']) // Exclude kamar dengan status check-out
            ->exists();
    }


    public function hitungKeuntungan(): float
    {
        return $this->harga - $this->harga_modal;
    }


    public function hitungPersentaseKeuntungan(): float
    {
        if ($this->harga_modal <= 0) {
            return 0;
        }

        return ($this->hitungKeuntungan() / $this->harga_modal) * 100;
    }


    public function formatHarga(): string
    {
        if ($this->punyaVarian()) {
            $minHarga = number_format($this->price_range['min'], 0, ',', '.');
            $maxHarga = number_format($this->price_range['max'], 0, ',', '.');

            if ($this->price_range['min'] === $this->price_range['max']) {
                return "Rp {$minHarga}";
            }

            return "Rp {$minHarga} - Rp {$maxHarga}";
        }

        return "Rp ".number_format($this->harga, 0, ',', '.');
    }

}

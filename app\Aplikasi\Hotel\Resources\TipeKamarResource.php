<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Resources\TipeKamarResource\Pages;
use App\Filament\Resources\KategoriResource;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Forms\Form;

use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;


class TipeKamarResource extends KategoriResource
{
    protected static ?string $model = KamarTipe::class;
    protected static ?string $navigationGroup = 'Hotel';
    protected static ?string $navigationLabel = 'Tipe Kamar';
    protected static ?string $pluralModelLabel = 'Tipe Kamar';
    protected static ?string $modelLabel = 'Tipe Kamar';
    protected static ?string $slug = 'pengaturan/tipekamar';
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static bool $shouldRegisterNavigation = false; 

    public static function form(Form $form): Form
    {
        return KategoriResource::form($form);
    }

    public static function table(Table $table): Table
    {
        return KategoriResource::table($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTipeKamar::route('/'),
        ];
    }
}

<?php

namespace App\Aplikasi\Hotel\Models\Seeders;

use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;

class KamarSeeder extends Seeder
{
    /**
     * <PERSON><PERSON>lank<PERSON> proses seeding database untuk Kamar.
     * 
     * @return void
     */
    public function run(): void
    {
        // Mendapatkan jumlah data dari environment variable atau default ke 20
        $jumlah = env('SEEDER_COUNT', 20);
        
        // Inisialisasi Faker dengan lokalisasi Indonesia
        $faker = Faker::create('id_ID');
        
        echo "Membuat data Tipe Kamar terlebih dahulu...\n";
        
        // Buat beberapa tipe kamar jika belum ada
        $tipeKamar = KamarTipe::all();
        if ($tipeKamar->isEmpty()) {
            // Daftar tipe kamar yang akan dibuat
            $daftarTipe = [
                [
                    'nama' => 'Standard Room',
                    'harga_modal' => 250000,
                    'ket' => 'Kamar standar dengan fasilitas dasar',
                    'sub' => 'HOTEL'
                ],
                [
                    'nama' => 'Deluxe Room',
                    'harga_modal' => 400000,
                    'ket' => 'Kamar lebih luas dengan fasilitas lengkap',
                    'sub' => 'HOTEL'
                ],
                [
                    'nama' => 'Superior Room',
                    'harga_modal' => 350000,
                    'ket' => 'Kamar dengan kualitas dan layanan lebih baik',
                    'sub' => 'HOTEL'
                ],
                [
                    'nama' => 'Executive Room',
                    'harga_modal' => 600000,
                    'ket' => 'Kamar premium dengan fasilitas mewah',
                    'sub' => 'HOTEL'
                ],
                [
                    'nama' => 'Suite Room',
                    'harga_modal' => 900000,
                    'ket' => 'Kamar suite dengan ruang tamu terpisah',
                    'sub' => 'HOTEL'
                ],
                [
                    'nama' => 'Family Room',
                    'harga_modal' => 750000,
                    'ket' => 'Kamar luas untuk keluarga',
                    'sub' => 'HOTEL'
                ],
            ];
            
            // Buat semua tipe kamar
            foreach ($daftarTipe as $tipe) {
                // Menggunakan instantiasi objek dan save untuk menghindari error static method
                $tipeBaru = new KamarTipe();
                $tipeBaru->nama = $tipe['nama'];
                $tipeBaru->jenis = 'KAMAR'; // Wajib karena ada global scope
                $tipeBaru->sub = $tipe['sub'];
                $tipeBaru->harga_modal = $tipe['harga_modal'];
                $tipeBaru->ket = $tipe['ket'];
                $tipeBaru->save();
            }
            
            echo "Berhasil membuat " . count($daftarTipe) . " tipe kamar.\n";
            $tipeKamar = KamarTipe::all();
        }
        
        echo "Membuat {$jumlah} data Kamar...\n";
        
        // Daftar fasilitas yang mungkin ada di kamar
        $daftarFasilitas = [
            'TV LED',
            'AC',
            'WiFi',
            'Kamar Mandi Dalam',
            'Bathtub',
            'Shower',
            'Pemanas Air',
            'Minibar',
            'Telepon',
            'Meja Kerja',
            'Brankas',
            'Coffee Maker',
            'Air Mineral Gratis',
            'View Kota',
            'View Pegunungan',
            'View Laut',
            'Balkon',
            'Akses Kolam Renang',
            'Sarapan Gratis',
            'Layanan Kamar 24 Jam'
        ];
        
        // Daftar spesifikasi kamar yang mungkin
        $daftarSpesifikasi = [
            'Ukuran Tempat Tidur' => ['Single', 'Double', 'Twin', 'Queen', 'King'],
            'Luas Kamar' => ['20m²', '24m²', '28m²', '32m²', '40m²', '50m²', '65m²'],
            'Kapasitas' => ['1 Orang', '2 Orang', '3 Orang', '4 Orang'],
            'Jenis Tempat Tidur' => ['1 Single Bed', '2 Single Bed', '1 Double Bed', '1 Queen Bed', '1 King Bed'],
            'Lantai' => ['1', '2', '3', '4', '5']
        ];
        
        // Membuat kamar berdasarkan lantai dan nomor
        $nomorKamarTersedia = [];
        for ($lantai = 1; $lantai <= 5; $lantai++) {
            for ($nomor = 1; $nomor <= 20; $nomor++) {
                $nomorKamarTersedia[] = sprintf("%d%02d", $lantai, $nomor);
            }
        }
        
        // Acak array nomor kamar
        shuffle($nomorKamarTersedia);
        
        // Buat kamar sejumlah yang diminta
        for ($i = 0; $i < min($jumlah, count($nomorKamarTersedia)); $i++) {
            // Pilih tipe kamar secara acak
            $tipe = $tipeKamar->random();
            
            // Tentukan nomor kamar
            $nomorKamar = $nomorKamarTersedia[$i];
            
            // Tentukan fasilitas secara acak
            $fasilitasKamar = $faker->randomElements(
                $daftarFasilitas, 
                $faker->numberBetween(5, count($daftarFasilitas))
            );
            
            // Tentukan spesifikasi
            $spesifikasiKamar = [];
            foreach ($daftarSpesifikasi as $key => $values) {
                $spesifikasiKamar[] = $key . ': ' . $faker->randomElement($values);
            }
            
            // Hitung harga berdasarkan tipe dan fasilitas
            $baseHarga = $tipe->harga_modal * 1.5; // Markup 50%
            $hargaTambahan = count($fasilitasKamar) * 25000; // Setiap fasilitas menambah 25rb
            $hargaTotal = $baseHarga + $hargaTambahan;
            
            // Buat data kamar - menggunakan metode yang benar untuk Model Eloquent
            $kamar = new Kamar();
            $kamar->jenis = 'KAMAR'; // Wajib karena ada global scope
            $kamar->toko_id = 1; // Asumsi hotel ID = 1
            $kamar->kategori_id = $tipe->id;
            $kamar->barcode = 'KMR-' . $nomorKamar;
            $kamar->nama = $tipe->nama . ' - ' . $nomorKamar;
            $kamar->harga = $hargaTotal;
            $kamar->harga_modal = $tipe->harga_modal;
            $kamar->stok = 1; // Kamar selalu stok 1
            $kamar->gambar = null; // Gambar bisa ditambahkan manual
            $kamar->ket = 'Kamar nomor ' . $nomorKamar . ' tipe ' . $tipe->nama;
            $kamar->fasilitas = $fasilitasKamar;
            $kamar->spesifikasi = $spesifikasiKamar;
            $kamar->tampil = 1; // Tampilkan di daftar
            $kamar->save();
        }
        
        echo "Selesai membuat " . min($jumlah, count($nomorKamarTersedia)) . " data Kamar.\n";
    }
}
<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\Konfig as KonfigUtama;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Str;

/**
 * @mixin IdeHelperKonfig
 */
class Konfig extends KonfigUtama
{
 

    public $timestamps = true;

 

    public static function pakaiShift()
    {
        $nilai = self::isi('shift');
        // Konversi nilai string ke boolean
        return filter_var($nilai, FILTER_VALIDATE_BOOLEAN);
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'KASIR');
        });

        static::creating(function ($model) {
            $model->jenis = 'KASIR';
        });

        static::updating(function ($model) {
            $model->jenis = 'KASIR';
        });
    }


    public static function jenisKasir()
    {
        $jenisKasir = self::isi('tampilan_kasir');

        // Pastikan nilai yang dikembalikan adalah string
        if (is_array($jenisKasir)) {
            return isset($jenisKasir['value']) ? (string) $jenisKasir['value'] : 'minimarket';
        }

        return (string) $jenisKasir;
    }
    /** 
         $kon = Konfig::jsonKuRaw('tampilan_resto') ?? [];
        $this->jumlahPerHalaman = $kon['jumlah_produk_perhalaman'] ?? 15;
        $this->jumlahPerbaris = $kon['jumlah_produk_perbaris'] ?? 4;

         $kon = Konfig::jsonKu('tampilan_resto');
        $perHalaman = $kon->firstWhere('key', 'jumlah_produk_perhalaman');
        $perBaris = $kon->firstWhere('key', 'jumlah_produk_perbaris');
        **/
}
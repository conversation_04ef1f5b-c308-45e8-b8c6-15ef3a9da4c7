<?php

namespace App\Aplikasi\Hotel\Pages;

use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Grid;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;

class Konfig extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-8-tooth';
    protected static string $view = 'hotel::pages.konfig';
    protected static ?string $title = 'Konfigurasi Hotel';
    protected static ?string $navigationLabel = 'Konfigurasi';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $slug = 'pengaturan/konfig';
    protected static ?int $navigationSort = 100;
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];
    protected $configPath;
  public function getHeaderWidgets(): array
    {
        return [
            // \App\Aplikasi\Hotel\Widgets\PenyairNavWidget::make('pengaturan'),
        ];
    }

    public function getHeaderWidgetsColumns(): int|array
    {
        return 1;
    }
    
    public function mount(): void
    {
        $this->configPath = config_path('hotel.php');
        
        // Coba ambil konfigurasi dari cache atau file
        $config = config('hotel', []);
        
        // Jika konfigurasi kosong, coba ambil dari file aplikasi.php langsung
        if (empty($config)) {
            $appConfigPath = app_path('Aplikasi/Hotel/config/aplikasi.php');
            if (File::exists($appConfigPath)) {
                $config = include $appConfigPath;
            }
        }
        
        // Jika masih kosong, gunakan nilai default dari file yang baru dibuat
        if (empty($config)) {
            $defaultConfigPath = __DIR__ . '/../config/aplikasi.php';
            if (File::exists($defaultConfigPath)) {
                $config = include $defaultConfigPath;
            }
        }
        
        $this->data = $config;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Konfigurasi Umum')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TagsInput::make('jenis_identitas')
                                    ->label('Jenis Identitas')
                                    ->placeholder('Tambahkan jenis identitas baru')
                                    ->helperText('Daftar jenis identitas yang dapat digunakan tamu'),

                                TagsInput::make('referensi')
                                    ->label('Referensi')
                                    ->placeholder('Tambahkan referensi baru')
                                    ->helperText('Daftar sumber referensi reservasi'),
                            ]),
                    ]),

                Section::make('Fasilitas & Spesifikasi')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TagsInput::make('fasilitas_kamar')
                                    ->label('Fasilitas Kamar')
                                    ->placeholder('Tambahkan fasilitas baru')
                                    ->helperText('Daftar fasilitas yang tersedia di kamar'),

                                TagsInput::make('spesifikasi_kamar')
                                    ->label('Spesifikasi Kamar')
                                    ->placeholder('Tambahkan spesifikasi baru')
                                    ->helperText('Daftar spesifikasi kamar yang tersedia'),
                            ]),
                    ]),

                Section::make('Status & Jadwal')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                KeyValue::make('reservasi_status')
                                    ->label('Status Reservasi')
                                    ->addButtonLabel('Tambah Status')
                                    ->keyLabel('Kode')
                                    ->valueLabel('Status')
                                    ->helperText('Kode dan deskripsi status reservasi'),

                                TagsInput::make('jadwal_shift')
                                    ->label('Jadwal Shift')
                                    ->placeholder('Tambahkan shift baru')
                                    ->helperText('Daftar jadwal shift kerja'),

                                TagsInput::make('pembayaran_status')
                                    ->label('Status Pembayaran')
                                    ->placeholder('Tambahkan status baru')
                                    ->helperText('Daftar status pembayaran'),
                            ]),
                    ]),

                Section::make('Biaya & Printer')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                KeyValue::make('biaya_layanan')
                                    ->label('Biaya Layanan (%)')
                                    ->addButtonLabel('Tambah Biaya')
                                    ->keyLabel('Jenis')
                                    ->valueLabel('Persentase')
                                    ->helperText('Pengaturan biaya layanan dalam persen'),

                                KeyValue::make('printer_kasir')
                                    ->label('Konfigurasi Printer Kasir')
                                    ->addButtonLabel('Tambah Konfigurasi')
                                    ->keyLabel('Kunci')
                                    ->valueLabel('Nilai')
                                    ->helperText('Pengaturan printer untuk struk'),
                            ]),
                    ]),
                    
                Section::make('Restoran & Pesanan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                KeyValue::make('meja')
                                    ->label('Daftar Meja')
                                    ->addButtonLabel('Tambah Meja Baru')
                                    ->keyLabel('Nama Meja')
                                    ->valueLabel('Lokasi')
                                    ->keyPlaceholder('Contoh: meja1')
                                    ->valuePlaceholder('dalam atau luar')
                                    ->helperText('Nama meja sebagai kunci dan lokasi (dalam/luar) sebagai nilai')
                                    ->columnSpan(1),

                                TagsInput::make('jenis_pesanan')
                                    ->label('Jenis Pesanan')
                                    ->placeholder('Tambahkan jenis pesanan baru')
                                    ->helperText('Daftar jenis pesanan yang tersedia (takeaway, makan di tempat, delivery)')
                                    ->columnSpan(1),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('simpan')
                ->label('Simpan Konfigurasi')
                ->icon('heroicon-o-document-check')
                ->submit('simpan'),
        ];
    }

    public function simpan(): void
    {
        $this->validate();

        try {
            $config = var_export($this->data, true);
            $configContent = "<?php\n\nreturn {$config};\n";
            
            // Pastikan path config utama valid
            if (empty($this->configPath)) {
                $this->configPath = config_path('hotel.php');
            }
            
            // Buat direktori jika belum ada (untuk file aplikasi.php)
            $appConfigDir = app_path('Aplikasi/Hotel/config');
            if (!File::exists($appConfigDir)) {
                File::makeDirectory($appConfigDir, 0755, true);
            }
            
            // Perbarui file konfigurasi aplikasi.php
            $configFilePath = app_path('Aplikasi/Hotel/config/aplikasi.php');
            File::put($configFilePath, $configContent);
            
            // Pastikan direktori config utama ada
            $configDir = dirname($this->configPath);
            if (!File::exists($configDir)) {
                File::makeDirectory($configDir, 0755, true);
            }
            
            // Perbarui file konfigurasi utama hotel.php
            File::put($this->configPath, $configContent);
            
            Notification::make()
                ->title('Konfigurasi berhasil disimpan')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menyimpan konfigurasi')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperKategori
 */
class Kategori extends Model
{
    use HasFactory;

 
    public function getTable()
    {
        return config('tabel.t_kategori.nama_tabel', 'kategori');
    }

  
    public function getFillable()
    {
        // Ambil kolom dari konfigurasi dengan nilai default array kosong
        $kolom = config('tabel.t_kategori.kolom', []);

        // Nilai default jika konfigurasi tidak tersedia atau tidak valid
        $defaultFillable = ['jenis', 'slug', 'nama', 'sub', 'gambar', 'ket', 'created_at', 'updated_at'];

        // Pastikan $kolom adalah array
        if (!is_array($kolom) || empty($kolom)) {
            return $defaultFillable;
        }

        // Hapus kolom 'id' dari fillable karena biasanya ID tidak perlu fillable
        return array_values(array_filter($kolom, function ($item) {
            return $item !== 'id';
        }));
    }

    
}

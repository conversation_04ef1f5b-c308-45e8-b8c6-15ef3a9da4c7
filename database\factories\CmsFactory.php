<?php

namespace Database\Factories;

use Modules\RajaCms\Models\Cms;
use App\Models\Karyawan;
use App\Models\KategoriArtikel;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\RajaCms\Models\Cms>
 */
class CmsFactory extends Factory
{
    /**
     * Nama model yang sesuai dengan factory.
     *
     * @var string
     */
    protected $model = Cms::class;

    /**
     * Mendefinisikan state default model.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Gunakan faker dengan locale Indonesia
        $this->faker = \Faker\Factory::create('id_ID');

        // Pilih jenis konten secara acak
        $jenis = $this->faker->randomElement(['HALAMAN', 'ARTIKEL',  'ACARA']);

        // Buat judul dalam Bahasa Indonesia
        $judulOptions = [
            'HALAMAN' => [
                'Tentang <PERSON>', 'Layanan Hotel', 'Fasilitas Kami', 'Kontak Kami',
                'Karir di Hotel', 'Kebijak<PERSON> Privasi', 'Syarat dan Keten<PERSON>an',
                'Lokasi Hotel', 'Panduan Wisata', 'Sejarah Hotel'
            ],
            'ARTIKEL' => [
                'Tips Liburan Nyaman di Hotel', 'Wisata Kuliner di Sekitar Hotel',
                'Kegiatan Menarik Selama Menginap', 'Panduan Wisata Lokal',
                'Rekomendasi Tempat Belanja', 'Tradisi Lokal yang Menarik',
                'Makanan Khas yang Wajib Dicoba', 'Festival Budaya Tahunan'
            ],
          
            'ACARA' => [
                'Workshop Kuliner Nusantara', 'Seminar Pariwisata Berkelanjutan',
                'Pameran Seni Lokal', 'Konser Musik di Hotel',
                'Perayaan Hari Kemerdekaan', 'Festival Kuliner',
                'Pertemuan Bisnis Tahunan', 'Acara Amal untuk Pendidikan'
            ]
        ];

        // Pilih judul acak sesuai jenis atau buat judul acak jika tidak ada opsi
        $judul = isset($judulOptions[$jenis]) ?
            $this->faker->randomElement($judulOptions[$jenis]) :
            'Artikel ' . $this->faker->words(3, true);

        // Tambahkan variasi ke judul agar tidak selalu sama
        if ($this->faker->boolean(70)) {
            // Gunakan kata-kata Indonesia
            $tambahan = [
                'di ' . $this->faker->city,
                'untuk ' . $this->faker->randomElement(['Keluarga', 'Liburan', 'Bisnis', 'Relaksasi']),
                'dengan ' . $this->faker->randomElement(['Harga Terjangkau', 'Pelayanan Terbaik', 'Fasilitas Lengkap']),
                'pada ' . $this->faker->monthName . ' ' . $this->faker->year
            ];
            $judul .= ' ' . $this->faker->randomElement($tambahan);
        }

        $judul = ucwords($judul); // Pastikan format judul benar
        $slug = Str::slug($judul);

        // Buat paragraf konten dalam Bahasa Indonesia
        $paragraphs = [];
        $paragraphCount = $this->faker->numberBetween(3, 6);

        $kontenOptions = [
            'Hotel kami menawarkan pengalaman menginap yang nyaman dengan fasilitas lengkap di ' . $this->faker->city . '.',
            'Nikmati pemandangan indah dari kamar yang dirancang dengan sentuhan modern dan tradisional.',
            'Restoran kami menyajikan hidangan lokal dan internasional dengan bahan-bahan segar pilihan dari ' . $this->faker->state . '.',
            'Lokasi strategis memudahkan akses ke berbagai tempat wisata populer di sekitar ' . $this->faker->city . '.',
            'Staf kami yang ramah siap membantu kebutuhan Anda selama menginap di ' . $this->faker->company . '.',
            'Fasilitas spa dan kolam renang tersedia untuk memanjakan diri Anda selama liburan di ' . $this->faker->city . '.',
            'Ruang pertemuan dan ballroom kami cocok untuk berbagai acara bisnis maupun sosial dengan kapasitas hingga ' . $this->faker->numberBetween(50, 500) . ' orang.',
            'Kami berkomitmen memberikan pelayanan terbaik untuk kenyamanan setiap tamu sejak tahun ' . $this->faker->year . '.',
            'Program loyalitas kami menawarkan berbagai keuntungan untuk tamu setia dengan diskon hingga ' . $this->faker->numberBetween(10, 50) . '%.',
            'Keamanan dan kebersihan menjadi prioritas utama kami untuk kenyamanan Anda selama pandemi.',
            'Nikmati sarapan pagi dengan menu buffet yang beragam di restoran hotel mulai pukul ' . $this->faker->time('H:i') . '.',
            'Area bermain anak tersedia untuk memastikan liburan keluarga yang menyenangkan di ' . $this->faker->company . '.',
            'Layanan kamar 24 jam siap memenuhi kebutuhan Anda kapan saja dengan menu spesial dari Chef ' . $this->faker->firstName . '.',
            'Koneksi internet cepat tersedia di seluruh area hotel secara gratis dengan kecepatan hingga ' . $this->faker->numberBetween(50, 200) . ' Mbps.',
            'Parkir luas tersedia untuk tamu yang membawa kendaraan pribadi dengan kapasitas ' . $this->faker->numberBetween(50, 300) . ' kendaraan.'
        ];

        for ($i = 0; $i < $paragraphCount; $i++) {
            // Gunakan konten dari opsi atau buat paragraf acak jika sudah habis
            if (count($kontenOptions) > 0 && $i < count($kontenOptions)) {
                $paragraphs[] = $kontenOptions[$i];
            } else {
                // Buat paragraf dengan kalimat Bahasa Indonesia
                $kalimat = [];
                $jumlahKalimat = $this->faker->numberBetween(3, 6);

                for ($j = 0; $j < $jumlahKalimat; $j++) {
                    $kalimat[] = $this->faker->sentence(8, true);
                }

                $paragraphs[] = implode(' ', $kalimat);
            }
        }

        $isi = '<p>' . implode('</p><p>', $paragraphs) . '</p>';

        // Gunakan formatter khusus Indonesia untuk tanggal
        $tanggalMulai = $this->faker->dateTimeBetween('-1 year', 'now');
        $tanggalUpdate = $this->faker->dateTimeBetween($tanggalMulai, 'now');

        return [
            'jenis' => $jenis,
            'judul' => $judul,
            'slug' => $slug,
            'sub' => $this->faker->optional(0.3)->numberBetween(1, 5),
            'kategori_id' => KategoriArtikel::inRandomOrder()->first()?->id,
            'gambar' => $this->faker->optional(0.7)->imageUrl(800, 600, 'hotel'),
            'isi' => $isi,
            'json' => [
                'pembicara' => $this->generatePembicara(),
                'acara' => $this->generateAcara(),
                'slideshow' => $this->generateSlideshow(),
                'seo' => $this->generateSeo(),
                'lokasi' => [
                    'alamat' => $this->faker->address,
                    'kota' => $this->faker->city,
                    'provinsi' => $this->faker->state,
                    'kode_pos' => $this->faker->postcode,
                    'telepon' => $this->faker->phoneNumber,
                    'email' => $this->faker->companyEmail,
                ],
                'kontak' => [
                    'nama' => $this->faker->name,
                    'jabatan' => $this->faker->jobTitle,
                    'telepon' => $this->faker->phoneNumber,
                    'email' => $this->faker->email,
                ],
                'harga' => $jenis === 'ACARA' ? $this->faker->numberBetween(50000, 500000) : null,
                'kapasitas' => $jenis === 'ACARA' ? $this->faker->numberBetween(10, 200) : null,
            ],
            'status' => 'tampil',
            'karyawan_id' => User::inRandomOrder()->first()?->id,
            'created_at' => $tanggalMulai,
            'updated_at' => $tanggalUpdate,
        ];
    }

    /**
     * Menghasilkan data pembicara untuk kolom json
     *
     * @return array
     */
    protected function generatePembicara(): array
    {
        $pembicara = [];
        $count = $this->faker->numberBetween(1, 3);

        // Gunakan nama Indonesia dari Faker
        for ($i = 0; $i < $count; $i++) {
            // Gunakan gelar akademik Indonesia
            $gelar = $this->faker->randomElement(['', 'Dr.', 'Prof.', 'Ir.', 'Drs.', 'S.E.', 'S.H.', 'M.M.']);
            $nama = $this->faker->name;
            if ($gelar) {
                $namaParts = explode(' ', $nama);
                if (count($namaParts) > 1) {
                    $nama = $gelar . ' ' . $nama;
                }
            }

            // Daftar jabatan dalam Bahasa Indonesia
            $jabatanIndonesia = [
                'Direktur Hotel', 'Manajer Pemasaran', 'Kepala Chef', 'Manajer Operasional',
                'Konsultan Pariwisata', 'Pakar Kuliner', 'Ahli Perhotelan', 'Pengusaha Sukses',
                'Dosen Pariwisata', 'Arsitek Hotel', 'Desainer Interior', 'Pemandu Wisata Senior',
                'General Manager', 'Direktur Eksekutif', 'Kepala Divisi Pengembangan',
                'Ketua Asosiasi Perhotelan ' . $this->faker->state
            ];

            // Buat bio yang lebih dinamis
            $pengalaman = $this->faker->numberBetween(5, 30);
            $universitas = $this->faker->randomElement([
                'Universitas Indonesia', 'Institut Teknologi Bandung', 'Universitas Gadjah Mada',
                'Sekolah Tinggi Pariwisata Bandung', 'Universitas Brawijaya', 'Universitas Airlangga',
                'Universitas Diponegoro', 'Universitas Padjadjaran', 'Universitas Hasanuddin'
            ]);
            $bidang = $this->faker->randomElement([
                'Manajemen Perhotelan', 'Pariwisata', 'Kuliner', 'Manajemen Bisnis',
                'Ekonomi', 'Arsitektur', 'Desain Interior', 'Hospitality'
            ]);
            $kota = $this->faker->city;

            $bioTemplate = [
                "Memiliki pengalaman lebih dari {$pengalaman} tahun di industri perhotelan dan pariwisata. Lulusan {$universitas} jurusan {$bidang}.",
                "Lulusan terbaik dari {$universitas} dengan berbagai penghargaan di bidang {$bidang}. Telah mengelola beberapa hotel bintang 5 di {$kota} dan sekitarnya.",
                "Telah menulis beberapa buku tentang {$bidang} dan pengembangan pariwisata berkelanjutan. Aktif sebagai pembicara di berbagai seminar nasional.",
                "Sering menjadi pembicara di berbagai seminar nasional dan internasional tentang industri perhotelan. Memiliki pengalaman {$pengalaman} tahun di bidang {$bidang}.",
                "Peraih penghargaan sebagai tokoh inspiratif dalam pengembangan pariwisata di {$kota}. Lulusan {$universitas} dengan predikat cum laude.",
                "Aktif dalam berbagai kegiatan sosial untuk pengembangan ekonomi masyarakat melalui pariwisata. Memiliki sertifikasi internasional di bidang {$bidang}.",
                "Pendiri beberapa hotel boutique yang sukses di {$kota} dan sekitarnya. Memiliki pengalaman {$pengalaman} tahun di industri perhotelan.",
                "Konsultan untuk berbagai proyek pengembangan destinasi wisata di Indonesia. Lulusan {$universitas} dan memiliki gelar master dari luar negeri."
            ];

            $pembicara[] = [
                'nama' => $nama,
                'jabatan' => $this->faker->randomElement($jabatanIndonesia),
                'foto' => $this->faker->imageUrl(200, 200, 'people'),
                'bio' => $this->faker->randomElement($bioTemplate),
                'email' => $this->faker->email,
                'telepon' => $this->faker->phoneNumber,
                'sosial_media' => [
                    'instagram' => '@' . strtolower(str_replace(' ', '', $this->faker->firstName)) . $this->faker->numberBetween(1, 999),
                    'linkedin' => 'linkedin.com/in/' . strtolower(str_replace(' ', '-', $this->faker->name)),
                    'twitter' => '@' . strtolower(str_replace(' ', '_', $this->faker->firstName)) . $this->faker->numberBetween(1, 999)
                ]
            ];
        }

        return $pembicara;
    }

    /**
     * Menghasilkan data acara untuk kolom json
     *
     * @return array
     */
    protected function generateAcara(): array
    {
        $acara = [];
        $count = $this->faker->numberBetween(1, 3);

        // Daftar nama acara dalam Bahasa Indonesia
        $namaAcara = [
            'Workshop Kuliner Nusantara', 'Seminar Pariwisata Berkelanjutan',
            'Pameran Seni Lokal', 'Konser Musik Akustik',
            'Perayaan Hari Kemerdekaan', 'Festival Kuliner Indonesia',
            'Pertemuan Bisnis Tahunan', 'Acara Amal untuk Pendidikan',
            'Pelatihan Barista', 'Kompetisi Chef Muda',
            'Talkshow Pengembangan Karir di Industri Perhotelan',
            'Launching Menu Baru Restoran Hotel'
        ];

        // Daftar lokasi dalam Bahasa Indonesia
        $lokasiAcara = [
            'Ballroom Utama Hotel', 'Ruang Pertemuan Garuda',
            'Restoran Utama', 'Taman Hotel',
            'Kolam Renang', 'Lobi Hotel',
            'Ruang Serbaguna Cendrawasih', 'Rooftop Garden'
        ];

        // Daftar deskripsi acara dalam Bahasa Indonesia
        $deskripsiAcara = [
            'Acara ini akan menampilkan berbagai kuliner khas nusantara dengan chef ternama.',
            'Diskusi interaktif tentang pengembangan pariwisata yang berkelanjutan di Indonesia.',
            'Pameran karya seni dari seniman lokal yang menampilkan keindahan budaya Indonesia.',
            'Nikmati alunan musik akustik sambil menikmati hidangan spesial dari chef kami.',
            'Rangkaian kegiatan menarik untuk memperingati hari kemerdekaan Indonesia.',
            'Festival yang menampilkan beragam kuliner khas dari berbagai daerah di Indonesia.',
            'Pertemuan tahunan untuk membahas perkembangan industri perhotelan di Indonesia.',
            'Acara amal yang bertujuan untuk mendukung pendidikan anak-anak kurang mampu.'
        ];

        // Tanggal acara dalam rentang 3 bulan ke depan
        $tanggalMulai = now();
        $tanggalAkhir = now()->addMonths(3);

        for ($i = 0; $i < $count; $i++) {
            $tanggalAcara = $this->faker->dateTimeBetween($tanggalMulai, $tanggalAkhir)->format('Y-m-d');

            $acara[] = [
                'nama' => $this->faker->randomElement($namaAcara),
                'tanggal' => $tanggalAcara,
                'waktu' => $this->faker->time('H:i'),
                'lokasi' => $this->faker->randomElement($lokasiAcara),
                'deskripsi' => $this->faker->randomElement($deskripsiAcara),
            ];
        }

        return $acara;
    }

    /**
     * Menghasilkan data slideshow untuk kolom json
     *
     * @return array
     */
    protected function generateSlideshow(): array
    {
        $slideshow = [];
        $count = $this->faker->numberBetween(2, 4);

        // Daftar judul slideshow dalam Bahasa Indonesia
        $judulSlideshow = [
            'Selamat Datang di Hotel Kami', 'Nikmati Pengalaman Menginap Terbaik',
            'Fasilitas Lengkap untuk Kenyamanan Anda', 'Kamar Mewah dengan Pemandangan Indah',
            'Restoran dengan Menu Spesial', 'Kolam Renang dengan Pemandangan Kota',
            'Ruang Pertemuan untuk Acara Bisnis', 'Spa dan Pusat Kebugaran',
            'Promo Spesial Akhir Pekan', 'Paket Liburan Keluarga'
        ];

        // Daftar deskripsi slideshow dalam Bahasa Indonesia
        $deskripsiSlideshow = [
            'Hotel bintang lima dengan pelayanan terbaik untuk kenyamanan Anda selama menginap.',
            'Nikmati berbagai fasilitas mewah yang kami sediakan untuk pengalaman menginap tak terlupakan.',
            'Lokasi strategis dengan akses mudah ke berbagai tempat wisata dan pusat bisnis.',
            'Kamar yang dirancang dengan sentuhan modern dan tradisional untuk kenyamanan maksimal.',
            'Restoran kami menyajikan hidangan lokal dan internasional dengan bahan-bahan segar pilihan.',
            'Nikmati pemandangan kota yang menakjubkan dari kolam renang rooftop kami.',
            'Ruang pertemuan dengan fasilitas lengkap untuk mendukung kegiatan bisnis Anda.',
            'Manjakan diri Anda dengan berbagai perawatan spa dan fasilitas kebugaran modern.'
        ];

        for ($i = 0; $i < $count; $i++) {
            $slideshow[] = [
                'gambar' => $this->faker->imageUrl(1200, 600, 'hotel'),
                'judul' => $this->faker->randomElement($judulSlideshow),
                'deskripsi' => $this->faker->randomElement($deskripsiSlideshow),
                'link' => '/halaman/tentang-kami',
                'urutan' => $i + 1,
            ];
        }

        return $slideshow;
    }

    /**
     * Menghasilkan data SEO untuk kolom json
     *
     * @return array
     */
    protected function generateSeo(): array
    {
        // Daftar meta title dalam Bahasa Indonesia
        $metaTitle = [
            'Hotel Terbaik di Kota | Pengalaman Menginap Mewah',
            'Fasilitas Lengkap dan Pelayanan Prima | Hotel Kami',
            'Reservasi Kamar Hotel | Harga Terbaik dan Lokasi Strategis',
            'Promo Spesial Menginap | Diskon dan Paket Menarik',
            'Restoran dan Kuliner | Hidangan Lokal dan Internasional'
        ];

        // Daftar meta description dalam Bahasa Indonesia
        $metaDescription = [
            'Hotel kami menawarkan pengalaman menginap terbaik dengan fasilitas lengkap dan pelayanan prima. Lokasi strategis dekat dengan pusat kota dan tempat wisata.',
            'Nikmati kenyamanan menginap di kamar mewah dengan pemandangan indah. Fasilitas lengkap termasuk kolam renang, spa, dan restoran.',
            'Reservasi kamar hotel dengan harga terbaik dan nikmati berbagai fasilitas mewah. Lokasi strategis dengan akses mudah ke berbagai tempat wisata.',
            'Dapatkan promo spesial untuk pengalaman menginap tak terlupakan. Berbagai paket menarik untuk liburan keluarga dan perjalanan bisnis.',
            'Restoran kami menyajikan hidangan lokal dan internasional dengan bahan-bahan segar pilihan. Nikmati pengalaman kuliner terbaik selama menginap.'
        ];

        // Daftar meta keywords dalam Bahasa Indonesia
        $metaKeywords = [
            'hotel, penginapan, kamar mewah, fasilitas lengkap, lokasi strategis',
            'reservasi hotel, booking kamar, harga terbaik, promo hotel, diskon menginap',
            'hotel bintang lima, pelayanan prima, pengalaman menginap, liburan keluarga',
            'restoran hotel, kuliner, hidangan lokal, menu internasional, makanan enak',
            'fasilitas hotel, kolam renang, spa, pusat kebugaran, ruang pertemuan'
        ];

        return [
            'meta_title' => $this->faker->randomElement($metaTitle),
            'meta_description' => $this->faker->randomElement($metaDescription),
            'meta_keywords' => $this->faker->randomElement($metaKeywords),
            'og_title' => $this->faker->randomElement($metaTitle),
            'og_description' => $this->faker->randomElement($metaDescription),
            'og_image' => $this->faker->imageUrl(1200, 630, 'hotel'),
        ];
    }

    /**
     * State untuk halaman beranda
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function beranda()
    {
        return $this->state(function () {
            return [
                'jenis' => 'HALAMAN',
                'status' => 'home',
            ];
        });
    }

    /**
     * State untuk artikel
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function artikel()
    {
        return $this->state(function () {
            return [
                'jenis' => 'ARTIKEL',
            ];
        });
    }
}
<?php

namespace Modules\RajaCms\Filament\Resources;

use AmidEsfahani\FilamentTinyEditor\TinyEditor;
use App\Filament\Forms\Components\CintaEditor;
use App\Filament\Forms\Components\PenyairEditor;
use App\Filament\Forms\Components\PenyairRadio;
use App\Filament\Forms\Components\RajaEditor;
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;
use Modules\RajaCms\Filament\Resources\CmsResource\Forms\AcaraForm;
use Modules\RajaCms\Filament\Resources\CmsResource\Forms\InfoKonten;
use Modules\RajaCms\Filament\Resources\CmsResource\Forms\SeoForm;
use Modules\RajaCms\Filament\Resources\CmsResource\Forms\SlideshowForm;
use Modules\RajaCms\Filament\Resources\CmsResource\Pages;
use App\Helpers\FileHelper;
use Modules\RajaCms\Models\Cms;
use App\Models\KategoriArtikel;
use App\Models\Konfig;
use App\Traits\JcolFieldHydrator;

use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

class CmsResource extends Resource implements HasShieldPermissions
{
    use JcolFieldHydrator;

    protected static ?string $model = Cms::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    // protected static bool $shouldRegisterNavigation = true;
    protected static ?string $navigationGroup = 'Cms';
    protected static ?int $navigationSort = 2;

    /**
     * Daftar prefix permission khusus untuk resource ini.
     *
     * Selain prefix bawaan (view, create, dsb.) kita tambahkan
     * 'view_tema' supaya Filament Shield menghasilkan permission
     * `view_tema_cms`.
     */
    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'publish',
            // prefix khusus untuk halaman Tema
            'view_tema',
        ];
    }

    public static function form(Form $form): Form
    {


        // Buat instance dari class ini untuk mengakses trait
        $instance = new static();

        // Buat form dengan schema
        $form = $form->schema([

            Grid::make()->schema([
                PenyairRadio::make('jenis')
                    ->options(Konfig::jcol('website', 'tipe_konten'))

                    ->icons([
                        'ARTIKEL' => 'heroicon-o-document-text',
                        'HALAMAN' => 'heroicon-o-document',

                        // 'WIDGET' => 'heroicon-o-squares-2x2',
                        'ACARA' => 'heroicon-o-calendar',

                    ])
                    ->default('HALAMAN')
                    ->buttonStyle()
                    ->columnSpanFull()
                    ->live(),
            ])->columnSpanFull(),


            Grid::make(1)
                ->schema([

                    RajaEditor::make('isi')
                        ->visible(fn($get) => in_array($get('jenis'), ['ARTIKEL', 'HALAMAN', 'ACARA', 'GALERI']))
                        ->columnSpanFull()
                        ->height('800px')
                        ->enableRajaPicker(true)
                        ->rajaPickerCollection('cms')
                        ->rajaPickerMaxFileSize(10)
                        ->required(),



                    // SLIDESHOW

                    AcaraForm::make()->visible(fn($get) => $get('jenis') == 'ACARA'),

                    // SLIDESHOW HOME
                    SlideshowForm::make()->visible(fn($get) => $get('status') == 'home'),


 
                    SeoForm::make()->visible(fn($get) => in_array($get('jenis'), ['ARTIKEL', 'HALAMAN', 'ACARA', 'GALERI'])),

                ])->columnSpan(4),


            Section::make('informasi konten')
                ->schema([

                    InfoKonten::make(),





                ])->columnSpan(2)->columns(2),





        ])->columns(6);

        // Terapkan JcolFieldHydrator pada form
        return $instance->applyJcolHydrator($form);
    }


    private static function generateUniqueSlug($slug)
    {
        $baseSlug = $slug;
        $count = 2;

        // Check if the slug already exists in the database
        while (DB::table('cms')->where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $count; // Add a number to the end of the slug
            $count++;
        }

        return $slug; // Return the unique slug
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            // \App\Filament\Widgets\IkonWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHalaman::route('/'),
            'create' => Pages\CreateCmsHalaman::route('/create'),

            'modul-editor' => Pages\ModulEditor::route('/modul-editor'),
            'tema' => Pages\Tema::route('/tema'),
            'tema-editor' => Pages\TemaEditor::route('/tema-editor'),

            'view' => Pages\ViewCms::route('/{record}'),
            'edit' => Pages\EditCms::route('/{record}/edit'),
        ];
    }
} 
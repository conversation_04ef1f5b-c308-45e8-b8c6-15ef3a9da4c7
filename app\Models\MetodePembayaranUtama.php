<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Penjualan;
/**
 * @mixin IdeHelperMetodePembayaranUtama
 */
class MetodePembayaranUtama extends Model
{
    use HasFactory;

    public function getTable()
    {
         return config('tabel.t_metode_pembayaran.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_metode_pembayaran.kolom', []);
    }


    protected $casts = [
        'status' => 'boolean',
    ];

   

    // Relasi dengan Reservasi jika diperlukan
    public function penjualans()
    {
        return $this->hasMany(Penjualan::class);
    }
 
    public function isActive(): bool
    {
        return (bool) $this->status;
    }

    // Method untuk mendapatkan field info pengirim sebagai array
    public function getInfoPengirimArray(): array
    {
        if (empty($this->info_pengirim)) {
            return [];
        }

        $data = json_decode($this->info_pengirim, true);
        return is_array($data) ? $data : [];
    }

    // Method untuk memeriksa apakah memerlukan input pelanggan
    public function membutuhkanInfoPengirim(): bool
    {
        return ! empty($this->getInfoPengirimArray());
    }

        public function hasValidInfoPengirim(): bool
    {
        // Cek nilai info_pengirim
        if ($this->info_pengirim === null || $this->info_pengirim === '' ||
            $this->info_pengirim === '[]' || $this->info_pengirim === '{}') {
            return false;
        }

        try {
            // Parse JSON jika string
            $data = is_string($this->info_pengirim)
                ? json_decode($this->info_pengirim, true)
                : $this->info_pengirim;

            // Cek field
            if (is_array($data)) {
                foreach ($data as $field) {
                    if (isset($field['type']) && isset($field['name'])) {
                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
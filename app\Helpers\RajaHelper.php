<?php 
 
// Helper functions - tidak boleh ada namespace
// Fungsi-fungsi ini harus berada di global scope

use Illuminate\Support\Collection;
use App\Helpers\AplikasiHelper;
use Illuminate\Support\Facades\Auth;

// Tambahkan di app/Helpers/ShortcodeHelper.php atau file helper lainnya
if (!function_exists('shortcode')) {
    function shortcode($content) {
        return app('shortcode')->process($content);
    }
}
// tambahkan
if (! function_exists('angkabersih')) {
  function angkabersih($harga)
  {
    return preg_replace('/[.,]/', '', $harga);
  }
}
if (! function_exists('bersihbersih')) {
  function bersihbersih($kata)
  {
    // Daftar tag yang diizinkan
    $allowed_tags = '<br><p>';

    // Langkah 1: Hapus semua tag HTML
    $cleaned = strip_tags($kata, $allowed_tags);

    // Langkah 2: Bersihkan atribut dari tag yang diizinkan
    $cleaned = preg_replace('/<(br|p|strong|b)(?:\s+[^>]*)?>/', '<$1>', $cleaned);

    return $cleaned;
  }
}
if (! function_exists('rupiah')) {
  function rupiah($angka, $desimal = 0)
  {
    return 'Rp ' . number_format((float)$angka, $desimal, ',', '.');
  }
}

if (! function_exists('selisihWaktu')) {
  function selisihWaktu($tgl, $tgl2 = null)
  {
    // If $tgl2 is not provided, use the current time
    $tgl2 = $tgl2 ? \Carbon\Carbon::parse($tgl2) : \Carbon\Carbon::now();

    // Parse $tgl as a Carbon instance
    $tgl1 = \Carbon\Carbon::parse($tgl);

    // Calculate the difference between the two dates
    $diff = $tgl1->diff($tgl2);

    // Return the difference in days, hours, and minutes
    return [
      'hari' => $diff->days, // The total days
      'malam' => $diff->days, // The total days
      'jam' => $diff->h,    // The remaining hours
      'menit' => $diff->i     // The remaining minutes
    ];
  }
}

if (! function_exists('inputbersih')) {

  function inputbersih($input)
  {
    // Hapus tag HTML  
    $clean = strip_tags($input);

    // Hapus karakter non-printable  
    $clean = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $clean);

    // Hapus spasi berlebih  
    $clean = trim(preg_replace('/\s+/', ' ', $clean));

    return $clean;
  }
}


if (! function_exists('enkripsi')) {
  function enkripsi($data)
  {
    $password = "penyair";
    $cipher = "AES-256-CBC"; // Cipher yang digunakan
    $key = hash('sha256', $password); // Hash password menjadi key
    $iv = substr(hash('sha256', uniqid()), 0, 16); // Inisialisasi IV
    $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv); // Enkripsi data
    $hasil = base64_encode($encrypted . "::" . $iv); // Gabungkan hasil enkripsi dan IV

    return
      $hasil;
  }
};

if (! function_exists('dekripsi')) {
  function dekripsi($data)
  {
    $password = "penyair";
    $cipher = "AES-256-CBC"; // Cipher yang digunakan
    $key = hash('sha256', $password); // Hash password menjadi key

    // Pisahkan hasil enkripsi dan IV
    list($encryptedData, $iv) = explode("::", base64_decode($data), 2);

    $decrypted = openssl_decrypt($encryptedData, $cipher, $key, 0, $iv); // Dekripsi data
    return $decrypted;
  }
};


if (!function_exists('bacaSemuaAplikasi')) {
  function bacaSemuaAplikasi(): Collection
  {
    return AplikasiHelper::bacaSemuaAplikasi();
  }
}

 if (!function_exists('sayaSuperAdmin')) {
  function  sayaSuperAdmin()
  {
    return Auth::check() && Auth::user()->hasRole('super_admin');
 
  }
}


if (!function_exists('render_htmlblade')) {
  /**
   * Render file HTML dengan Blade directives
   */
  function render_htmlblade($filename, $data = [])
  {
      $temaAktif = config('tema.aktif', 'default');
      $filePath = public_path("tema/{$temaAktif}/{$filename}.html");
      
      if (!\Illuminate\Support\Facades\File::exists($filePath)) {
          return "File {$filename}.html tidak ditemukan";
      }
      
      $htmlContent = \Illuminate\Support\Facades\File::get($filePath);

 

      // Simpan ke file temporary
      $tempViewName = 'temp_html_' . uniqid();
      $tempPath = storage_path("temp/{$tempViewName}.blade.php");
      if (!file_exists(storage_path('temp'))) {
          mkdir(storage_path('temp'), 0777, true);
      }
      file_put_contents($tempPath, $htmlContent);

      // Render dengan Blade
      $output = view()->file($tempPath, $data)->render();

      // Hapus file temporary
      unlink($tempPath);

      return $output;
  }
}


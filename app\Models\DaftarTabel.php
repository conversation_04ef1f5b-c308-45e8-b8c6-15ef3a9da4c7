<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin IdeHelperDaftarTabel
 */
class DaftarTabel extends Model
{
    protected $fillable = ['nama_tabel'];
    
    // Tidak menggunakan tabel database fisik
    protected $table = null;
    
    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();
        
        // Tambahkan global scope untuk selalu mengambil daftar tabel
        static::addGlobalScope('daftarTabel', function (Builder $builder) {
            // Tidak perlu melakukan apa-apa disini karena kita akan mengganti query-nya
        });
    }
    
    /**
     * Mendapatkan instance baru dari query builder.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function query()
    {
        $instance = new static;
        
        // Jalankan query untuk mendapatkan daftar tabel database
        $daftarTabel = DB::connection()->getDoctrineSchemaManager()->listTableNames();
        
        // Filter tabel sistem jika diperlukan
        $daftarTabel = array_filter($daftarTabel, function ($tabel) {
            $tabelSistem = ['migrations', 'jobs', 'failed_jobs', 'password_reset_tokens', 'personal_access_tokens'];
            return !in_array($tabel, $tabelSistem);
        });
        
        // Konversi array tabel menjadi koleksi objek
        $collection = collect();
        foreach ($daftarTabel as $tabel) {
            $modelTabel = new static();
            $modelTabel->nama_tabel = $tabel;
            $collection->push($modelTabel);
        }
        
        // Buat query builder dari koleksi
        return $instance->newQueryWithoutScopes()->setModel($instance)->whereIn('id', $collection->pluck('id'))->getModel()->newCollection($collection);
    }
    
    /**
     * Mendapatkan model baru dari query builder.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newEloquentBuilder($query)
    {
        return new Builder($query);
    }
}
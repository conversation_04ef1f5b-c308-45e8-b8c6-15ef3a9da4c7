<?php
// VERSI 3
namespace App\Filament\Widgets;

use App\Filament\Forms\Components\FieldBuilder;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Widgets\Widget;
use Illuminate\Support\Str;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

class RajaDesainerWidget extends Widget implements HasForms
{
   use InteractsWithForms;

   // Path view ke widget
   protected static string $view = 'widgets.rajadesainer-widget';

   public static function canView(): bool
   {
       // Widget ini akan muncul di dashboard
       if(Auth::user()->hasRole('superadmin')){
          return true;
       }
       return false;
   }


   // Widget akan memakan lebar penuh
   protected int|string|array $columnSpan = 'full';

   // Properti untuk menyimpan data form
   public array $formData = [
      'desainer_info_pengirim' => [],
      'preview_json' => '',
      'preview_data' => '',
      'tampilkan_lanjutan' => false, // Properti baru untuk toggle tampilan field lanjutan
   ];

   /**
    * Definisi formulir
    */
   public function form(Form $form): Form
   {
      return $form
         ->schema([
            Grid::make()
               ->schema([
                  // Kolom 1 (60%): Bagian desainer field
                  Grid::make()
                     ->schema([
                        // Checkbox untuk menampilkan/sembunyikan field lanjutan
                        Checkbox::make('tampilkan_lanjutan')
                           ->label('Tampilkan pengaturan lanjutan')
                           ->helperText('Centang untuk menampilkan semua pengaturan field')
                           ->live()
                           ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Mengaktifkan ini akan menampilkan semua opsi pengaturan field'),

                        // Repeater untuk mengelola field
                        Repeater::make('desainer_info_pengirim')
                           ->grid(1)
                           ->columns(4)
                           ->schema([
                              // 1. Jenis field
                              Select::make('jenis_field')
                                 ->options([
                                    'textinput' => 'TextInput',
                                    'textarea' => 'Textarea',
                                    'select' => 'Select Dropdown',
                                    'radio' => 'Radio Button',
                                    'placeholder' => 'Placeholder form',
                                    'viewfield' => 'View Field',
                                    'fileupload' => 'File Upload',
                                  
                                 ])
                                 ->label('Jenis Input')
                                 ->placeholder('Pilih jenis')
                                 ->live()
                                 ->required()
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Jenis komponen input yang akan digunakan'),

                              // 2. Nama field
                              TextInput::make('json_nama')
                                 ->label('Nama field')
                                 ->placeholder('Contoh: nama_pengirim')
                                 ->required()
                                 ->live()
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Nama field yang akan digunakan dalam data JSON'),

                              // 3. Opsi label
                              Select::make('field_label')
                                 ->options([
                                    'auto' => 'Otomatis',
                                    'false' => 'jangan tampilkan',
                                    'custom' => 'Kustom'
                                 ])
                                 ->default('auto')
                                 ->live()
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Cara menampilkan label pada formulir')
                                 ->visible(function () {
                                    return $this->formData['tampilkan_lanjutan'] ?? false;
                                 })
                                 ->afterStateUpdated(function (Get $get, Set $set) {
                                    $isinya = $get('field_label');
                                    if ($isinya == 'auto') {
                                       $set('json_label', Str::title($get('json_nama')));
                                    }
                                 }),

                              // 4. Label kustom
                              TextInput::make('json_label')
                                 ->label('Label tampilan')
                                 ->placeholder('Label yang ditampilkan')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Teks label yang akan ditampilkan di formulir')
                                 ->disabled(fn(Get $get) => $get('field_label') == 'auto' || $get('field_label') == 'false')
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return $tampilkanLanjutan && $get('field_label') == 'custom';
                                 }),

                              // 5. Opsi untuk select dan radio
                              TextInput::make('field_options')
                                 ->required()
                                 ->label('Pilihan (dipisahkan koma)')
                                 ->placeholder('Contoh: bca,bni,mandiri')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Daftar pilihan yang tersedia, pisahkan dengan koma')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'select' || $get('jenis_field') == 'radio')
                                 ->columnSpan(2),

                              // 6. Placeholder
                              TextInput::make('placeholder')
                                 ->label('Placeholder')
                                 ->placeholder('Contoh: Masukkan nama pengirim')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Teks petunjuk yang muncul saat field masih kosong')
                                 ->columnSpan(2)
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return in_array($get('jenis_field'), ['textinput', 'textarea']) && $tampilkanLanjutan;
                                 }),

                              // Field untuk view path (khusus ViewField)
                              TextInput::make('view')
                                 ->label('Path View')
                                 ->placeholder('Contoh: kasir.components.form-view')
                                 ->helperText('Path ke Blade view yang akan dirender')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'viewfield')
                                 ->columnSpan(2),

                              // Field untuk data JSON (khusus ViewField)
                              Textarea::make('data')
                                 ->label('Data View (JSON)')
                                 ->placeholder('data json')
                                 ->helperText('Data JSON yang akan diteruskan ke view')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'viewfield')
                                 ->columnSpan(2),

                              // Field untuk directory (khusus FileUpload)
                              TextInput::make('directory')
                                 ->label('Directory')
                                 ->placeholder('Contoh: uploads/gambar')
                                 ->helperText('Lokasi penyimpanan file')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'fileupload')
                                 ->columnSpan(2),

                              // Toggle untuk image editor (khusus FileUpload)
                              Toggle::make('editor')
                                 ->inline(false)
                                 ->label('Image Editor')
                                 ->helperText('Aktifkan editor gambar')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'fileupload')
                                 ->columnSpan(2),

                   

                              // Field untuk resize width (khusus FileUpload)
                              TextInput::make('resizewidth')
                                 ->label('Resize Width')
                                 ->placeholder('Contoh: 1920')
                                 ->helperText('Lebar gambar setelah resize')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'fileupload')
                                 ->columnSpan(1),

                              // Field untuk resize height (khusus FileUpload)
                              TextInput::make('resizeheight')
                                 ->label('Resize Height')
                                 ->placeholder('Contoh: 1080')
                                 ->helperText('Tinggi gambar setelah resize')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'fileupload')
                                 ->columnSpan(1),

                              // Field untuk prefix nama file (khusus FileUpload)
                              TextInput::make('file_prefix')
                                 ->label('Prefix Nama File')
                                 ->placeholder('Contoh: gambar_')
                                 ->helperText('Awalan untuk nama file yang diupload')
                                 ->visible(fn(Get $get) => $get('jenis_field') == 'fileupload')
                                 ->columnSpan(2),

                              // 6.1 Prefix
                              TextInput::make('prefix')
                                 ->label('Prefix')
                                 ->placeholder('Contoh: Rp, $, dll')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Teks yang muncul di awal field')
                                 ->columnSpan(1)
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return in_array($get('jenis_field'), ['textinput', 'select']) && $tampilkanLanjutan;
                                 }),

                              // 6.2 Suffix
                              TextInput::make('suffix')
                                 ->label('Suffix')
                                 ->placeholder('Contoh: kg, cm, dll')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Teks yang muncul di akhir field')
                                 ->columnSpan(1)
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return in_array($get('jenis_field'), ['textinput', 'select']) && $tampilkanLanjutan;
                                 }),

                              // 7. Pengaturan ukuran textarea
                              Grid::make(2)->schema([
                                 TextInput::make('lebar')
                                    ->label('Lebar (kolom)')
                                    ->placeholder('Contoh: 20')
                                    ->default(5)
                                    ->numeric()
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Jumlah kolom untuk lebar textarea'),

                                 TextInput::make('tinggi')
                                    ->label('Tinggi (baris)')
                                    ->placeholder('Contoh: 5')
                                    ->default(5)
                                    ->numeric()
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Jumlah baris untuk tinggi textarea'),
                              ])
                                 ->visible(function (Get $get) {
                                    // Ambil nilai dari luar repeater
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return $get('jenis_field') == 'textarea' && $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 8. Autosize untuk textarea
                              Toggle::make('autosize')
                                 ->inline(false)
                                 ->label('Otomatis menyesuaikan ukuran')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Textarea akan menyesuaikan ukuran otomatis sesuai konten')
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return $get('jenis_field') == 'textarea' && $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 9. Tooltips
                              TextInput::make('tooltip')
                                 ->label('Tooltip bantuan')
                                 ->placeholder('Teks bantuan tambahan')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Teks bantuan yang muncul saat pengguna mengarahkan kursor ke ikon info')
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return $get('jenis_field') != 'grid' && $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 10. Default value untuk textarea dan textinput
                              Textarea::make('default')
                                 ->label('Nilai default')
                                 ->placeholder('Nilai awal')
                                 ->rows(3)
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Nilai yang akan diisi otomatis saat formulir dimuat')
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return in_array($get('jenis_field'), ['placeholder', 'textarea', 'textinput']) && $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 11. Default value untuk select dan radio
                              TextInput::make('default')
                                 ->label('Nilai default')
                                 ->placeholder('Nilai awal')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Nilai awal yang dipilih')
                                 ->visible(function (Get $get) {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    return in_array($get('jenis_field'), ['select', 'radio']) && $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 12. Lebar kolom
                              Select::make('columnspan')
                                 ->label('Lebar kolom')
                                 ->options([
                                    '1' => '1 - Sempit',
                                    '2' => '2 - Sedang',
                                    '3' => '3 - Lebar',
                                    'full' => 'Full - Penuh'
                                 ])
                                 ->default('1')
                                 ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Ukuran lebar kolom yang akan digunakan field ini')
                                 ->visible(function () {
                                    $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                    // Grid tetap memerlukan columnspan
                                    return $tampilkanLanjutan;
                                 })
                                 ->columnSpan(2),

                              // 13. Toggle lainnya
                              Grid::make(2)->schema([
                                 Toggle::make('required')
                                    ->inline(false)
                                    ->label('Wajib diisi')
                                    ->default(true)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Field ini harus diisi dan tidak boleh kosong')
                                    ->visible(fn(Get $get) => !in_array($get('jenis_field'), ['placeholder', 'grid'])),

                                 Toggle::make('disabled')
                                    ->inline(false)
                                    ->label('disable')
                                    ->default(false)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Field ini tidak dapat diubah')
                                    ->visible(fn(Get $get) => $get('jenis_field') != 'grid'),

                                 Toggle::make('readonly')
                                    ->inline(false)
                                    ->label('Readonly')
                                    ->default(false)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Field dapat dilihat tetapi tidak dapat diubah')
                                    ->visible(function (Get $get) {
                                       $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                       return in_array($get('jenis_field'), ['textinput', 'textarea']) && $tampilkanLanjutan;
                                    }),

                                 Toggle::make('is_numeric')
                                    ->inline(false)
                                    ->label('Numerik')
                                    ->default(false)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Field hanya menerima input angka')
                                    ->visible(fn(Get $get) => $get('jenis_field') == 'textinput'),

                                 Toggle::make('inline')
                                    ->inline(false)
                                    ->label('Inline')
                                    ->default(false)
                                    ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Tampilkan pilihan secara horizontal')
                                    ->visible(function (Get $get) {
                                       $tampilkanLanjutan = $this->formData['tampilkan_lanjutan'] ?? false;
                                       return $get('jenis_field') == 'radio' && $tampilkanLanjutan;
                                    }),
                              ])
                                 ->columns(2)
                                 ->columnSpan(2),
                           ])
                           ->itemLabel(function (array $state): ?string {
                              return $state['json_label'] ?? $state['json_nama'] ?? 'Field Baru';
                           })
                           ->addActionLabel('Tambah Field Baru')
                           ->reorderable()
                           ->defaultItems(0)
                           ->columnSpan('full'),
                     ])
                     ->columnSpan(['default' => 1, 'lg' => 9]), // 60% dari total lebar

                  // Kolom 2 (40%): Bagian preview JSON dan form
                  Grid::make()
                     ->schema([
                        // Tampilan JSON yang dihasilkan (menggunakan JsonColumn)
                        JsonColumn::make('preview_json')
                           ->label('JSON Preview')
                           ->editorOnly()
                           ->editorHeight(550)
                           ->columnSpan('full'),

                        // Hidden field untuk menyimpan data preview
                        Hidden::make('preview_data'),

                        // Preview form secara visual
                        Section::make('Preview Formulir')
                           ->description('Pratinjau formulir yang akan ditampilkan')
                           ->schema(function (Get $get) {
                              $jsonData = $get('preview_data');
                              if (! empty($jsonData)) {
                                 return FieldBuilder::buatFields(
                                    $jsonData,
                                    'preview_form_data',
                                    true,
                                    'Belum ada field yang ditambahkan'
                                 );
                              }

                              return [
                                 Placeholder::make('pesan_preview')
                                    ->content('Klik tombol Generate untuk melihat preview')
                                    ->extraAttributes(['class' => 'text-center py-4 text-gray-500 italic'])
                              ];
                           })
                           ->columnSpan('full'),
                     ])
                     ->columnSpan(['default' => 1, 'lg' => 3]), // 40% dari total lebar
               ])
               ->columns(['default' => 1, 'lg' => 12]) // Total 6 kolom untuk pembagian 4:2 (67%:33%)
         ])
         ->statePath('formData');
   }

   /**
    * Inisialisasi data saat widget dimuat
    */
   public function mount(): void
   {
      $this->form->fill();
   }

   /**
    * Memeriksa apakah string adalah JSON yang valid
    *
    * @param string $string String yang akan diperiksa
    * @return bool True jika valid, false jika tidak
    */
   protected function isValidJson(string $string): bool
   {
      if (empty($string)) {
         return false;
      }

      json_decode($string);
      return (json_last_error() === JSON_ERROR_NONE);
   }

   /**
    * Fungsi untuk mereset semua field di desainer
    */
   public function resetDesainer(): void
   {
      // Reset repeater dengan array kosong
      $this->form->fill([
         'desainer_info_pengirim' => [],
         'preview_json' => '',
         'preview_data' => '',
      ]);

      // Tampilkan notifikasi sukses
      Notification::make()
         ->title('Reset Berhasil')
         ->body('Semua field telah dihapus')
         ->success()
         ->send();
   }

   /**
    * Fungsi untuk menghasilkan JSON dan preview form dari desainer
    */
   public function generateJsonPreview(): void
   {
      // Ambil data dari form
      $formData = $this->form->getState();
      $repeaterData = $formData['desainer_info_pengirim'] ?? [];

      // Jika data kosong, kembalikan array kosong
      if (empty($repeaterData)) {
         $jsonPreview = '[]';
         $this->form->fill([
            'desainer_info_pengirim' => $repeaterData, // Simpan data repeater yang ada (kosong)
            'preview_json' => $jsonPreview,
            'preview_data' => $jsonPreview,
            'tampilkan_lanjutan' => $formData['tampilkan_lanjutan'] ?? false, // Pertahankan status checkbox
         ]);

         Notification::make()
            ->title('Data Kosong')
            ->body('Silakan tambahkan field terlebih dahulu')
            ->warning()
            ->send();

         return;
      }

      // Proses data repeater menjadi format JSON yang dibutuhkan FieldBuilder
      $jsonData = [];
      foreach ($repeaterData as $item) {
         $fieldData = [
            'type' => $item['jenis_field'] ?? 'textinput',
            'name' => $item['json_nama'] ?? '',
         ];

         // Hanya tambahkan required jika bukan placeholder atau grid
         if (!in_array($fieldData['type'], ['placeholder', 'grid'])) {
            $fieldData['required'] = $item['required'] ?? true;
         }

         // Handle label berdasarkan pilihan
         if (($item['field_label'] ?? 'auto') === 'custom') {
            $fieldData['label'] = $item['json_label'] ?? '';
         } elseif (($item['field_label'] ?? 'auto') === 'false') {
            $fieldData['label'] = false;
         }

         // Handle options untuk select dan radio
         if (in_array($item['jenis_field'] ?? '', ['select', 'radio']) && ! empty($item['field_options'])) {
            // Pisahkan options berdasarkan koma dan bersihkan spasi
            $options = array_map('trim', explode(',', $item['field_options']));

            // Ubah format menjadi objek JSON dengan key dan value yang sama
            $optionsObj = [];
            foreach ($options as $option) {
               $optionsObj[$option] = $option;
            }

            // Tambahkan ke fieldData sebagai objek
            $fieldData['options'] = $optionsObj;
         }

         // Tambahkan properti tambahan (tooltip tidak untuk grid)
         if (! empty($item['tooltip']) && $fieldData['type'] !== 'grid') {
            $fieldData['tooltip'] = $item['tooltip'];
         }

         if (! empty($item['placeholder']) && in_array($item['jenis_field'], ['textinput', 'textarea'])) {
            $fieldData['placeholder'] = $item['placeholder'];
         }

         // Tambahkan prefix dan suffix untuk textinput dan select
         if (! empty($item['prefix']) && in_array($item['jenis_field'], ['textinput', 'select'])) {
            $fieldData['prefix'] = $item['prefix'];
         }

         if (! empty($item['suffix']) && in_array($item['jenis_field'], ['textinput', 'select'])) {
            $fieldData['suffix'] = $item['suffix'];
         }

         if (isset($item['default']) && ! empty($item['default'])) {
            $fieldData['default'] = $item['default'];
         }

         // Properti khusus textarea
         if ($item['jenis_field'] === 'textarea') {
            if (! empty($item['lebar'])) {
               $fieldData['lebar'] = (int) $item['lebar'];
            }

            if (! empty($item['tinggi'])) {
               $fieldData['tinggi'] = (int) $item['tinggi'];
            }

            if (isset($item['autosize']) && $item['autosize']) {
               $fieldData['autosize'] = true;
            }
         }

         // Properti readonly dan disabled
         if (isset($item['readonly']) && $item['readonly']) {
            $fieldData['readonly'] = true;
         }

         // Hanya tambahkan disabled jika bukan grid
         if (isset($item['disabled']) && $item['disabled'] && $fieldData['type'] !== 'grid') {
            $fieldData['disabled'] = true;
         }

         // Properti is_numeric untuk TextInput
         if (isset($item['is_numeric']) && $item['is_numeric'] && $item['jenis_field'] === 'textinput') {
            $fieldData['is_numeric'] = true;
         }

         // Properti inline untuk radio
         if (isset($item['inline']) && $item['inline'] && $item['jenis_field'] === 'radio') {
            $fieldData['inline'] = true;
         }

         // ViewField khusus
         if ($item['jenis_field'] === 'viewfield') {
            if (!empty($item['view'])) {
               $fieldData['view'] = $item['view'];
            }

            if (!empty($item['data'])) {
               try {
                  // Coba parse JSON data
                  $viewData = json_decode($item['data'], true);
                  if (json_last_error() === JSON_ERROR_NONE && is_array($viewData)) {
                     $fieldData['data'] = $viewData;
                  }
               } catch (\Exception) {
                  // Jika gagal, masukkan sebagai string biasa
                  $fieldData['data'] = ['content' => $item['data']];
               }
            }
         }

         // FileUpload khusus
         if ($item['jenis_field'] === 'fileupload') {
            // Tambahkan directory jika ada
            if (!empty($item['directory'])) {
               $fieldData['directory'] = $item['directory'];
            }

            // Tambahkan image editor jika diaktifkan
            if (isset($item['editor']) && $item['editor']) {
               $fieldData['editor'] = true;
            }

            // Tambahkan resize width jika ada
            if (!empty($item['resizewidth'])) {
               $fieldData['resizewidth'] = $item['resizewidth'];
            }

            // Tambahkan resize height jika ada
            if (!empty($item['resizeheight'])) {
               $fieldData['resizeheight'] = $item['resizeheight'];
            }

            // Tambahkan prefix nama file jika ada
            if (!empty($item['file_prefix'])) {
               $fieldData['prefix'] = $item['file_prefix'];
            }
         }

         // Grid khusus
         if ($item['jenis_field'] === 'grid') {
            // Tambahkan columns jika ada
            if (!empty($item['columns'])) {
               // Cek apakah columns berupa JSON
               if ($this->isValidJson($item['columns'])) {
                  $fieldData['columns'] = json_decode($item['columns'], true);
               } else {
                  // Jika bukan JSON, anggap sebagai angka
                  $fieldData['columns'] = (int) $item['columns'];
               }
            }

            // Tambahkan schema jika ada
            if (!empty($item['schema'])) {
               try {
                  // Coba parse JSON schema
                  $schemaData = json_decode($item['schema'], true);
                  if (json_last_error() === JSON_ERROR_NONE && is_array($schemaData)) {
                     $fieldData['schema'] = $schemaData;
                  }
               } catch (\Exception) {
                  // Jika gagal, biarkan schema kosong
                  $fieldData['schema'] = [];
               }
            }
         }
         // Properti lebar kolom
         if (! empty($item['columnspan'])) {
            $fieldData['columnspan'] = $item['columnspan'];
         }

         $jsonData[] = $fieldData;
      }

      // Hasilkan JSON dengan format yang rapi
      $jsonPreview = json_encode($jsonData, JSON_PRETTY_PRINT);

      // Update form dengan hasil JSON, TETAPI tetap pertahankan data repeater dan status checkbox
      $this->form->fill([
         'desainer_info_pengirim' => $repeaterData, // PENTING: Simpan kembali data repeater yang ada
         'preview_json' => $jsonPreview,
         'preview_data' => $jsonPreview,
         'tampilkan_lanjutan' => $formData['tampilkan_lanjutan'] ?? false, // Pertahankan status checkbox
      ]);

      // Tampilkan notifikasi sukses
      Notification::make()
         ->title('JSON Berhasil Dibuat')
         ->body('Preview form telah diperbarui')
         ->success()
         ->send();
   }

   /**
    * Fungsi untuk mengimpor data dari JSON ke dalam desainer
    */
   public function importFromJson(): void
   {
      // Ambil data dari form
      $formData = $this->form->getState();
      $jsonString = $formData['preview_json'] ?? '';

      // Validasi JSON
      if (empty($jsonString) || ! $this->isValidJson($jsonString)) {
         Notification::make()
            ->title('Format JSON Tidak Valid')
            ->body('Pastikan format JSON sudah benar')
            ->danger()
            ->send();
         return;
      }

      // Decode JSON
      $jsonData = json_decode($jsonString, true);

      // Validasi struktur JSON
      if (! is_array($jsonData)) {
         Notification::make()
            ->title('Format JSON Tidak Sesuai')
            ->body('JSON harus berupa array dari field')
            ->danger()
            ->send();
         return;
      }

      // Ubah format JSON ke format repeater
      $repeaterData = [];

      foreach ($jsonData as $item) {
         // Skip jika tidak ada type atau name
         if (! isset($item['type']) || ! isset($item['name'])) {
            continue;
         }

         // Data dasar
         $fieldData = [
            'jenis_field' => $item['type'],
            'json_nama' => $item['name'],
         ];

         // Handle label
         if (isset($item['label'])) {
            if ($item['label'] === false) {
               $fieldData['field_label'] = 'false';
            } else {
               $fieldData['field_label'] = 'custom';
               $fieldData['json_label'] = $item['label'];
            }
         } else {
            $fieldData['field_label'] = 'auto';
            $fieldData['json_label'] = Str::title($item['name']);
         }

         // Handle options (untuk select dan radio)
         if (isset($item['options'])) {
            if (is_array($item['options'])) {
               $fieldData['field_options'] = implode(', ', array_keys($item['options']));
            } else {
               $fieldData['field_options'] = $item['options'];
            }
         }

         // Handle properti tambahan
         if (isset($item['placeholder'])) {
            $fieldData['placeholder'] = $item['placeholder'];
         }

         if (isset($item['tooltip']) && $item['type'] !== 'grid') {
            $fieldData['tooltip'] = $item['tooltip'];
         }

         if (isset($item['prefix'])) {
            $fieldData['prefix'] = $item['prefix'];
         }

         if (isset($item['suffix'])) {
            $fieldData['suffix'] = $item['suffix'];
         }

         if (isset($item['default'])) {
            $fieldData['default'] = $item['default'];
         }

         // Handle properti textarea
         if ($item['type'] === 'textarea') {
            if (isset($item['lebar'])) {
               $fieldData['lebar'] = $item['lebar'];
            }

            if (isset($item['tinggi'])) {
               $fieldData['tinggi'] = $item['tinggi'];
            }

            if (isset($item['autosize'])) {
               $fieldData['autosize'] = $item['autosize'];
            }
         }

         // Handle properti lainnya
         if (isset($item['required']) && $item['type'] !== 'grid') {
            $fieldData['required'] = $item['required'];
         }

         if (isset($item['readonly'])) {
            $fieldData['readonly'] = $item['readonly'];
         }

         if (isset($item['disabled']) && $item['type'] !== 'grid') {
            $fieldData['disabled'] = $item['disabled'];
         }

         if (isset($item['is_numeric'])) {
            $fieldData['is_numeric'] = $item['is_numeric'];
         }

         if (isset($item['inline'])) {
            $fieldData['inline'] = $item['inline'];
         }

         if (isset($item['columnspan'])) {
            $fieldData['columnspan'] = $item['columnspan'];
         }

         // Handle view dan data untuk ViewField
         if ($item['type'] === 'viewfield') {
            if (isset($item['view'])) {
               $fieldData['view'] = $item['view'];
            }

            if (isset($item['data']) && is_array($item['data'])) {
               $fieldData['data'] = json_encode($item['data'], JSON_PRETTY_PRINT);
            }
         }

         // Handle properti khusus FileUpload
         if ($item['type'] === 'fileupload') {
            if (isset($item['directory'])) {
               $fieldData['directory'] = $item['directory'];
            }

            if (isset($item['editor'])) {
               $fieldData['editor'] = $item['editor'];
            }

            if (isset($item['resizewidth'])) {
               $fieldData['resizewidth'] = $item['resizewidth'];
            }

            if (isset($item['resizeheight'])) {
               $fieldData['resizeheight'] = $item['resizeheight'];
            }

            if (isset($item['prefix'])) {
               $fieldData['file_prefix'] = $item['prefix'];
            }
         }

         // Handle properti khusus Grid
         if ($item['type'] === 'grid') {
            // Handle columns
            if (isset($item['columns'])) {
               if (is_array($item['columns'])) {
                  $fieldData['columns'] = json_encode($item['columns']);
               } else {
                  $fieldData['columns'] = $item['columns'];
               }
            }

            // Handle schema
            if (isset($item['schema']) && is_array($item['schema'])) {
               $fieldData['schema'] = json_encode($item['schema'], JSON_PRETTY_PRINT);
            }
         }

         $repeaterData[] = $fieldData;
      }

      // Update form dengan data repeater baru, tetapi pertahankan status checkbox
      $this->form->fill([
         'desainer_info_pengirim' => $repeaterData,
         'preview_json' => $jsonString,
         'preview_data' => $jsonString,
         'tampilkan_lanjutan' => $formData['tampilkan_lanjutan'] ?? false, // Pertahankan status checkbox
      ]);

      // Tampilkan notifikasi sukses
      Notification::make()
         ->title('Import Berhasil')
         ->body('Field berhasil diimpor dari JSON')
         ->success()
         ->send();
   }
}

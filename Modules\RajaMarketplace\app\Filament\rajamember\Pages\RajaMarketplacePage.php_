<?php

namespace Modules\RajaMarketplace\Filament\rajamember\Pages;

use Filament\Pages\Page;
use Modules\RajaMarketplace\Models\MpToko;
use Illuminate\Support\Facades\Auth;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;

class RajaMarketplacePage extends Page implements HasActions
{
    use InteractsWithActions;
    public static string $view = 'rajamarketplace::index';
    public static ?string $navigationLabel = 'Dashboard';
    public static ?string $slug = 'marketplace/dashboard2';
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $navigationGroup = 'Marketplace';
    public static ?string $navigationIcon = 'heroicon-c-home';

    public $tokoData = null;

    public function mount()
    {
        // Ambil data toko milik user yang sedang login, atau buat instance baru dengan data default
        $this->tokoData = MpToko::firstOrNew(
            ['user_id' => Auth::id()],
            [
                'nama' => 'nama toko',
                'alamat' => 'Alamat toko',
                'telpon' => '0000000',
                'jenis' => 'MARKETPLACE'
            ]
        );
    }



    protected function getHeaderActions(): array
    {
        return [
            Action::make('edit')
                ->label('Edit Toko')
                ->icon('heroicon-o-pencil-square')
                ->color('primary')
                ->url('/admin/marketplace/dashboard/edit-toko'),
        ];
    }


}

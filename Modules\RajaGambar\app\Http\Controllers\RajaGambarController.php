<?php

namespace Modules\RajaGambar\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RajaGambar\Services\RajaGambarService;
use Illuminate\Support\Facades\Validator;
use Exception;

class RajaGambarController extends Controller
{
    protected $rajaGambarService;

    public function __construct(RajaGambarService $rajaGambarService)
    {
        $this->rajaGambarService = $rajaGambarService;
    }

    /**
     * Resize gambar dengan width dan height
     */
    public function resize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240', // Max 10MB
            'width' => 'required|integer|min:1|max:4000',
            'height' => 'required|integer|min:1|max:4000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();

            $result = $this->rajaGambarService
                ->load($imagePath)
                ->resize($request->width, $request->height)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil diresize',
                'data' => [
                    'url' => $result,
                    'width' => $request->width,
                    'height' => $request->height
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set width dengan mempertahankan aspect ratio
     */
    public function width(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'required|integer|min:1|max:4000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();

            $result = $this->rajaGambarService
                ->load($imagePath)
                ->width($request->width)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Width gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'width' => $request->width
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set height dengan mempertahankan aspect ratio
     */
    public function height(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'height' => 'required|integer|min:1|max:4000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();

            $result = $this->rajaGambarService
                ->load($imagePath)
                ->height($request->height)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Height gambar berhasil diubah',
                'data' => [
                    'url' => $result,
                    'height' => $request->height
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Fit gambar dalam dimensi tertentu
     */
    public function fit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'required|integer|min:1|max:4000',
            'height' => 'required|integer|min:1|max:4000',
            'fit_method' => 'nullable|string|in:contain,max,fill,stretch,crop',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();

            $result = $this->rajaGambarService
                ->load($imagePath)
                ->fit($request->width, $request->height, $request->fit_method)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-fit',
                'data' => [
                    'url' => $result,
                    'width' => $request->width,
                    'height' => $request->height,
                    'fit_method' => $request->fit_method ?? 'contain'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Crop gambar
     */
    public function crop(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|image|max:10240',
            'width' => 'required|integer|min:1|max:4000',
            'height' => 'required|integer|min:1|max:4000',
            'crop_method' => 'nullable|string|in:crop-top-left,crop-top,crop-top-right,crop-left,crop-center,crop-right,crop-bottom-left,crop-bottom,crop-bottom-right',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $imagePath = $request->file('image')->getPathname();

            $result = $this->rajaGambarService
                ->load($imagePath)
                ->crop($request->width, $request->height, $request->crop_method)
                ->optimize()
                ->save();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil di-crop',
                'data' => [
                    'url' => $result,
                    'width' => $request->width,
                    'height' => $request->height,
                    'crop_method' => $request->crop_method ?? 'crop-center'
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memproses gambar: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace Modules\RajaMember\Filament\rajamember\Widgets;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use App\Models\User;

class UserInfoWidget extends Widget
{
   
    use HasWidgetShield;
    protected static string $view = 'rajamember::filament.widgets.user-info-widget';
    
    protected static ?int $sort = 1;
    
    protected int | string | array $columnSpan = 'full';
    
    public function getData(): array
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (!$user) {
            return [
                'user' => null,
                'info' => [
                    'name' => 'Tidak diketahui',
                    'email' => 'Tidak diketahui',
                    'username' => 'Belum diset',
                    'role' => 'Tidak ada role',
                    'email_verified' => 'Belum Terverifikasi',
                    'avatar_url' => null,
                ]
            ];
        }

        // Ambil role user (menggunakan Spatie Permission jika ada)
        $userRole = 'Member';
        if (method_exists($user, 'getRoleNames')) {
            $roles = $user->getRoleNames();
            $userRole = $roles->isNotEmpty() ? $roles->first() : '-';
        } elseif (method_exists($user, 'roles')) {
            $roles = $user->roles;
            $userRole = $roles->isNotEmpty() ? $roles->first()->name : '-';
        }

        return [
            'user' => $user,
            'info' => [
                'name' => $user->name ?? 'Nama tidak tersedia',
                'email' => $user->email ?? 'Email tidak tersedia',
                'username' => !empty($user->username) ? $user->username : 'Belum diset',
                'role' => $userRole,
                'email_verified' => $user->email_verified_at ? 'Terverifikasi' : 'Belum Terverifikasi',
                'avatar_url' => !empty($user->avatar_url) ? $user->avatar_url : null,
            ]
        ];
    }
}

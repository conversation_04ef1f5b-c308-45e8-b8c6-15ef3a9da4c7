<?php

namespace App\Filament\Resources\PermissionResource\Pages;

use App\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Permission;

class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    /**
     * Menambahkan tab di atas tabel agar user dapat memfilter
     * permission berdasarkan jenisnya: Resources, Pages, Widgets,
     * serta Custom Permissions.
     */
    public function getTabs(): array
    {
        // Prefix bawaan Filament Shield untuk permission resource.
        $resourcePrefixes = config('filament-shield.permission_prefixes.resource', [
            'view', 'view_any', 'create', 'update', 'delete', 'delete_any',
        ]);

        /* ---------------------------------------------------------- */
        /*  Closure helper untuk masing-masing kondisi tipe permission */
        /* ---------------------------------------------------------- */

        $resourceCondition = function (Builder $query) use ($resourcePrefixes): Builder {
            return $query->where(function (Builder $q) use ($resourcePrefixes) {
                foreach ($resourcePrefixes as $prefix) {
                    $q->orWhere('name', 'like', $prefix . '_%');
                }

                // Pastikan bukan page_ atau widget_
                $q->where('name', 'not like', 'page_%')
                  ->where('name', 'not like', 'widget_%');
            });
        };

        $pageCondition = fn (Builder $query): Builder => $query->where('name', 'like', 'page_%');

        $widgetCondition = fn (Builder $query): Builder => $query->where('name', 'like', 'widget_%');

        $customCondition = function (Builder $query) use ($resourcePrefixes): Builder {
            $query->where('name', 'not like', 'page_%')
                  ->where('name', 'not like', 'widget_%');

            foreach ($resourcePrefixes as $prefix) {
                $query->where('name', 'not like', $prefix . '_%');
            }

            return $query;
        };

        /* ------------------------------ */
        /*  Hitung badge setiap kategori  */
        /* ------------------------------ */
        $resourceCount = Permission::query()->tap($resourceCondition)->count();
        $pageCount     = Permission::query()->tap($pageCondition)->count();
        $widgetCount   = Permission::query()->tap($widgetCondition)->count();
        $customCount   = Permission::query()->tap($customCondition)->count();

        return [
            'resources' => Tab::make('Resources')
                ->badge($resourceCount)
                ->modifyQueryUsing($resourceCondition),

            'pages' => Tab::make('Pages')
                ->badge($pageCount)
                ->modifyQueryUsing($pageCondition),

            'widgets' => Tab::make('Widgets')
                ->badge($widgetCount)
                ->modifyQueryUsing($widgetCondition),

            'custom' => Tab::make('Custom Permissions')
                ->badge($customCount)
                ->modifyQueryUsing($customCondition),
        ];
    }
} 
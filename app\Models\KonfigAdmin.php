<?php

namespace App\Models;

use App\Models\Konfig;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Str;
use App\Traits\PakaiJcol;
/**
 * @mixin IdeHelperKonfigAdmin
 */
class KonfigAdmin extends Konfig
{ 

    use PakaiJcol;
     
      protected $casts = [
        'json' => 'array',
       
    ];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'ADMIN');
        });

        static::creating(function ($model) {
            $model->jenis = 'ADMIN';
        });

        static::updating(function ($model) {
            $model->jenis = 'ADMIN';
        });
    }



}
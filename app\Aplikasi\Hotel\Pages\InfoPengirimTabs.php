<?php

namespace App\Aplikasi\Hotel\Pages;

use App\Aplikasi\Hotel\Resources\KamarResource;
use App\Aplikasi\Hotel\Resources\MetodePembayaranResource;
use App\Aplikasi\Hotel\Resources\ReservasiResource;

use App\Aplikasi\Hotel\Widgets\PenyairNavWidget;

use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Builder;
use Filament\Forms\Components\Builder\Block;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action as PageAction;
use Filament\Notifications\Notification;
use Filament\Support\Enums\ActionSize;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class InfoPengirimTabs extends Page
{
    // Tentukan view yang akan digunakan
    protected static string $view = 'hotel::pages.info-pengirim';

    // Properti untuk menyimpan label navigasi
    protected static ?string $navigationLabel = 'Desainer Info Pengirim';

    // Properti untuk menyimpan ikon navigasi
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    // Properti untuk menyimpan grup navigasi
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $slug = 'pengaturan/info-pengirim';

    protected static bool $shouldRegisterNavigation = false;

    // Properti untuk state konfigurasi
    public array $data = [];

    // Path file konfigurasi
    protected string $configFilePath = '';

    // Struktur field default
    protected array $defaultFieldStructure = [
        'nama' => '',
        'jenis' => 'textinput',
        'hint' => '',
        'insert' => true,
    ];

    // Jenis-jenis field yang didukung
    protected array $jenisFieldOptions = [
        'textinput' => 'Text Input',
        'textarea' => 'Text Area',
        'radio' => 'Radio Button',
        'select' => 'Select Dropdown',
        'view' => 'Custom View',
        'upload' => 'Upload File',
    ];

    public function getHeaderWidgets(): array
    {
        return [
            PenyairNavWidget::make('pengaturan'),
        ];
    }

    public function getHeaderWidgetsColumns(): int|array
    {
        return 1;
    }
    // Tentukan properti form
    public ?array $formState = null;

    public function mount(): void
    {
        // Set path file konfigurasi
        $this->configFilePath = app_path('Aplikasi/Hotel/config/info_pengirim.php');

        // Muat konfigurasi ke dalam properti data
        $this->loadConfig();
    }

    /**
     * Memuat konfigurasi dari file
     */
    protected function loadConfig(): void
    {
        // Ambil konfigurasi dari file
        $config = config('hotel.info_pengirim', []);

        // Simpan ke properti data untuk digunakan di form
        $this->data = $config;

        // Siapkan untuk form
        $this->prepareFormState();
    }



    protected function prepareFormState(): void
    {
        $formState = [
            'fields' => [],
        ];

        // Struktur baru dengan fields dikelompokkan per metode
        foreach ($this->data as $metodeName => $fields) {
            $formState['fields'][$metodeName] = $fields;
        }

        $this->formState = $formState;
    }

    /**
     * Mendefinisikan form
     */
    /**
     * Mendefinisikan form
     */
    /**
     * Mendefinisikan form
     */
    /**
     * Mendefinisikan form
     */
    public function form(Form $form): Form
    {
        // Buat tab untuk setiap metode pembayaran
        $metodeTabs = [];

        // Tab untuk menambahkan metode baru
        $metodeTabs[] = Tab::make('Tambah Metode')
            ->icon('heroicon-o-plus-circle')
            ->schema([
                Grid::make(1)
                    ->schema([
                        // Form untuk menambah metode baru
                        Section::make('Tambah Metode Pembayaran Baru')
                            ->description('Halaman ini untuk mengatur jenis pembayaran, dan informasi pengirim')
                            ->schema([
                                // Tombol untuk menambah metode baru
                                \Filament\Forms\Components\Actions::make([
                                    \Filament\Forms\Components\Actions\Action::make('tambahMetodeBaru')
                                        ->label('Tambah Metode Baru')
                                        ->icon('heroicon-o-plus')
                                        ->color('success')
                                        ->requiresConfirmation()
                                        ->modalHeading('Tambah Metode Pembayaran')
                                        ->modalDescription('Pastikan nama metode unik dan representatif')
                                        ->modalSubmitActionLabel('Tambah Metode')
                                        ->form([
                                            TextInput::make('nama_metode')
                                                ->label('Nama Metode')
                                                ->required()
                                                ->placeholder('Contoh: kartu_kredit')
                                                ->helperText('Gunakan nama yang unik. Spasi akan otomatis diganti dengan underscore (_)')
                                                ->afterStateUpdated(function (string $state, $set) {
                                                    // Format nama metode secara otomatis
                                                    $formattedState = Str::slug($state, '_');

                                                    // Update state jika berbeda
                                                    if ($formattedState !== $state) {
                                                        $set('nama_metode', $formattedState);
                                                    }
                                                })
                                        ])
                                        ->action(function (array $data) {
                                            // Tampilkan log untuk debugging
                                            \Illuminate\Support\Facades\Log::info('Tambah metode data:', $data);

                                            // Validasi input
                                            if (empty($data['nama_metode'])) {
                                                Notification::make()
                                                    ->title('Nama metode tidak boleh kosong')
                                                    ->danger()
                                                    ->send();
                                                return;
                                            }

                                            // Bersihkan nama metode (slug)
                                            $metodeName = Str::slug($data['nama_metode'], '_');

                                            // Validasi - metode kosong
                                            if (empty($metodeName)) {
                                                Notification::make()
                                                    ->title('Nama metode tidak valid')
                                                    ->body('Harap gunakan alfanumerik dan underscore')
                                                    ->danger()
                                                    ->send();
                                                return;
                                            }

                                            // Validasi - apakah metode sudah ada (case insensitive)
                                            foreach (array_keys($this->data) as $existingMethod) {
                                                if (strtolower($existingMethod) === strtolower($metodeName)) {
                                                    Notification::make()
                                                        ->title("Metode '{$metodeName}' sudah ada")
                                                        ->body('Silakan gunakan nama metode lain yang unik')
                                                        ->danger()
                                                        ->send();
                                                    return;
                                                }
                                            }

                                            // Tambahkan metode baru dengan array kosong
                                            $this->data[$metodeName] = [];

                                            // Siapkan state form
                                            $this->prepareFormState();

                                            // Notifikasi
                                            Notification::make()
                                                ->title("Metode '{$metodeName}' berhasil ditambahkan")
                                                ->body('Silakan pilih tab baru untuk mengatur fieldnya')
                                                ->success()
                                                ->send();
                                        })
                                ]),

                                Placeholder::make('petunjuk_tambah')
                                    ->label('Petunjuk')
                                    ->content('Klik tombol "Tambah Metode Baru" di atas untuk menambahkan metode pembayaran baru. Nama metode akan otomatis dikonversi menjadi slug (huruf kecil dan underscore).')
                                    ->columnSpan(1),
                            ])
                    ]),
            ]);

        // Buat tab untuk setiap metode pembayaran yang ada
        foreach ($this->data as $metodeName => $fields) {
            $metodeTabs[] = Tab::make($metodeName)
                ->icon('heroicon-o-credit-card')
                ->schema([
                    Grid::make(1)
                        ->schema([
                            Section::make("Konfigurasi Metode {$metodeName}")
                                ->description("Atur field-field untuk metode pembayaran {$metodeName}")
                                ->schema([
                                    Repeater::make("fields.{$metodeName}")
                                        ->label('Field Setting')
                                        ->schema([
                                            TextInput::make('nama')
                                                ->label('Nama Field')
                                                ->required()
                                                ->maxLength(50)
                                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Spasi akan otomatis diganti menjadi underscore (_)')

                                                ->afterStateUpdated(function (string $state, $set) {
                                                    // Ganti spasi dengan underscore
                                                    $formattedState = str_replace(' ', '_', $state);

                                                    // Pastikan hanya menggunakan alfanumerik dan underscore
                                                    $formattedState = preg_replace('/[^a-zA-Z0-9_]/', '', $formattedState);

                                                    // Ubah ke lowercase untuk konsistensi
                                                    $formattedState = strtolower($formattedState);

                                                    // Update state jika berbeda
                                                    if ($formattedState !== $state) {
                                                        $set('nama', $formattedState);
                                                    }
                                                })
                                                ->columnSpan(1),

                                            Select::make('jenis')
                                                ->label('Jenis Field')
                                                ->options($this->jenisFieldOptions)
                                                ->required()
                                                ->reactive()
                                                ->columnSpan(1),

                                            Textarea::make('hint')
                                                ->label('petujuk')
                                                ->maxLength(100)
                                                ->columnSpan(1),

                                            TextInput::make('prefix')
                                                ->label('Prefix')
                                                ->visible(fn ($get) => $get('jenis') === 'textinput')
                                                ->columnSpan(1),

                                            TextInput::make('prefixicon')
                                                ->label('Ikon prefix')
                                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Contoh: heroicon-m-user, heroicon-o-credit-card')
                                                ->visible(fn ($get) => $get('jenis') === 'textinput')
                                                ->columnSpan(1),


                                            TextInput::make('suffix')
                                                ->label('Suffix')
                                                ->visible(fn ($get) => $get('jenis') === 'textinput')
                                                ->columnSpan(1),

                                            TextInput::make('suffixicon')
                                                ->label('Ikon suffix')

                                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Contoh: heroicon-m-user, heroicon-o-credit-card')
                                                ->visible(fn ($get) => $get('jenis') === 'select')
                                                ->columnSpan(1),



                                            TextInput::make('mask')
                                                ->label('Input Mask')
                                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Contoh: 99/99 untuk tanggal')
                                                ->visible(fn ($get) => $get('jenis') === 'textinput')
                                                ->columnSpan(1),

                                            Textarea::make('options')
                                                ->label('Opsi (dipisahkan koma)')
                                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Contoh: BCA,BNI,Mandiri,BRI')
                                                ->visible(fn ($get) => in_array($get('jenis'), ['radio', 'select']))
                                                ->columnSpan(2),


                                            TextInput::make('view')
                                                ->label('View Path')
                                                ->visible(fn ($get) => $get('jenis') === 'view')
                                                ->placeholder('hotel::pembayaran.custom')
                                                ->columnSpan(2),

                                            TextInput::make('location')
                                                ->label('Upload Location')
                                                ->visible(fn ($get) => $get('jenis') === 'upload')
                                                ->placeholder('app/Aplikasi/Hotel/uploads')
                                                ->columnSpan(2),


                                            Textarea::make('default_value')
                                                ->label('isi Default')
                                                ->visible(fn ($get) => in_array($get('jenis'), ['textinput', 'textarea', 'view']))
                                                ->columnSpan(1),

                                            Checkbox::make('numeric')
                                                ->label('Angka saja ?')
                                                ->visible(fn ($get) => $get('jenis') === 'textinput')
                                                ->columnSpan(1),

                                            Checkbox::make('disabled')
                                                ->label('Tidak bisa di edit')
                                                ->visible(fn ($get) => in_array($get('jenis'), ['textinput', 'textarea']))
                                                ->columnSpan(1),

                                            Toggle::make('insert')
                                                ->label('Simpan di Database')
                                                ->default(true)
                                                ->columnSpan(1),
                                        ])
                                        ->defaultItems(0)
                                        ->reorderable(true)
                                        ->collapsible()
                                        ->itemLabel(fn (array $state): ?string => $state['nama'] ?? null)
                                        ->addActionLabel('+ Tambah Field')
                                        ->columnSpan(2)->columns(4),
                                ]),

                            // Tombol hapus metode pembayaran
                            \Filament\Forms\Components\Actions::make([
                                \Filament\Forms\Components\Actions\Action::make('hapusMetode_'.$metodeName)  // Nama unik per metode
                                    ->label('Hapus Metode Pembayaran')
                                    ->icon('heroicon-o-trash')
                                    ->color('danger')
                                    ->requiresConfirmation()
                                    ->modalHeading('Hapus Metode Pembayaran')
                                    ->modalDescription("Yakin ingin menghapus metode pembayaran '{$metodeName}'? Semua field yang telah dikonfigurasi akan hilang.")
                                    ->modalSubmitActionLabel('Ya, Hapus')
                                    ->action(function () use ($metodeName) {
                                        // Hapus metode dari data
                                        unset($this->data[$metodeName]);

                                        // Siapkan state form
                                        $this->prepareFormState();

                                        // Notifikasi
                                        Notification::make()
                                            ->title("Metode '{$metodeName}' berhasil dihapus")
                                            ->success()
                                            ->send();
                                    })
                            ]),
                        ]),
                ]);
        }

        // Tab bantuan
        $metodeTabs[] = Tab::make('Bantuan')
            ->icon('heroicon-o-information-circle')
            ->schema([
                Section::make('Petunjuk Penggunaan')
                    ->schema([
                        Placeholder::make('help_general')
                            ->label('Informasi Umum')
                            ->content('Berikut adalah petunjuk penggunaan konfigurasi info pengirim'),

                        Grid::make(1)
                            ->schema([
                                Placeholder::make('help_step1')
                                    ->label('1. Tambah Metode Pembayaran')
                                    ->content('Buka tab "Tambah Metode" dan klik tombol "Tambah Metode Baru". Masukkan nama metode pada form yang muncul.'),

                                Placeholder::make('help_step2')
                                    ->label('2. Konfigurasi Field')
                                    ->content('Buka tab metode pembayaran yang ingin dikonfigurasi, kemudian klik "+ Tambah Field" untuk menambahkan field baru.'),

                                Placeholder::make('help_step3')
                                    ->label('3. Atur Properti Field')
                                    ->content('Isi semua properti field sesuai kebutuhan. Properti yang tersedia akan bervariasi tergantung jenis field yang dipilih.'),

                                Placeholder::make('help_step4')
                                    ->label('4. Simpan Perubahan')
                                    ->content('Setelah selesai mengatur semua field, klik tombol "Simpan Konfigurasi" untuk menyimpan perubahan.'),

                                Placeholder::make('help_fields')
                                    ->label('Jenis Field yang Tersedia')
                                    ->content(function () {
                                        $output = '<ul class="list-disc pl-4">';
                                        foreach ($this->jenisFieldOptions as $value => $label) {
                                            $output .= "<li><strong>{$label}</strong> ({$value})</li>";
                                        }
                                        $output .= '</ul>';
                                        return $output;
                                    }),
                            ]),
                    ]),
            ]);

        return $form
            ->schema([
                Tabs::make('InfoPengirimTabs')
                    ->tabs($metodeTabs)
                    ->persistTab()
                    ->id('info-pengirim-tabs')
                    ->columnSpan(2),
            ])
            ->statePath('formState');
    }

    /**
     * Mendapatkan header actions untuk page
     */
    protected function getHeaderActions(): array
    {
        return [
            PageAction::make('simpan')
                ->label('Simpan Konfigurasi')
                ->color('primary')
                ->size(ActionSize::Large)
                ->icon('heroicon-o-check')
                ->action('simpanKonfigurasi'),

            PageAction::make('reset')
                ->label('Reset Konfigurasi')
                ->color('danger')
                ->size(ActionSize::Large)
                ->icon('heroicon-o-arrow-path')
                ->requiresConfirmation()
                ->modalHeading('Reset Konfigurasi')
                ->modalDescription('Yakin ingin mengembalikan konfigurasi ke pengaturan awal? Semua perubahan akan hilang.')
                ->modalSubmitActionLabel('Ya, Reset')
                ->action('resetKonfigurasi'),
        ];
    }

    /**
     * Menyimpan konfigurasi ke file
     */
    public function simpanKonfigurasi(): void
    {
        // Pastikan configFilePath telah diinisialisasi
        if (empty($this->configFilePath)) {
            $this->configFilePath = app_path('Aplikasi/Hotel/config/info_pengirim.php');
        }

        // Validasi terlebih dahulu
        $this->validate();

        // Transformasi data dari form ke format yang dibutuhkan untuk file konfigurasi
        $configData = [];

        // Dengan struktur baru, fields sudah dikelompokkan per metode
        if (isset($this->formState['fields'])) {
            foreach ($this->formState['fields'] as $metodeName => $fields) {
                // Reset key array agar berurutan numerik (0, 1, 2, ...) dan bukan UUID
                $cleanFields = [];

                foreach ($fields as $field) {
                    // Bersihkan field dari nilai yang tidak diperlukan
                    $cleanField = [];

                    // Properti yang selalu disimpan jika ada
                    $requiredProps = ['nama', 'jenis', 'hint', 'options', 'prefix', 'prefixicon',
                        'suffixicon', 'mask', 'view', 'location', 'default_value'];

                    // Simpan properti yang wajib dan memiliki nilai
                    foreach ($requiredProps as $prop) {
                        if (isset($field[$prop]) && $field[$prop] !== null && $field[$prop] !== '') {
                            $cleanField[$prop] = $field[$prop];
                        }
                    }

                    // Properti boolean yang hanya disimpan jika true
                    $booleanProps = ['insert', 'numeric', 'disabled'];
                    foreach ($booleanProps as $prop) {
                        if (isset($field[$prop]) && $field[$prop] === true) {
                            $cleanField[$prop] = true;
                        }
                    }

                    // Pastikan nama dan jenis selalu ada
                    if (! empty($cleanField['nama']) && ! empty($cleanField['jenis'])) {
                        $cleanFields[] = $cleanField;
                    }
                }

                $configData[$metodeName] = $cleanFields;
            }
        }

        // Generate konten PHP untuk file konfigurasi dengan format yang lebih rapi
        $content = "<?php\nreturn ".$this->formatArrayForExport($configData).";\n";

        // Simpan ke file
        File::put($this->configFilePath, $content);

        // Update konfigurasi di cache
        config(['hotel.info_pengirim' => $configData]);

        // Muat ulang data
        $this->data = $configData;

        // Notifikasi sukses
        Notification::make()
            ->title('Konfigurasi Berhasil Disimpan')
            ->success()
            ->send();
    }

    /**
     * Format array untuk export ke PHP dengan tampilan yang lebih rapi
     * 
     * @param array $array Array yang akan diformat
     * @param int $indent Level indentasi
     * @return string
     */
    protected function formatArrayForExport(array $array, int $indent = 0): string
    {
        $result = "[\n";
        $indentStr = str_repeat(' ', ($indent + 1) * 4);

        foreach ($array as $key => $value) {
            $result .= $indentStr;

            // Cetak key jika bukan numeric index berurutan
            if (! is_int($key)) {
                $result .= "\"$key\" => ";
            }

            // Format value sesuai tipenya
            if (is_array($value)) {
                $result .= $this->formatArrayForExport($value, $indent + 1);
            } elseif (is_bool($value)) {
                $result .= $value ? 'true' : 'false';
            } elseif (is_null($value)) {
                $result .= 'null';
            } elseif (is_int($value) || is_float($value)) {
                $result .= $value;
            } else {
                $result .= "\"".addslashes($value)."\"";
            }

            $result .= ",\n";
        }

        $result .= str_repeat(' ', $indent * 4)."]";
        return $result;
    }

    /**
     * Mengekspor variabel PHP ke string (Metode lama)
     * @deprecated Gunakan formatArrayForExport sebagai gantinya
     */
    protected function varExport($var, $indent = false)
    {
        switch (gettype($var)) {
            case 'string':
                return '"'.addcslashes($var, "\\\$\"\r\n\t\v\f").'"';
            case 'array':
                $indexed = array_keys($var) === range(0, count($var) - 1);
                $r = [];
                foreach ($var as $key => $value) {
                    $r[] = ($indent ? '    ' : '')
                        .($indexed ? '' : $this->varExport($key).' => ')
                        .$this->varExport($value, true);
                }
                return "[\n".implode(",\n", $r)."\n".($indent ? '' : '    ').']';
            case 'boolean':
                return $var ? 'true' : 'false';
            case 'NULL':
                return 'null';
            case 'integer':
            case 'double':
                return $var;
            default:
                return var_export($var, true);
        }
    }

    /**
     * Reset konfigurasi ke default
     */
    public function resetKonfigurasi(): void
    {
        // Pastikan configFilePath telah diinisialisasi
        if (empty($this->configFilePath)) {
            $this->configFilePath = app_path('Aplikasi/Hotel/config/info_pengirim.php');
        }

        // Path file reset (konfigurasi default)
        $resetFilePath = app_path('Aplikasi/Hotel/config/info_pengirim_reset.php');

        // Periksa apakah file reset ada
        if (! File::exists($resetFilePath)) {
            // Jika tidak ada, beri notifikasi error
            Notification::make()
                ->title('File Reset Tidak Ditemukan')
                ->body('File konfigurasi default tidak ditemukan di path: '.$resetFilePath)
                ->danger()
                ->send();
            return;
        }

        // Salin isi file reset ke file konfigurasi aktif
        File::copy($resetFilePath, $this->configFilePath);

        // Muat ulang konfigurasi
        $this->loadConfig();

        // Notifikasi sukses
        Notification::make()
            ->title('Konfigurasi Berhasil Direset')
            ->body('Konfigurasi telah dikembalikan ke pengaturan default.')
            ->success()
            ->send();
    }
}
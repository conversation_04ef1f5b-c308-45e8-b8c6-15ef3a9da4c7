<x-filament-panels::page>
    {{-- Form Filter --}}
    <div class="mb-6">
        <x-filament::section>
            <x-slot name="heading">Filter Laporan</x-slot>
            <x-slot name="description">Pilih periode dan jenis laporan yang ingin ditampilkan</x-slot>
            
            {{ $this->form }}

            <x-slot name="footer">
                <div class="flex justify-end gap-3">
                    <x-filament::button
                        wire:click="filter"
                        color="primary"
                        icon="heroicon-o-funnel"
                    >
                        Terapkan Filter
                    </x-filament::button>
                </div>
            </x-slot>
        </x-filament::section>
    </div>
    
    {{-- Tam<PERSON>lkan Infolist atau Tabel berdasarkan jenis laporan --}}
    @if($jenis === 'ringkasan')
        <div class="space-y-6">
            {{-- Grafik Pendapatan Bulanan --}}
            <x-filament::section>
                <x-slot name="heading">Grafik Pendapatan 6 Bulan Terakhir</x-slot>
                
                <div class="h-80">
                    <canvas id="pendapatanChart"></canvas>
                </div>
                
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const ctx = document.getElementById('pendapatanChart').getContext('2d');
                        
                        // Data dari controller
                        const pendapatanData = @json($data['pendapatan']['pendapatan_bulanan'] ?? []);
                        
                        // Persiapkan data untuk chart
                        const labels = pendapatanData.map(item => item.bulan);
                        const values = pendapatanData.map(item => item.pendapatan);
                        
                        // Buat chart
                        const pendapatanChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'Pendapatan (Rp)',
                                    data: values,
                                    backgroundColor: '#0284c7',
                                    borderColor: '#0369a1',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            callback: function(value) {
                                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                                            }
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(context.raw);
                                            }
                                        }
                                    }
                                }
                            }
                        });
                        
                        // Pastikan chart diperbaharui saat halaman di-livewire reload
                        Livewire.hook('message.processed', (message, component) => {
                            if (component.fingerprint.name === 'laporan') {
                                pendapatanChart.data.labels = @json($data['pendapatan']['pendapatan_bulanan'] ?? []).map(item => item.bulan);
                                pendapatanChart.data.datasets[0].data = @json($data['pendapatan']['pendapatan_bulanan'] ?? []).map(item => item.pendapatan);
                                pendapatanChart.update();
                            }
                        });
                    });
                </script>
            </x-filament::section>
            
            {{-- Ringkasan Laporan dengan StaticInfolist (tidak memerlukan state atau record dari Filament) --}}
            {{-- PERBAIKAN: Buat InfoList secara manual di sini, bukan pakai $this->infolist --}}
            <x-filament::section>
                <x-slot name="heading">Ringkasan Pendapatan</x-slot>
                <x-slot name="headerIcon">heroicon-o-currency-rupiah</x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Pendapatan</p>
                        <p class="text-2xl font-bold mt-1">
                            Rp {{ number_format($data['pendapatan']['total_pendapatan'] ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pendapatan Bulan Ini</p>
                        <p class="text-2xl font-bold mt-1">
                            Rp {{ number_format($data['pendapatan']['pendapatan_bulan_ini'] ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pendapatan Hari Ini</p>
                        <p class="text-2xl font-bold mt-1">
                            Rp {{ number_format($data['pendapatan']['pendapatan_hari_ini'] ?? 0, 0, ',', '.') }}
                        </p>
                    </div>
                </div>
            </x-filament::section>
            
            <x-filament::section>
                <x-slot name="heading">Ringkasan Reservasi</x-slot>
                <x-slot name="headerIcon">heroicon-o-calendar</x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Reservasi</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['reservasi']['total_reservasi'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Check-in Hari Ini</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['reservasi']['check_in_hari_ini'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Check-out Hari Ini</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['reservasi']['check_out_hari_ini'] ?? 0 }}
                        </p>
                    </div>
                </div>
            </x-filament::section>
            
            <x-filament::section>
                <x-slot name="heading">Statistik Kamar</x-slot>
                <x-slot name="headerIcon">heroicon-o-home-modern</x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Kamar</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['kamar']['total_kamar'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Kamar Tersedia</p>
                        <p class="text-2xl font-bold mt-1 text-success-600 dark:text-success-400">
                            {{ $data['kamar']['kamar_tersedia'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Persentase Okupansi</p>
                        <p class="text-2xl font-bold mt-1 text-warning-600 dark:text-warning-400">
                            {{ $data['kamar']['persentase_okupansi'] ?? 0 }}%
                        </p>
                    </div>
                </div>
            </x-filament::section>
            
            <x-filament::section>
                <x-slot name="heading">Data Tamu</x-slot>
                <x-slot name="headerIcon">heroicon-o-user-group</x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Tamu</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['tamu']['total_tamu'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Tamu Baru</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['tamu']['tamu_baru'] ?? 0 }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Rata-rata Menginap</p>
                        <p class="text-2xl font-bold mt-1">
                            {{ $data['tamu']['rata_rata_menginap'] ?? 0 }} hari
                        </p>
                    </div>
                </div>
            </x-filament::section>
        </div>
    @elseif($jenis === 'pendapatan')
        <div class="space-y-6">
            <x-filament::section>
                <x-slot name="heading">Laporan Pendapatan</x-slot>
                <x-slot name="description">
                    Periode: {{ \Carbon\Carbon::parse($tanggalMulai)->format('d M Y') }} - {{ \Carbon\Carbon::parse($tanggalSelesai)->format('d M Y') }}
                </x-slot>
                
                {{-- Tampilkan statistik pendapatan --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                          
                            <span class="text-sm font-medium">Total Pendapatan</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            Rp {{ number_format($data['pendapatan']['total_pendapatan'] ?? 0, 0, ',', '.') }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-calendar class="w-5 h-5 text-success-500" />
                            <span class="text-sm font-medium">Pendapatan Bulan Ini</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            Rp {{ number_format($data['pendapatan']['pendapatan_bulan_ini'] ?? 0, 0, ',', '.') }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-clock class="w-5 h-5 text-warning-500" />
                            <span class="text-sm font-medium">Pendapatan Hari Ini</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            Rp {{ number_format($data['pendapatan']['pendapatan_hari_ini'] ?? 0, 0, ',', '.') }}
                        </div>
                    </x-filament::card>
                </div>
                
                {{-- Tabel Pendapatan --}}
                {{ $this->table }}
            </x-filament::section>
        </div>
    @elseif($jenis === 'reservasi')
        <div class="space-y-6">
            <x-filament::section>
                <x-slot name="heading">Laporan Reservasi</x-slot>
                <x-slot name="description">
                    Periode: {{ \Carbon\Carbon::parse($tanggalMulai)->format('d M Y') }} - {{ \Carbon\Carbon::parse($tanggalSelesai)->format('d M Y') }}
                </x-slot>
                
                {{-- Tampilkan statistik reservasi --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-clipboard-document-list class="w-5 h-5 text-primary-500" />
                            <span class="text-sm font-medium">Total Reservasi</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['reservasi']['total_reservasi'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-arrow-right-circle class="w-5 h-5 text-success-500" />
                            <span class="text-sm font-medium">Check-in Hari Ini</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['reservasi']['check_in_hari_ini'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-arrow-left-circle class="w-5 h-5 text-warning-500" />
                            <span class="text-sm font-medium">Check-out Hari Ini</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['reservasi']['check_out_hari_ini'] ?? 0 }}
                        </div>
                    </x-filament::card>
                </div>
                
                {{-- Tabel Reservasi --}}
                {{ $this->table }}
            </x-filament::section>
        </div>
    @elseif($jenis === 'kamar')
        <div class="space-y-6">
            <x-filament::section>
                <x-slot name="heading">Laporan Kamar</x-slot>
                <x-slot name="description">
                    Status dan ketersediaan kamar saat ini
                </x-slot>
                
                {{-- Tampilkan statistik kamar --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-home-modern class="w-5 h-5 text-primary-500" />
                            <span class="text-sm font-medium">Total Kamar</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['kamar']['total_kamar'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-check-circle class="w-5 h-5 text-success-500" />
                            <span class="text-sm font-medium">Kamar Tersedia</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['kamar']['kamar_tersedia'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-chart-bar class="w-5 h-5 text-warning-500" />
                            <span class="text-sm font-medium">Persentase Okupansi</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['kamar']['persentase_okupansi'] ?? 0 }}%
                        </div>
                    </x-filament::card>
                </div>
                
                {{-- Tabel Tipe Kamar --}}
                <div class="mb-6">
                    <h3 class="text-base font-medium mb-3">Distribusi Tipe Kamar</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tipe Kamar</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Jumlah</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($data['kamar']['tipe_kamar'] ?? [] as $tipe)
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm">{{ $tipe['nama'] }}</td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm">{{ $tipe['jumlah'] }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                
                {{-- Tabel Kamar --}}
                {{ $this->table }}
            </x-filament::section>
        </div>
    @elseif($jenis === 'tamu')
        <div class="space-y-6">
            <x-filament::section>
                <x-slot name="heading">Laporan Tamu</x-slot>
                <x-slot name="description">
                    Periode: {{ \Carbon\Carbon::parse($tanggalMulai)->format('d M Y') }} - {{ \Carbon\Carbon::parse($tanggalSelesai)->format('d M Y') }}
                </x-slot>
                
                {{-- Tampilkan statistik tamu --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-user-group class="w-5 h-5 text-primary-500" />
                            <span class="text-sm font-medium">Total Tamu</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['tamu']['total_tamu'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-user-plus class="w-5 h-5 text-success-500" />
                            <span class="text-sm font-medium">Tamu Baru</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['tamu']['tamu_baru'] ?? 0 }}
                        </div>
                    </x-filament::card>
                    
                    <x-filament::card>
                        <div class="flex items-center gap-2">
                            <x-heroicon-o-clock class="w-5 h-5 text-warning-500" />
                            <span class="text-sm font-medium">Rata-rata Menginap</span>
                        </div>
                        <div class="text-2xl font-bold mt-2">
                            {{ $data['tamu']['rata_rata_menginap'] ?? 0 }} hari
                        </div>
                    </x-filament::card>
                </div>
                
                {{-- Tabel Tamu --}}
                {{ $this->table }}
            </x-filament::section>
        </div>
    @endif
</x-filament-panels::page>
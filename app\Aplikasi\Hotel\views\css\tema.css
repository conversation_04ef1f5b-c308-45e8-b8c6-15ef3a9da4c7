/* 
   Tema Hotel - <PERSON><PERSON>, Enterprise, Profesional
   Aplikasi Perhotelan dengan FilamentPHP 
*/

/* Variabel warna utama - nuansa emas mewah */
:root {
    --primary-50: 253 242 230 !important; 
    --primary-100: 254 235 200 !important;
    --primary-200: 253 224 171 !important;
    --primary-300: 251 207 126 !important;
    --primary-400: 237 181 82 !important;
    --primary-500: 220 160 47 !important;
    --primary-600: 192 133 26 !important;
    --primary-700: 154 98 15 !important;
    --primary-800: 125 77 15 !important;
    --primary-900: 102 60 16 !important;
    --primary-950: 63 34 8 !important;
    
    /* <PERSON><PERSON> sekunder - biru elegan */
    --secondary-50: 236 244 254 !important;
    --secondary-100: 207 226 252 !important;
    --secondary-200: 165 199 247 !important;
    --secondary-300: 119 167 240 !important;
    --secondary-400: 80 134 230 !important;
    --secondary-500: 53 106 216 !important;
    --secondary-600: 40 82 186 !important;
    --secondary-700: 36 68 147 !important;
    --secondary-800: 31 53 112 !important;
    --secondary-900: 28 44 87 !important;
    --secondary-950: 17 27 54 !important;
    
    /* <PERSON>na a<PERSON>en - burgundy kee<PERSON>an */
    --accent-50: 246 235 237 !important;
    --accent-100: 235 207 214 !important;
    --accent-200: 218 168 181 !important;
    --accent-300: 196 126 144 !important;
    --accent-400: 171 85 109 !important;
    --accent-500: 143 53 83 !important;
    --accent-600: 119 37 67 !important;
    --accent-700: 90 27 50 !important;
    --accent-800: 70 22 40 !important;
    --accent-900: 54 18 32 !important;
    --accent-950: 33 10 19 !important;
    
    /* Warna netral - abu-abu hangat */
    --gray-50: 250 250 249 !important;
    --gray-100: 244 244 243 !important;
    --gray-200: 228 228 226 !important;
    --gray-300: 213 213 209 !important;
    --gray-400: 169 168 164 !important;
    --gray-500: 135 134 131 !important;
    --gray-600: 111 110 107 !important;
    --gray-700: 91 90 88 !important;
    --gray-800: 75 74 72 !important;
    --gray-900: 64 63 62 !important;
    --gray-950: 34 33 33 !important;
    
    /* Override warna baru untuk success, warning, danger */
    --success: 16 185 129 !important;
    --warning: 245 158 11 !important;
    --danger: 239 68 68 !important;
    --info: 59 130 246 !important;
}

/* Styling navigasi atas */
.fi-topbar {
    background-image: linear-gradient(to right, rgb(var(--primary-800)), rgb(var(--primary-900))) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.fi-topbar .fi-icon {
    color: rgb(var(--primary-50)) !important;
}

/* Pengaturan logo/branding */
.fi-logo {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
}

/* Navigasi grup */
.fi-navigation-item-group-label {
    color: rgb(var(--primary-300)) !important;
    font-weight: 600 !important;
    letter-spacing: 0.05em !important;
    text-transform: uppercase !important;
    font-size: 0.75rem !important;
}

/* Item navigasi */
.fi-navigation-item {
    margin-bottom: 0.25rem !important;
    border-radius: 0.375rem !important;
    transition: all 0.2s ease-in-out !important;
}

.fi-navigation-item-active {
    background-color: rgba(var(--primary-600), 0.15) !important;
    border-left: 3px solid rgb(var(--primary-400)) !important;
}

.fi-navigation-item:hover:not(.fi-navigation-item-active) {
    background-color: rgba(var(--primary-950), 0.3) !important;
}

.fi-navigation-item-icon {
    color: rgb(var(--primary-400)) !important;
}

.fi-navigation-item-active .fi-navigation-item-icon {
    color: rgb(var(--primary-300)) !important;
}

.fi-navigation-item-label {
    font-weight: 500 !important;
}

/* Kartu/Card dengan desain mewah */
.fi-card {
    border: none !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
    border-radius: 0.5rem !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.fi-card:hover {
    box-shadow: 0 6px 24px rgba(var(--primary-950), 0.12) !important;
    transform: translateY(-2px) !important;
}

.fi-card-header {
    border-bottom: 1px solid rgba(var(--primary-200), 0.5) !important;
    background-color: white !important;
    padding: 1rem 1.25rem !important;
}

.fi-card-heading {
    color: rgb(var(--primary-900)) !important;
    font-weight: 600 !important;
    font-size: 1.125rem !important;
}

.fi-card-content {
    background-color: white !important;
    padding: 1.25rem !important;
}

/* Tombol dengan gaya premium */
.fi-btn {
    font-weight: 500 !important;
    border-radius: 0.375rem !important;
    transition: all 0.2s ease !important;
}

.fi-btn-primary {
    background-color: rgb(var(--primary-600)) !important;
    border: 1px solid rgb(var(--primary-700)) !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(var(--primary-900), 0.2) !important;
}

.fi-btn-primary:hover {
    background-color: rgb(var(--primary-700)) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(var(--primary-900), 0.3) !important;
}

.fi-btn-secondary {
    background-color: rgb(var(--secondary-600)) !important;
    border: 1px solid rgb(var(--secondary-700)) !important;
    color: white !important;
}

.fi-btn-secondary:hover {
    background-color: rgb(var(--secondary-700)) !important;
    transform: translateY(-1px) !important;
}

/* Input forms */
.fi-input-wrp {
    transition: all 0.2s ease !important;
}

.fi-input {
    border-color: rgb(var(--gray-300)) !important;
    border-radius: 0.375rem !important;
    padding: 0.625rem 0.875rem !important;
}

.fi-input:focus {
    border-color: rgb(var(--primary-500)) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-500), 0.2) !important;
}

/* Form labels */
.fi-form-label {
    font-weight: 500 !important;
    color: rgb(var(--gray-700)) !important;
}

/* Tabel data dengan gaya enterprise */
.fi-ta-header-cell {
    background-color: rgb(var(--gray-100)) !important;
    color: rgb(var(--gray-800)) !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 0.75rem !important;
    letter-spacing: 0.025em !important;
    padding-top: 0.875rem !important;
    padding-bottom: 0.875rem !important;
}

.fi-ta-row {
    border-bottom: 1px solid rgb(var(--gray-200)) !important;
    transition: background-color 0.15s ease !important;
}

.fi-ta-row:hover {
    background-color: rgba(var(--primary-50), 0.5) !important;
}

/* Panel halaman utama */
.fi-main {
    background-color: #f8f9fa !important;
    position: relative !important;
}

.fi-main::before {
    content: "" !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 40% !important;
    height: 300px !important;
    background-image: linear-gradient(135deg, rgba(var(--primary-200), 0.15) 0%, rgba(var(--primary-400), 0.05) 100%) !important;
    z-index: 0 !important;
    pointer-events: none !important;
    border-bottom-left-radius: 50% 20% !important;
}

/* Badge styling */
.fi-badge {
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.025em !important;
    font-size: 0.75rem !important;
}

.fi-badge-success {
    background-color: rgb(var(--success)) !important;
    color: white !important;
}

.fi-badge-warning {
    background-color: rgb(var(--warning)) !important;
    color: white !important;
}

.fi-badge-danger {
    background-color: rgb(var(--danger)) !important;
    color: white !important;
}

/* Filter dan pencarian */
.fi-ta-search-box {
    border-radius: 0.375rem !important;
    border-color: rgb(var(--gray-300)) !important;
    transition: all 0.2s ease !important;
}

.fi-ta-search-box:focus-within {
    border-color: rgb(var(--primary-500)) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-500), 0.2) !important;
}

/* Modal dan dialog */
.fi-modal-window {
    border: none !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    overflow: hidden !important;
}

.fi-modal-header {
    border-bottom: 1px solid rgba(var(--gray-200), 1) !important;
    background-color: white !important;
}

.fi-modal-heading {
    color: rgb(var(--primary-900)) !important;
    font-weight: 600 !important;
}

.fi-modal-content {
    padding: 1.5rem !important;
}

.fi-modal-footer {
    background-color: rgb(var(--gray-50)) !important;
    border-top: 1px solid rgba(var(--gray-200), 1) !important;
    padding: 1rem 1.5rem !important;
}

/* Select dropdown */
.fi-select-trigger-input {
    border-color: rgb(var(--gray-300)) !important;
    border-radius: 0.375rem !important;
}

.fi-select-trigger-input:focus {
    border-color: rgb(var(--primary-500)) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-500), 0.2) !important;
}

/* Pagination */
.fi-pagination-item {
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.fi-pagination-item-active {
    background-color: rgb(var(--primary-600)) !important;
    color: white !important;
}

/* Notification styling */
.fi-notification {
    border: none !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.fi-notification-title {
    font-weight: 600 !important;
}

.fi-notification-close {
    color: rgb(var(--gray-500)) !important;
}

/* Widget dashboard */
.fi-widget {
    border-radius: 0.5rem !important;
    overflow: hidden !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

.fi-widget:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.fi-widget-header {
    border-bottom: 1px solid rgba(var(--primary-200), 0.5) !important;
    padding: 1rem !important;
}

.fi-widget-content {
    padding: 1.25rem !important;
}

/* Mode gelap */
.dark .fi-topbar {
    background-image: linear-gradient(to right, rgb(35, 25, 15), rgb(30, 20, 10)) !important;
}

.dark .fi-main {
    background-color: rgb(20, 20, 20) !important;
}

.dark .fi-card {
    background-color: rgb(30, 30, 30) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
}

.dark .fi-card-header {
    background-color: rgb(35, 35, 35) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.dark .fi-card-content {
    background-color: rgb(30, 30, 30) !important;
}

.dark .fi-ta-header-cell {
    background-color: rgb(35, 35, 35) !important;
    color: rgb(220, 220, 220) !important;
}

.dark .fi-ta-row {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.dark .fi-ta-row:hover {
    background-color: rgba(var(--primary-900), 0.3) !important;
}

.dark .fi-modal-window {
    background-color: rgb(30, 30, 30) !important;
}

.dark .fi-modal-header {
    background-color: rgb(35, 35, 35) !important;
}

.dark .fi-modal-footer {
    background-color: rgb(28, 28, 28) !important;
}

/* Elemen khusus hotel */
.hotel-room-status-available {
    background-color: rgba(var(--success), 0.1) !important;
    color: rgb(var(--success)) !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
}

.hotel-room-status-occupied {
    background-color: rgba(var(--danger), 0.1) !important;
    color: rgb(var(--danger)) !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
}

.hotel-room-status-maintenance {
    background-color: rgba(var(--warning), 0.1) !important;
    color: rgb(var(--warning)) !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.25rem !important;
    font-weight: 500 !important;
}

/* Timeline untuk reservasi */
.hotel-timeline {
    position: relative !important;
    margin-left: 1.5rem !important;
}

.hotel-timeline-item {
    padding-bottom: 1.5rem !important;
    padding-left: 1.5rem !important;
    position: relative !important;
}

.hotel-timeline-item::before {
    content: "" !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 1px !important;
    background-color: rgb(var(--gray-300)) !important;
}

.hotel-timeline-item::after {
    content: "" !important;
    position: absolute !important;
    left: -0.375rem !important;
    top: 0.25rem !important;
    width: 0.75rem !important;
    height: 0.75rem !important;
    border-radius: 9999px !important;
    background-color: rgb(var(--primary-500)) !important;
    border: 2px solid white !important;
}

.dark .hotel-timeline-item::after {
    border-color: rgb(30, 30, 30) !important;
}

/* Animasi subtle untuk interaksi */
@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--primary-500), 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--primary-500), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--primary-500), 0);
    }
}

.animate-pulse-glow {
    animation: pulseGlow 2s infinite !important;
}

/* Komponen reservasi */
.hotel-booking-card {
    border-left: 4px solid rgb(var(--primary-500)) !important;
    transition: all 0.2s ease !important;
}

.hotel-booking-card:hover {
    border-left-color: rgb(var(--primary-600)) !important;
}

/* Tambahan untuk form reservasi */
.hotel-checkin-form {
    border-radius: 0.5rem !important;
    padding: 1.5rem !important;
    background-color: white !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.dark .hotel-checkin-form {
    background-color: rgb(30, 30, 30) !important;
}

/* Elemen dekoratif */
.hotel-gold-divider {
    height: 1px !important;
    background-image: linear-gradient(to right, transparent, rgba(var(--primary-500), 0.5), transparent) !important;
    margin: 2rem 0 !important;
}

/* Status pembayaran */
.hotel-payment-paid {
    color: rgb(var(--success)) !important;
    font-weight: 600 !important;
}

.hotel-payment-pending {
    color: rgb(var(--warning)) !important;
    font-weight: 600 !important;
}

.hotel-payment-cancelled {
    color: rgb(var(--danger)) !important;
    font-weight: 600 !important;
}
<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Widgets;


use App\Aplikasi\Hotel\Services\Keranjang;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Illuminate\Contracts\View\View;

class KeranjangWidget extends Widget
{
    protected static string $view = 'hotel::reservasi.keranjang_widget';

    protected int|string|array $columnSpan = 1;

    protected static ?int $sort = 2;

    public static function canView(): bool
    {
        $currentPath = request()->path();

        return str_contains($currentPath, 'reservasis/create');
    }


    public function kurangiJumlah($itemId)
    {
        try {
            $this->getKeranjang()->kurangiJumlah($itemId, 1);
           
        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal mengurangi jumlah')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function tambahJumlah($itemId)
    {
        try {
            $this->getKeranjang()->tambahJumlah($itemId, 1);
          
        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menambah jumlah')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    public function getTotalPembayaran()
    {
        return session('total_pembayaran_temp', 0);
    }

    // Tambahkan method untuk menghitung sisa tagihan
    public function hitungSisaTagihan()
    {
        $totalAkhir = $this->hitungTotalAkhir();
        $totalPembayaran = $this->getTotalPembayaran();

        return max(0, $totalAkhir - $totalPembayaran);
    }

    public function getKeranjang(): Keranjang
    {
        return app(Keranjang::class);
    }

    public function calculateDuration($checkIn, $checkOut): int
    {
        if (empty($checkIn) || empty($checkOut)) {
            return 0;
        }

        $checkInDate = Carbon::parse($checkIn);
        $checkOutDate = Carbon::parse($checkOut);

        // Calculate duration in days
        return max(1, $checkOutDate->diffInDays($checkInDate));
    }

    public function getItems()
    {
        return $this->getKeranjang()->items();
    }

    public function getTotalHarga()
    {
        return $this->getKeranjang()->totalHarga();
    }

    /**
     * Ambil nilai biaya tambahan dari session
     */
    public function getBiayaTambahan(): array
    {
        $sessionData = session('biaya_tambahan', []);

        return [
            'pajak' => $sessionData['pajak'] ?? 0,
            'servis' => $sessionData['servis'] ?? 0,
            'diskon' => $sessionData['diskon'] ?? 0,
        ];
    }

    /**
     * Hitung total akhir dengan biaya tambahan
     */
    public function hitungTotalAkhir()
    {
        $biayaTambahan = $this->getBiayaTambahan();

        return $this->getKeranjang()->hitungTotalAkhir(
            (float)$biayaTambahan['diskon'],
            0, // Diskon nominal
            (float)$biayaTambahan['servis'],
            (float)$biayaTambahan['pajak']
        );
    }

    public function removeItem($itemId)
    {
        $this->getKeranjang()->hapusItem($itemId);
        $this->dispatch('keranjang-updated');
    }

    public function render(): View
    {
        $biayaTambahan = $this->getBiayaTambahan();
        $totalAkhir = $this->hitungTotalAkhir();
        $totalHarga = $this->getTotalHarga();
        $totalPembayaran = $this->getTotalPembayaran();
        $sisaTagihan = $this->hitungSisaTagihan();

        // Hitung komponen-komponen biaya
        $diskonNominal = round(($biayaTambahan['diskon'] / 100) * $totalHarga);
        $subTotal = $totalHarga - $diskonNominal;
        $pajakNominal = round(($biayaTambahan['pajak'] / 100) * $subTotal);
        $servisNominal = round(($biayaTambahan['servis'] / 100) * $subTotal);

        return view(static::$view, [
            'items' => $this->getItems(),
            'totalHarga' => $totalHarga,
            'totalAkhir' => $totalAkhir,
            'totalPembayaran' => $totalPembayaran,
            'sisaTagihan' => $sisaTagihan,
            'persenValues' => $biayaTambahan,
            'komponenBiaya' => [
                'diskonNominal' => $diskonNominal,
                'subTotal' => $subTotal,
                'pajakNominal' => $pajakNominal,
                'servisNominal' => $servisNominal,
            ]
        ]);
    }

    public function kosongkan()
    {
        $this->getKeranjang()->kosongkanKeranjang();
        $this->dispatch('keranjang-updated');
    }
}

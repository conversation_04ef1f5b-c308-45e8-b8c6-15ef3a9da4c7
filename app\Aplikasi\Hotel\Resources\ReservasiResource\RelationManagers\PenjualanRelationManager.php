<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PenjualanRelationManager extends RelationManager
{
    protected static string $relationship = 'penjualan';
    protected static ?string $title = 'Penjualan';
    protected static ?string $modelLabel = 'Penjualan';
    protected static ?string $pluralModelLabel = 'Penjualan';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('penjualan_invoice')
                    ->label('No Invoice')
                    ->required(),
                Forms\Components\TextInput::make('nama')
                    ->label('Nama')
                    ->maxLength(100),
                Forms\Components\TextInput::make('nama_tamu')
                    ->label('Nama Tamu')
                    ->maxLength(100),
                Forms\Components\TextInput::make('sub')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('diskon')
                    ->numeric(),
                Forms\Components\TextInput::make('pajak')
                    ->numeric(),
                Forms\Components\TextInput::make('servis')
                    ->numeric(),
                Forms\Components\TextInput::make('jumlah_pembayaran')
                    ->numeric(),
                Forms\Components\Select::make('metode_pembayaran')
                    ->options([
                        'cash' => 'Cash',
                        'qris' => 'QRIS',
                        'transfer' => 'Transfer Bank',
                        'kartukredit' => 'Kartu Kredit',
                    ]),
                Forms\Components\TextInput::make('status_pembayaran')
                    ->maxLength(191),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('penjualan_invoice')
            ->columns([
                Tables\Columns\TextColumn::make('penjualan_invoice')
                    ->label('No Invoice')
                    ->searchable(),
                Tables\Columns\TextColumn::make('nama')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sub')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('jumlah_pembayaran')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('metode_pembayaran')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status_pembayaran')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}

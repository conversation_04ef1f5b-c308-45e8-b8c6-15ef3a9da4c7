<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\Konfig as BaseKonfig;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperKonfig
 */
class Konfig extends BaseKonfig
{



    protected static function booted()
    {
        static::addGlobalScope('toko_id', function (Builder $builder) {

            $builder->whereIn('jenis', ['GLOBAL', 'HOTEL']);


        });

        static::creating(function ($model) {
            $model->jenis = 'HOTEL';
        });

        static::updating(function ($model) {
            $model->jenis = 'HOTEL';
        });
    }
}

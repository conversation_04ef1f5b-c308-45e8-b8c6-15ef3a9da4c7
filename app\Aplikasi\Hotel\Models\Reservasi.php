<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\Penjualan;
use App\Models\User;


use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperReservasi
 */
class Reservasi extends Model
{
    use HasFactory;

    public function getTable()
    {
         return config('tabel.t_reservasi.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_reservasi.kolom', []);
    }
 


    protected $casts = [
        'check_in' => 'datetime',
        'check_out' => 'datetime',
        'jumlah_pembayaran' => 'float',
    ];


    public function tamu(): BelongsTo
    {
        return $this->belongsTo(Tamu::class);
    }

    public function penjualan(): HasMany
    {
        return $this->hasMany(Penjualan::class, 'reservasi_id');
    }


    public function pembayaran(): HasMany
    {
        return $this->hasMany(Pembayaran::class, 'reservasi_id');
    }

    public function transaksi(): HasMany
    {
        return $this->hasMany(Transaksi::class, 'reservasi_id');
    }


    public function hitungDurasi(): int
    {
        if (! $this->check_in || ! $this->check_out) {
            return 0;
        }

        return $this->check_in->diffInDays($this->check_out);
    }


    public function isAktifSekarang(): bool
    {
        $now = now();
        return $this->check_in <= $now && $this->check_out >= $now;
    }


    public function isSelesai(): bool
    {
        return $this->status === 'SCO' || $this->check_out < now();
    }


    public function hitungTotalPembayaran(): float
    {
        return $this->pembayaran()
            ->sum('jumlah');
    }


    public function hitungSisaPembayaran(): float
    {
        return $this->totalHarga() - $this->hitungTotalPembayaran();
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'karyawan_id');
    }

    /**
     * Menghitung total harga dengan menggunakan eager loading jika tersedia
     *
     * @return float
     */
    public function totalHarga()
    {
        // Jika transaksi belum di-load, load terlebih dahulu
        if (!$this->relationLoaded('transaksi')) {
            $this->load('transaksi');
        }

        // Menghitung subtotal dengan mengalikan jumlah * harga untuk setiap transaksi
        $subtotal = $this->transaksi->sum(function ($transaksi) {
            return $transaksi->jumlah * $transaksi->harga;
        });
        $totalSetelahDiskon = $subtotal - ($subtotal * ($this->diskon / 100));
        $totalSetelahPajak = $totalSetelahDiskon * (1 + ($this->pajak / 100));
        $totalAkhir = $totalSetelahPajak + ($subtotal * ($this->servis / 100));

        return $totalAkhir;
    }



    public function hitungNilaiPajak(): float
    {

        return $this->totalHargaBersih() * ($this->pajak / 100);
    }

    public function hitungNilaiDiskon(): float
    {
        return $this->totalHargaBersih() * ($this->diskon / 100);
    }

    public function hitungNilaiServis(): float
    {
        return $this->totalHargaBersih() * ($this->servis / 100);
    }



    /**
     * Menghitung sisa tagihan dengan menggunakan data yang sudah di-eager load
     *
     * @return float
     */
    public function hitungSisaTagihan()
    {
        // Cek apakah total_pembayaran sudah di-eager load
        if (array_key_exists('total_pembayaran', $this->attributes)) {
            $pembayaran = $this->total_pembayaran;
        } else {
            $pembayaran = $this->hitungTotalPembayaran();
        }

        $tagihan = $this->totalHarga();
        $sisa = $tagihan - $pembayaran;
        return $sisa < 1 ? 0 : $sisa;
    }

    /**
     * Menghitung total harga bersih dengan menggunakan eager loading jika tersedia
     *
     * @return float
     */
    public function totalHargaBersih()
    {
        // Jika transaksi belum di-load, load terlebih dahulu
        if (!$this->relationLoaded('transaksi')) {
            $this->load('transaksi');
        }

        // Menghitung subtotal dengan mengalikan jumlah * harga untuk setiap transaksi
        $subtotal = $this->transaksi->sum(function ($transaksi) {
            return $transaksi->jumlah * $transaksi->harga;
        });

        return $subtotal;
    }

    public function hitungTotalHargaAkhir(): float
    {
        $subtotal = $this->totalHargaBersih();
        $nilaiPajak = $this->hitungNilaiPajak();
        $nilaiDiskon = $this->hitungNilaiDiskon();
        $nilaiServis = $this->hitungNilaiServis();

        return $subtotal + $nilaiPajak - $nilaiDiskon + $nilaiServis;
    }


    /**
     * Mendapatkan status reservasi dengan menggunakan cache
     *
     * @return string
     */
    public function statusReservasi()
    {
        $hasil = cache()->remember('reservasi_status_raw', now()->addHour(), function () {
            return Konfig::jsonKuRaw('reservasi_status');
        });
        return $hasil[$this->status_reservasi] ?? $this->status_reservasi;
    }

    /**
     * Accessor untuk status_reservasi_text
     *
     * @return string
     */
    public function getStatusReservasiTextAttribute()
    {
        $hasil = cache()->remember('reservasi_status_raw', now()->addHour(), function () {
            return Konfig::jsonKuRaw('reservasi_status');
        });
        return $hasil[$this->status_reservasi] ?? '';
    }

    public function kamar(): BelongsTo
    {
        return $this->belongsTo(Kamar::class, 'kamar_id');
    }


    public function hargajual()
    {
        return $this->hasMany(HargaJual::class, 'kamar_id', 'id');
    }

    public function getTotalHargaAttribute(): float
    {
        return $this->totalHarga();
    }

    public static function hitungTotalTagihanSummary($query): float
    {
        // Ambil ID dari query
        $ids = $query->pluck('id')->toArray();

        // Gunakan ID untuk mendapatkan model Reservasi yang lengkap
        return self::whereIn('id', $ids)
            ->get()
            ->sum(function ($reservasi) {
                return $reservasi->totalHarga();
            });
    }


}
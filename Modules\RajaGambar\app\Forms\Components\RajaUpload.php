<?php

namespace Modules\RajaGambar\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Modules\RajaGambar\Services\RajaGambarService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class RajaUpload extends FileUpload
{
    protected string $view = 'rajagambar::forms.components.raja-upload';
    
    protected bool $autoOptimize = false;
    protected ?array $autoResizeConfig = null;
    protected ?array $watermarkConfig = null;
    protected array $customEffects = [];
    protected string $outputDirectory = 'raja-processed';
    protected ?RajaGambarService $rajaGambarService = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rajaGambarService = app(RajaGambarService::class);

        // Set default configurations
        $this->acceptedFileTypes(['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'])
            ->maxSize(10240) // 10MB
            ->disk('public')
            ->directory($this->outputDirectory)
            ->visibility('public');

        // Add processing after upload
        $this->afterStateUpdated(function ($state, $component) {
            if ($state) {
                $this->processUploadedImages($state, $component);
            }
        });
    }
    
    /**
     * Enable automatic optimization
     */
    public function autoOptimize(bool $optimize = true): static
    {
        $this->autoOptimize = $optimize;
        return $this;
    }
    
    /**
     * Set automatic resize configuration
     */
    public function autoResize(int $width, int $height, ?string $fitMethod = null): static
    {
        $this->autoResizeConfig = [
            'width' => $width,
            'height' => $height,
            'fit_method' => $fitMethod
        ];
        return $this;
    }
    
    /**
     * Add watermark configuration
     */
    public function addWatermark(string $watermarkPath, string $position = 'bottom-right', int $opacity = 50, int $padding = 10): static
    {
        $this->watermarkConfig = [
            'path' => $watermarkPath,
            'position' => $position,
            'opacity' => $opacity,
            'padding' => $padding
        ];
        return $this;
    }
    
    /**
     * Apply custom effects
     */
    public function applyEffects(array $effects): static
    {
        $this->customEffects = $effects;
        return $this;
    }
    
    /**
     * Set output directory
     */
    public function outputDirectory(string $directory): static
    {
        $this->outputDirectory = $directory;
        $this->directory($directory);
        return $this;
    }
    
    /**
     * Get auto optimize setting
     */
    public function getAutoOptimize(): bool
    {
        return $this->autoOptimize;
    }
    
    /**
     * Get auto resize configuration
     */
    public function getAutoResizeConfig(): ?array
    {
        return $this->autoResizeConfig;
    }
    
    /**
     * Get watermark configuration
     */
    public function getWatermarkConfig(): ?array
    {
        return $this->watermarkConfig;
    }
    
    /**
     * Get custom effects
     */
    public function getCustomEffects(): array
    {
        return $this->customEffects;
    }
    
    /**
     * Get output directory
     */
    public function getOutputDirectory(): string
    {
        return $this->outputDirectory;
    }
    
    /**
     * Process uploaded images using RajaGambarService
     */
    protected function processUploadedImages($state, $component): void
    {
        if (!$this->rajaGambarService) {
            return;
        }
        
        $files = is_array($state) ? $state : [$state];
        $processedFiles = [];
        
        foreach ($files as $file) {
            if (empty($file)) continue;
            
            try {
                $processedFile = $this->processImage($file);
                if ($processedFile) {
                    $processedFiles[] = $processedFile;
                }
            } catch (\Exception $e) {
                // Log error but continue processing other files
                Log::error('RajaUpload processing error: ' . $e->getMessage());
                $processedFiles[] = $file; // Keep original if processing fails
            }
        }
        
        // Update the component state with processed files
        if (!empty($processedFiles)) {
            $component->state(is_array($state) ? $processedFiles : $processedFiles[0]);
        }
    }
    
    /**
     * Process individual image
     */
    protected function processImage(string $filePath): ?string
    {
        $disk = Storage::disk($this->getDiskName());
        
        if (!$disk->exists($filePath)) {
            return null;
        }
        
        $fullPath = $disk->path($filePath);
        
        // Start processing chain
        $service = $this->rajaGambarService->load($fullPath);
        
        // Apply auto resize if configured
        if ($this->autoResizeConfig) {
            $config = $this->autoResizeConfig;
            if ($config['fit_method']) {
                $service->fit($config['width'], $config['height'], $config['fit_method']);
            } else {
                $service->resize($config['width'], $config['height']);
            }
        }
        
        // Apply custom effects
        foreach ($this->customEffects as $effect => $params) {
            switch ($effect) {
                case 'brightness':
                    $service->brightness($params);
                    break;
                case 'contrast':
                    $service->contrast($params);
                    break;
                case 'gamma':
                    $service->gamma($params);
                    break;
                case 'colorize':
                    $service->colorize($params['red'], $params['green'], $params['blue']);
                    break;
                case 'background':
                    $service->background($params);
                    break;
                case 'border':
                    $service->border($params['width'] ?? 1, $params['type'] ?? 'overlay', $params['color'] ?? '#000000');
                    break;
                case 'orientation':
                    $service->orientation($params);
                    break;
                case 'flip':
                    $service->flip($params);
                    break;
            }
        }
        
        // Apply watermark if configured
        if ($this->watermarkConfig) {
            $config = $this->watermarkConfig;
            $service->watermark($config['path'], $config['position'], $config['opacity'], $config['padding']);
        }
        
        // Apply optimization if enabled
        if ($this->autoOptimize) {
            $service->optimize();
        }
        
        // Generate processed filename
        $pathInfo = pathinfo($filePath);
        $processedFileName = $pathInfo['filename'] . '_processed.' . $pathInfo['extension'];
        $processedPath = $this->outputDirectory . '/' . $processedFileName;
        
        // Save processed image
        $result = $service->save($disk->path($processedPath));
        
        if ($result) {
            // Delete original file if different from processed
            if ($filePath !== $processedPath) {
                $disk->delete($filePath);
            }
            return $processedPath;
        }
        
        return $filePath; // Return original if processing failed
    }
    
    /**
     * Create a new RajaUpload instance
     */
    public static function make(string $name): static
    {
        return new static($name);
    }
}

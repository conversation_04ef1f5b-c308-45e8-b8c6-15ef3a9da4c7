<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Illuminate\Support\Facades\App;

class data extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'pages.data';
    protected static ?string $title = 'Data';
    protected static ?string $slug = 'system/data';
    protected static ?string $navigationLabel = 'Data';
    protected static bool $shouldRegisterNavigation = true;
    protected static ?string $navigationGroup = 'System';

    // Nonaktifkan polling otomatis yang mungkin menyebabkan refresh
    protected $polling = false;

    // Tambahkan listener untuk event
    protected $listeners = ['refreshData' => 'refreshData'];

    public $tables = [];
    public $thotel = [];
    public $backupFiles = [];
    public $factories = [];
    public $defaultRecordCount = 10;


    // Tambahkan variabel untuk menyimpan tab aktif
    public $activeTab;

    // Variabel untuk terminal console
    public $consoleLog = [];
    public $showConsole = true;

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    /**
     * Metode ini dipanggil setiap kali halaman di-render
     */
    public function render(): \Illuminate\Contracts\View\View
    {
        // Pastikan tab aktif disimpan di session
        if ($this->activeTab) {
            session(['data_active_tab' => $this->activeTab]);
        }

        return parent::render();
    }

    public function mount()
    {
        // Set locale ke bahasa Indonesia untuk format tanggal
        Carbon::setLocale('id');
        App::setLocale('id');

        $configTables = config('tabel');

        // Langsung gunakan config('tabel.tablehotel') tanpa melalui ConfigTabelHelper::ambilTabel()
        $this->thotel = config('tabel.tablehotel');

        $formattedTables = [];

        foreach ($configTables as $table) {
            if (isset($table['nama_tabel'])) {
                $formattedTables[] = [
                    'nama_tabel' => $table['nama_tabel']
                ];
            }
        }

        $this->tables = $formattedTables;

        // Muat daftar file backup
        $this->loadBackupFiles();

        // Muat daftar factory
        $this->loadFactories();

        // Ambil tab aktif dari session atau gunakan default
        $this->activeTab = session('data_active_tab', 'tab1');

        // Tambahkan pesan awal ke terminal
        $this->addToConsoleLog('Terminal siap digunakan', 'info');
        $this->addToConsoleLog('Selamat datang di halaman Data Manager', 'success');
    }

    /**
     * Memuat daftar factory yang tersedia
     */
    public function loadFactories()
    {
        $factoryPath = database_path('factories');
        $factories = [];

        if (File::exists($factoryPath)) {
            $factoryFiles = File::files($factoryPath);

            foreach ($factoryFiles as $file) {
                $fileName = $file->getFilename();
                if (str_ends_with($fileName, 'Factory.php')) {
                    $factoryName = str_replace('Factory.php', '', $fileName);
                    $factories[] = [
                        'name' => $factoryName,
                        'file' => $fileName,
                        'path' => $file->getPathname(),
                        'record_count' => $this->defaultRecordCount,
                    ];
                }
            }
        }

        $this->factories = $factories;
    }

    /**
     * Menjalankan factory untuk membuat data dummy
     */
    public function runFactory($factoryName, $count = null)
    {
        try {
            // Jika count tidak diberikan, cari di array factories
            if ($count === null) {
                foreach ($this->factories as $factory) {
                    if ($factory['name'] === $factoryName) {
                        $count = $factory['record_count'];
                        break;
                    }
                }

                // Jika masih tidak ditemukan, gunakan default
                if ($count === null) {
                    $count = $this->defaultRecordCount;
                }
            }

            // Pastikan count adalah integer positif
            $count = max(1, (int)$count);

            // Log ke terminal
            $this->addToConsoleLog("Menjalankan factory {$factoryName} untuk membuat {$count} record...", 'info');

            // Coba beberapa pendekatan untuk menjalankan factory
            $success = false;
            $message = '';

            // Pendekatan 1: Coba jalankan seeder jika ada
            $seederClass = "\\Database\\Seeders\\{$factoryName}Seeder";
            if (class_exists($seederClass)) {
                Artisan::call('db:seed', [
                    '--class' => $seederClass,
                ]);
                $message = "Berhasil menjalankan seeder {$seederClass}";
                $success = true;
                $this->addToConsoleLog($message, 'success');
            }
            // Pendekatan 2: Coba jalankan factory langsung dengan namespace App\Models
            else {
                $modelClass = "\\App\\Models\\{$factoryName}";
                if (class_exists($modelClass) && method_exists($modelClass, 'factory')) {
                    try {
                        $modelClass::factory()->count($count)->create();
                        $message = "Berhasil membuat {$count} record menggunakan {$factoryName}Factory";
                        $success = true;
                        $this->addToConsoleLog($message, 'success');
                    } catch (\Exception $e) {
                        // Lanjut ke pendekatan berikutnya
                        $message = "Gagal menjalankan factory untuk {$modelClass}: " . $e->getMessage();
                        $this->addToConsoleLog($message, 'warning');
                    }
                }

                // Pendekatan 3: Coba jalankan factory dengan namespace App\Models\Extend
                if (!$success) {
                    $extendModelClass = "\\App\\Models\\Extend\\{$factoryName}";
                    if (class_exists($extendModelClass) && method_exists($extendModelClass, 'factory')) {
                        try {
                            $extendModelClass::factory()->count($count)->create();
                            $message = "Berhasil membuat {$count} record menggunakan {$factoryName}Factory (Extend)";
                            $success = true;
                            $this->addToConsoleLog($message, 'success');
                        } catch (\Exception $e) {
                            // Lanjut ke pendekatan berikutnya
                            $message .= "\nGagal menjalankan factory untuk {$extendModelClass}: " . $e->getMessage();
                            $this->addToConsoleLog($message, 'warning');
                        }
                    }
                }

                // Pendekatan 4: Coba jalankan factory dengan Tinker sebagai fallback
                if (!$success) {
                    try {
                        $tinkerCommand = "\\App\\Models\\{$factoryName}::factory()->count({$count})->create();";
                        Artisan::call('tinker', [
                            '--execute' => $tinkerCommand,
                        ]);
                        $message = "Berhasil menjalankan factory melalui Tinker";
                        $success = true;
                        $this->addToConsoleLog($message, 'success');
                    } catch (\Exception $e) {
                        $message .= "\nGagal menjalankan factory melalui Tinker: " . $e->getMessage();
                        $this->addToConsoleLog($message, 'error');
                    }
                }
            }

            if ($success) {
                // Perbarui jumlah record di array factories jika berhasil
                foreach ($this->factories as $key => $factory) {
                    if ($factory['name'] === $factoryName) {
                        // Pastikan nilai record_count selalu diperbarui
                        $this->factories[$key]['record_count'] = $count;
                        break;
                    }
                }

                // Notifikasi sukses dengan jumlah record yang benar
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => "Berhasil membuat {$count} record menggunakan {$factoryName}Factory"
                ]);
            } else {
                throw new \Exception("Tidak dapat menjalankan factory: {$message}");
            }
        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal menjalankan factory: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal menjalankan factory: " . $e->getMessage(), 'error');
        }
    }

    /**
     * Mengubah jumlah record yang akan dibuat oleh factory
     */
    public function updateRecordCount($index, $count)
    {
        if (isset($this->factories[$index])) {
            // Konversi ke integer untuk memastikan nilai yang valid
            $count = (int) $count;
            if ($count < 1) $count = 1;
            if ($count > 1000) $count = 1000;

            // Perbarui nilai di array factories
            $this->factories[$index]['record_count'] = $count;

            // Log perubahan
            $factoryName = $this->factories[$index]['name'];
            $this->addToConsoleLog("Jumlah record untuk {$factoryName} diubah menjadi {$count}", 'info');

            // Tambahkan notifikasi untuk konfirmasi visual
            $this->dispatch('notify', [
                'type' => 'info',
                'message' => "Jumlah record untuk {$factoryName} diubah menjadi {$count}"
            ]);
        }
    }

    /**
     * Memuat daftar file backup yang tersedia
     */
    public function loadBackupFiles()
    {
        $backupPath = storage_path('backupdb');

        // Buat direktori jika belum ada
        if (!File::exists($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }

        $files = File::files($backupPath);
        $backupFiles = [];

        foreach ($files as $file) {
            if (File::extension($file) === 'sql') {
                $backupFiles[] = [
                    'name' => File::basename($file),
                    'size' => $this->formatFileSize(File::size($file)),
                    'date' => Carbon::createFromTimestamp(File::lastModified($file))->locale('id')->translatedFormat('D, d F Y H:i'),
                    'path' => $file->getPathname(),
                ];
            }
        }

        // Urutkan berdasarkan tanggal terbaru
        usort($backupFiles, function ($a, $b) {
            return strtotime(str_replace(' ', '', $b['date'])) - strtotime(str_replace(' ', '', $a['date']));
        });

        $this->backupFiles = $backupFiles;
    }

    /**
     * Format ukuran file menjadi format yang mudah dibaca
     */
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Deteksi path Laragon
     */
    private function detectLaragonPath()
    {
        // Cek beberapa lokasi umum
        $possiblePaths = [
            'C:\\laragon',
            'D:\\laragon',
            'E:\\laragon',
            'C:\\Program Files\\laragon',
            'D:\\Program Files\\laragon',
            'E:\\Program Files\\laragon',
        ];

        foreach ($possiblePaths as $path) {
            if (is_dir($path)) {
                return $path;
            }
        }

        // Jika tidak ditemukan, coba cari dari environment variable
        $envPath = getenv('LARAGON_ROOT');
        if ($envPath && is_dir($envPath)) {
            return $envPath;
        }

        // Default path jika tidak ditemukan
        return 'C:\\laragon';
    }

    public function kosongkanTabel($tableName, $tableKey = null)
    {
        try {
            // Log ke terminal
            $this->addToConsoleLog("Mengosongkan tabel {$tableName}...", 'info');

            // Cari konfigurasi tabel untuk mendapatkan kondisi where jika ada
            $whereConditions = null;

            // Jika tableKey diberikan, gunakan itu untuk mencari konfigurasi
            if ($tableKey) {
                foreach ($this->thotel as $key => $config) {
                    if ($key === $tableKey && isset($config['where'])) {
                        $whereConditions = $config['where'];
                        break;
                    }
                }
            } else {
                // Jika tableKey tidak diberikan, cari berdasarkan nama tabel
                foreach ($this->thotel as $config) {
                    if ($config['nama_tabel'] === $tableName && isset($config['where'])) {
                        $whereConditions = $config['where'];
                        break;
                    }
                }
            }

            // Buat query builder
            $query = DB::table($tableName);

            // Terapkan kondisi where jika ada
            if ($whereConditions) {
                foreach ($whereConditions as $column => $value) {
                    $query->where($column, $value);
                }

                // Hapus data dengan kondisi where
                $count = $query->count();
                $query->delete();

                $message = "Tabel {$tableName} berhasil dikosongkan ({$count} data)";
            } else {
                // Jika tidak ada kondisi where, gunakan truncate
                $query->truncate();
                $message = "Tabel {$tableName} berhasil dikosongkan";
            }

            // Notifikasi sukses
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => $message
            ]);

            // Log ke terminal
            $this->addToConsoleLog($message, 'success');

            // Refresh data tanpa mengubah tab aktif
            $this->refreshData();
        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal mengosongkan tabel: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal mengosongkan tabel {$tableName}: " . $e->getMessage(), 'error');
        }
    }

    public function refreshData()
    {
        // Refresh data tabel tanpa mengubah tab aktif
        $configTables = config('tabel');
        $formattedTables = [];

        foreach ($configTables as $table) {
            if (isset($table['nama_tabel'])) {
                $formattedTables[] = [
                    'nama_tabel' => $table['nama_tabel']
                ];
            }
        }

        $this->tables = $formattedTables;

        // Refresh data thotel
        $this->thotel = config('tabel.tablehotel');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;

        // Simpan tab aktif ke session
        session(['data_active_tab' => $tab]);

        // Jika tab backup dipilih, muat ulang daftar file backup
        if ($tab === 'tab5') {
            $this->loadBackupFiles();
        }
    }

    /**
     * Menambahkan pesan ke console log
     */
    public function addToConsoleLog($message, $type = 'info')
    {
        // Tambahkan timestamp ke pesan
        $timestamp = Carbon::now()->format('H:i:s');

        // Tampung maksimal 100 baris log untuk performa
        if (count($this->consoleLog) > 100) {
            // Hapus 20 log paling lama jika sudah mencapai 100
            $this->consoleLog = array_slice($this->consoleLog, 20);
        }

        // Tambahkan pesan ke console log
        $this->consoleLog[] = [
            'timestamp' => $timestamp,
            'message' => $message,
            'type' => $type
        ];

        // Tampilkan console jika belum ditampilkan
        $this->showConsole = true;
    }

    /**
     * Membersihkan console log
     */
    public function clearConsoleLog()
    {
        $this->consoleLog = [];
    }

    /**
     * Toggle tampilan console
     */
    public function toggleConsole()
    {
        $this->showConsole = !$this->showConsole;
    }

    /**
     * Membuat backup database menggunakan mysqldump
     */
    public function backupDatabase()
    {
        try {
            // Ambil konfigurasi database dari .env
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            // Buat nama file dengan format tanggal D_F_Y_H_i (contoh: Jum_Mei_2023_14_30)
            $timestamp = Carbon::now()->locale('id')->format('D_F_Y_H_i');
            // Pastikan nama file tidak mengandung karakter yang tidak valid
            $timestamp = str_replace([':', '/', '\\', '?', '*', '"', '<', '>', '|'], '_', $timestamp);
            $filename = "backup_{$timestamp}.sql";
            $backupPath = storage_path("backupdb/{$filename}");

            // Pastikan direktori ada
            if (!File::exists(storage_path('backupdb'))) {
                File::makeDirectory(storage_path('backupdb'), 0755, true);
            }

            // Log ke terminal
            $this->addToConsoleLog("Memulai backup database ke {$filename}...", 'info');

            // Deteksi path Laragon
            $laragonPath = $this->detectLaragonPath();
            $this->addToConsoleLog("Menggunakan Laragon di: {$laragonPath}", 'info');

            // Cari path mysqldump dari Laragon
            $mysqldumpPath = "{$laragonPath}\\bin\\mysql\\mysql-8.0.30-winx64\\bin\\mysqldump.exe";

            // Jika file tidak ditemukan, coba path alternatif
            if (!file_exists($mysqldumpPath)) {
                $mysqldumpPath = "{$laragonPath}\\bin\\mysql\\mysql-5.7.33-winx64\\bin\\mysqldump.exe";
            }

            // Jika masih tidak ditemukan, coba path lain
            if (!file_exists($mysqldumpPath)) {
                // Coba cari di direktori bin Laragon
                $laragonBinPath = "{$laragonPath}\\bin";
                $this->addToConsoleLog("Mencari mysqldump di {$laragonBinPath}...", 'info');

                // Cari di semua folder mysql
                $mysqlFolders = glob($laragonBinPath . "\\mysql\\*\\bin\\mysqldump.exe");
                if (!empty($mysqlFolders)) {
                    $mysqldumpPath = $mysqlFolders[0];
                    $this->addToConsoleLog("Menemukan mysqldump di {$mysqldumpPath}", 'success');
                } else {
                    // Jika masih tidak ditemukan, gunakan perintah tanpa path
                    $mysqldumpPath = "mysqldump";
                    $this->addToConsoleLog("Tidak menemukan mysqldump, menggunakan perintah default", 'warning');
                }
            }

            // Buat perintah mysqldump dengan path yang ditemukan
            $command = "\"{$mysqldumpPath}\"";
            $command .= " --host={$host}";
            $command .= " --port={$port}";
            $command .= " --user={$username}";

            // Tambahkan password jika ada
            if ($password) {
                $command .= " --password={$password}";
            }

            $command .= " {$database} > \"{$backupPath}\"";

            // Jalankan perintah
            $process = Process::fromShellCommandline($command);
            $process->setTimeout(300); // 5 menit timeout
            $process->run();

            // Cek hasil
            if (!$process->isSuccessful()) {
                $this->addToConsoleLog("Perintah mysqldump gagal, mencoba pendekatan alternatif...", 'warning');

                // Coba pendekatan alternatif dengan PHP
                try {
                    $this->backupDatabaseWithPHP($backupPath, $host, $port, $database, $username, $password);
                } catch (\Exception $e) {
                    throw new \Exception("Gagal backup database: " . $e->getMessage());
                }
            }

            // Refresh daftar file backup
            $this->loadBackupFiles();

            // Notifikasi sukses
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => "Backup database berhasil dibuat: {$filename}"
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Backup database berhasil dibuat: {$filename}", 'success');
            $this->addToConsoleLog("Ukuran file: " . $this->formatFileSize(File::size($backupPath)), 'info');

        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal membuat backup: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal membuat backup: " . $e->getMessage(), 'error');
        }
    }

    /**
     * Download file backup
     */
    public function downloadBackup($filename)
    {
        $filePath = storage_path("backupdb/{$filename}");

        if (File::exists($filePath)) {
            $this->addToConsoleLog("Mendownload file backup: {$filename}", 'info');

            return response()->download($filePath);
        } else {
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "File backup tidak ditemukan: {$filename}"
            ]);

            $this->addToConsoleLog("File backup tidak ditemukan: {$filename}", 'error');
        }
    }

    /**
     * Restore database dari file backup
     */
    public function restoreBackup($filename)
    {
        try {
            // Ambil konfigurasi database dari .env
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            $backupPath = storage_path("backupdb/{$filename}");

            // Cek apakah file ada
            if (!File::exists($backupPath)) {
                throw new \Exception("File backup tidak ditemukan: {$filename}");
            }

            // Log ke terminal
            $this->addToConsoleLog("Memulai restore database dari {$filename}...", 'info');

            // Deteksi path Laragon
            $laragonPath = $this->detectLaragonPath();
            $this->addToConsoleLog("Menggunakan Laragon di: {$laragonPath}", 'info');

            // Cari path mysql dari Laragon
            $mysqlPath = "{$laragonPath}\\bin\\mysql\\mysql-8.0.30-winx64\\bin\\mysql.exe";

            // Jika file tidak ditemukan, coba path alternatif
            if (!file_exists($mysqlPath)) {
                $mysqlPath = "{$laragonPath}\\bin\\mysql\\mysql-5.7.33-winx64\\bin\\mysql.exe";
            }

            // Jika masih tidak ditemukan, coba path lain
            if (!file_exists($mysqlPath)) {
                // Coba cari di direktori bin Laragon
                $laragonBinPath = "{$laragonPath}\\bin";
                $this->addToConsoleLog("Mencari mysql di {$laragonBinPath}...", 'info');

                // Cari di semua folder mysql
                $mysqlFolders = glob($laragonBinPath . "\\mysql\\*\\bin\\mysql.exe");
                if (!empty($mysqlFolders)) {
                    $mysqlPath = $mysqlFolders[0];
                    $this->addToConsoleLog("Menemukan mysql di {$mysqlPath}", 'success');
                } else {
                    // Jika masih tidak ditemukan, gunakan perintah tanpa path
                    $mysqlPath = "mysql";
                    $this->addToConsoleLog("Tidak menemukan mysql, menggunakan perintah default", 'warning');
                }
            }

            // Buat perintah mysql dengan path yang ditemukan
            $command = "\"{$mysqlPath}\"";
            $command .= " --host={$host}";
            $command .= " --port={$port}";
            $command .= " --user={$username}";

            // Tambahkan password jika ada
            if ($password) {
                $command .= " --password={$password}";
            }

            $command .= " {$database} < \"{$backupPath}\"";

            // Jalankan perintah
            $process = Process::fromShellCommandline($command);
            $process->setTimeout(300); // 5 menit timeout
            $process->run();

            // Cek hasil
            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }

            // Notifikasi sukses
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => "Database berhasil direstore dari: {$filename}"
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Database berhasil direstore dari: {$filename}", 'success');

        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal restore database: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal restore database: " . $e->getMessage(), 'error');
        }
    }

    /**
     * Backup database menggunakan PHP
     */
    private function backupDatabaseWithPHP($backupPath, $host, $port, $database, $username, $password)
    {
        $this->addToConsoleLog("Mencoba backup database dengan PHP...", 'info');

        try {
            // Buat koneksi PDO
            $dsn = "mysql:host={$host};port={$port};dbname={$database}";
            $pdo = new \PDO($dsn, $username, $password);
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // Ambil daftar tabel
            $tables = [];
            $result = $pdo->query("SHOW TABLES");
            while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                $tables[] = $row[0];
            }

            $this->addToConsoleLog("Ditemukan " . count($tables) . " tabel", 'info');

            // Buat file backup
            $output = "-- Backup database {$database} - " . date('Y-m-d H:i:s') . "\n";
            $output .= "-- Generated by Hotel RID Backup System\n\n";
            $output .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

            // Proses setiap tabel
            foreach ($tables as $table) {
                $this->addToConsoleLog("Memproses tabel: {$table}", 'info');

                // Ambil struktur tabel
                $result = $pdo->query("SHOW CREATE TABLE `{$table}`");
                $row = $result->fetch(\PDO::FETCH_NUM);
                $output .= "DROP TABLE IF EXISTS `{$table}`;\n";
                $output .= $row[1] . ";\n\n";

                // Ambil data tabel
                $result = $pdo->query("SELECT * FROM `{$table}`");
                $columnCount = $result->columnCount();

                // Jika ada data
                if ($result->rowCount() > 0) {
                    $output .= "INSERT INTO `{$table}` VALUES\n";
                    $rowCount = 0;

                    while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                        $rowCount++;
                        $output .= "(";

                        for ($i = 0; $i < $columnCount; $i++) {
                            if ($row[$i] === null) {
                                $output .= "NULL";
                            } elseif (is_numeric($row[$i])) {
                                $output .= $row[$i];
                            } else {
                                $output .= "'" . addslashes($row[$i]) . "'";
                            }

                            if ($i < ($columnCount - 1)) {
                                $output .= ",";
                            }
                        }

                        $output .= ")";

                        if ($rowCount < $result->rowCount()) {
                            $output .= ",\n";
                        } else {
                            $output .= ";\n\n";
                        }
                    }
                }
            }

            $output .= "SET FOREIGN_KEY_CHECKS=1;\n";

            // Simpan ke file
            file_put_contents($backupPath, $output);

            $this->addToConsoleLog("Backup database dengan PHP berhasil", 'success');

            return true;
        } catch (\Exception $e) {
            $this->addToConsoleLog("Backup database dengan PHP gagal: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Jalankan backup otomatis
     */
    public function runAutoBackup()
    {
        try {
            $this->addToConsoleLog("Menjalankan backup otomatis...", 'info');

            // Jalankan command backup otomatis
            $process = Process::fromShellCommandline('php artisan db:auto-backup --force');
            $process->setTimeout(3600); // 1 jam timeout
            $process->run();

            // Cek hasil
            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }

            // Ambil output
            $output = $process->getOutput();

            // Log output ke terminal
            foreach (explode("\n", $output) as $line) {
                if (trim($line)) {
                    $this->addToConsoleLog($line, 'info');
                }
            }

            // Refresh daftar file backup
            $this->loadBackupFiles();

            // Notifikasi sukses
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => "Backup otomatis berhasil dijalankan"
            ]);

        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal menjalankan backup otomatis: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal menjalankan backup otomatis: " . $e->getMessage(), 'error');
        }
    }

    /**
     * Hapus file backup
     */
    public function deleteBackup($filename)
    {
        try {
            $filePath = storage_path("backupdb/{$filename}");

            if (File::exists($filePath)) {
                File::delete($filePath);

                // Refresh daftar file backup
                $this->loadBackupFiles();

                // Notifikasi sukses
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => "File backup berhasil dihapus: {$filename}"
                ]);

                // Log ke terminal
                $this->addToConsoleLog("File backup berhasil dihapus: {$filename}", 'success');
            } else {
                throw new \Exception("File backup tidak ditemukan: {$filename}");
            }
        } catch (\Exception $e) {
            // Notifikasi error
            $this->dispatch('notify', [
                'type' => 'danger',
                'message' => "Gagal menghapus file backup: " . $e->getMessage()
            ]);

            // Log ke terminal
            $this->addToConsoleLog("Gagal menghapus file backup: " . $e->getMessage(), 'error');
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperTransaksi
 */
class Transaksi extends Model
{
    public function getTable()
    {
         return config('tabel.t_transaksi.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_transaksi.kolom', []);
    }

   public function penjualan()
    {
        return $this->belongsTo(Penjualan::class, 'penjualan_id', 'id');
    }

  


public function totalHarga(): float
{
    return $this->jumlah * $this->harga;
}

}

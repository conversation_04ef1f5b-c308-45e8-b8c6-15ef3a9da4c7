<?php

namespace App\Models;

use <PERSON><PERSON>chin\FilamentMenuBuilder\Models\Menu as BaseMenu;
use Datlechin\FilamentMenuBuilder\Models\MenuItem;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperMenu
 */
class Menu extends BaseMenu
{
   


    // Pada Model Menu
public function children()
{
    return $this->hasMany(MenuItem::class, 'parent_id')->orderBy('order');
}

public function parent()
{
    return $this->belongsTo(MenuItem::class, 'parent_id');
}

}

<?php

namespace App\Aplikasi\Hotel\Resources\KonfigResource\Pages;

use App\Aplikasi\Hotel\Resources\KonfigResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditKonfig extends EditRecord
{
    protected static string $resource = KonfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Tambahkan tombol "Save Change" di header
            Actions\Action::make('simpanPerubahan')
                ->label('Simpan ')
                ->color('success')
                ->icon('heroicon-o-check')
                ->action(function () {
                    // Simpan perubahan
                    $this->save();
                }),
            
            // Tombol kembali ke halaman daftar
            Actions\Action::make('kembali')
                ->label('Kembali')
                ->color('gray')
                ->icon('heroicon-o-arrow-left')
                ->url(fn () => KonfigResource::getUrl()),
        ];
    }
}

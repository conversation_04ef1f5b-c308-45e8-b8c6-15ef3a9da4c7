<?php

namespace App\Aplikasi\Hotel\Helpers;

use Illuminate\Support\Arr;

class ConfigHelper
{
    /**
     * Dapatkan daftar jenis identitas tamu
     *
     * @return array
     */
    public static function getJenisIdentitas(): array
    {
        return config('hotel.jenis_identitas', []);
    }

    /**
     * Dapatkan daftar referensi reservasi
     *
     * @return array
     */
    public static function getReferensi(): array
    {
        return config('hotel.referensi', []);
    }

    /**
     * Dapatkan daftar fasilitas kamar
     *
     * @return array
     */
    public static function getFasilitasKamar(): array
    {
        return config('hotel.fasilitas_kamar', []);
    }

    /**
     * Dapatkan daftar status reservasi
     *
     * @param string|null $kode Kode status (opsional)
     * @return array|string
     */
    public static function getReservasiStatus(string $kode = null)
    {
        $status = config('hotel.reservasi_status', []);
        
        if ($kode !== null) {
            return $status[$kode] ?? $kode;
        }
        
        return $status;
    }

    /**
     * Dapatkan kode status reservasi dari nama status
     *
     * @param string $nama Nama status
     * @return string|null
     */
    public static function getReservasiStatusCode(string $nama): ?string
    {
        $status = config('hotel.reservasi_status', []);
        $flipped = array_flip($status);
        
        return $flipped[$nama] ?? null;
    }

    /**
     * Dapatkan daftar spesifikasi kamar
     *
     * @return array
     */
    public static function getSpesifikasiKamar(): array
    {
        return config('hotel.spesifikasi_kamar', []);
    }

    /**
     * Dapatkan nilai biaya layanan
     *
     * @param string|null $jenis Jenis biaya (pajak, servis, diskon)
     * @param float $default Nilai default jika tidak ditemukan
     * @return array|float
     */
    public static function getBiayaLayanan(string $jenis = null, float $default = 0)
    {
        $biaya = config('hotel.biaya_layanan', []);
        
        if ($jenis !== null) {
            return isset($biaya[$jenis]) ? (float)$biaya[$jenis] : $default;
        }
        
        return $biaya;
    }

    /**
     * Dapatkan daftar jadwal shift
     *
     * @return array
     */
    public static function getJadwalShift(): array
    {
        return config('hotel.jadwal_shift', []);
    }

    /**
     * Dapatkan daftar status pembayaran
     *
     * @return array
     */
    public static function getPembayaranStatus(): array
    {
        return config('hotel.pembayaran_status', []);
    }

    /**
     * Dapatkan konfigurasi printer kasir
     *
     * @param string|null $key Kunci konfigurasi
     * @param mixed $default Nilai default jika tidak ditemukan
     * @return array|mixed
     */
    public static function getPrinterKasir(string $key = null, $default = null)
    {
        $printer = config('hotel.printer_kasir', []);
        
        if ($key !== null) {
            return $printer[$key] ?? $default;
        }
        
        return $printer;
    }

    /**
     * Dapatkan daftar meja
     *
     * @param string|null $lokasi Filter berdasarkan lokasi (dalam/luar)
     * @return array
     */
    public static function getMeja(string $lokasi = null): array
    {
        $meja = config('hotel.meja', []);
        
        if ($lokasi !== null) {
            return array_filter($meja, function($value) use ($lokasi) {
                return $value === $lokasi;
            });
        }
        
        return $meja;
    }

    /**
     * Dapatkan daftar jenis pesanan
     *
     * @return array
     */
    public static function getJenisPesanan(): array
    {
        return config('hotel.jenis_pesanan', []);
    }

    /**
     * Ubah array menjadi array asosiatif untuk opsi dropdown
     *
     * @param array $array Array sederhana
     * @return array Array asosiatif (key => value sama)
     */
    public static function arrayToOptions(array $array): array
    {
        return array_combine($array, $array);
    }

    /**
     * Hitung total dengan biaya layanan
     *
     * @param float $subtotal Subtotal
     * @param bool $includePajak Sertakan pajak
     * @param bool $includeServis Sertakan biaya servis
     * @param bool $includeDiskon Sertakan diskon
     * @return array Detail perhitungan
     */
    public static function hitungTotalDenganLayanan(
        float $subtotal, 
        bool $includePajak = true, 
        bool $includeServis = true, 
        bool $includeDiskon = true
    ): array {
        $pajak = 0;
        $servis = 0;
        $diskon = 0;
        
        if ($includePajak) {
            $pajakPersen = self::getBiayaLayanan('pajak', 0);
            $pajak = $subtotal * ($pajakPersen / 100);
        }
        
        if ($includeServis) {
            $servisPersen = self::getBiayaLayanan('servis', 0);
            $servis = $subtotal * ($servisPersen / 100);
        }
        
        if ($includeDiskon) {
            $diskonPersen = self::getBiayaLayanan('diskon', 0);
            $diskon = $subtotal * ($diskonPersen / 100);
        }
        
        $total = $subtotal + $pajak + $servis - $diskon;
        
        return [
            'subtotal' => $subtotal,
            'pajak' => $pajak,
            'pajak_persen' => self::getBiayaLayanan('pajak', 0),
            'servis' => $servis,
            'servis_persen' => self::getBiayaLayanan('servis', 0),
            'diskon' => $diskon,
            'diskon_persen' => self::getBiayaLayanan('diskon', 0),
            'total' => $total,
        ];
    }

    /**
     * Format harga ke format rupiah
     *
     * @param float $amount Jumlah
     * @param bool $withSymbol Tampilkan simbol Rp
     * @return string Harga format rupiah
     */
    public static function formatRupiah(float $amount, bool $withSymbol = true): string
    {
        $formatted = number_format($amount, 0, ',', '.');
        
        return $withSymbol ? "Rp {$formatted}" : $formatted;
    }

    /**
     * Memeriksa apakah fasilitas valid (ada dalam daftar)
     *
     * @param string $fasilitas Fasilitas yang diperiksa
     * @return bool
     */
    public static function isFasilitasValid(string $fasilitas): bool
    {
        return in_array($fasilitas, self::getFasilitasKamar());
    }

    /**
     * Memeriksa apakah spesifikasi valid (ada dalam daftar)
     *
     * @param string $spesifikasi Spesifikasi yang diperiksa
     * @return bool
     */
    public static function isSpesifikasiValid(string $spesifikasi): bool
    {
        return in_array($spesifikasi, self::getSpesifikasiKamar());
    }

    /**
     * Dapatkan daftar status housekeeping
     *
     * @return array
     */
    public static function getStatusHousekeeping(): array
    {
        return [
            'bersih' => 'Bersih',
            'kotor' => 'Kotor',
            'maintenance' => 'Dalam Perbaikan',
            'belum-diperiksa' => 'Belum Diperiksa'
        ];
    }

    /**
     * Dapatkan nilai dari konfigurasi dengan path dot notation
     *
     * @param string $key Path konfigurasi dengan dot notation
     * @param mixed $default Nilai default jika tidak ditemukan
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        return config('hotel.' . $key, $default);
    }


     public static function jenisPembayaran(bool $withLabels = true, bool $asCollection = true)
    {
        // Dapatkan daftar jenis pembayaran dari config
        $jenisPembayaran = array_keys(config('hotel.info_pengirim', []));
        
        // Perbaiki label untuk tampilan yang lebih baik
        $labelPembayaran = [
            'transfer' => 'Transfer Bank',
            'kartukredit' => 'Kartu Kredit',
            'tunai' => 'Tunai',
            'debit' => 'Kartu Debit',
            'qris' => 'QRIS',
            'custom' => 'Kustom',
            'custom2' => 'Kustom 2',
            'cobax' => 'Coba X'
        ];
        
        // Buat array untuk options
        $hasil = [];
        
        if ($withLabels) {
            // Gunakan label yang diperbaiki
            foreach ($jenisPembayaran as $jenis) {
                $hasil[$jenis] = $labelPembayaran[$jenis] ?? ucfirst($jenis);
            }
        } else {
            // Gunakan nilai asli
            $hasil = array_combine($jenisPembayaran, $jenisPembayaran);
        }
        
        // Kembalikan hasil
        if ($asCollection) {
            return collect($hasil);
        }
        
        return $hasil;
    }
    
}
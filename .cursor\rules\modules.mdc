---
description: saat membuat module baru / saat saya menyuruh memindahkan resource atau file ke dalam modul / menambahakan fitur untuk modul
globs: 
alwaysApply: false
---
# Aturan khusus direktori Modules

- Direktori `Modules` menggunakan package `tomatophp/filament-plugins` dan `nwidart/laravel-modules`.
- <PERSON><PERSON> pernah menghapus file atau folder di dalam `Modules` tanpa konfirmasi eksplisit dari pengguna.
- Untuk membuat modul baru gunakan:
  ```bash
  php artisan filament-plugins:generate {NamaPlugin}
  ```
- Struktur wajib modul:

  ```
  Modules/{NamaPlugin}/
  ├── Config/
  │   └── config.php
  ├── Database/
  │   ├── Migrations/
  │   └── Seeders/
  ├── Http/
  │   └── Controllers/
  ├── Providers/
  │   └── {NamaPlugin}ServiceProvider.php
  ├── Resources/
  │   ├── Views/
  │   └── Lang/
  ├── Routes/
  │   └── web.php
  ├── Filament/
  │   ├── Pages/
  │   ├── Widgets/
  │   └── Resources/
  └── module.json
  ```

- <PERSON><PERSON>h modul dibuat, jalankan `composer dump-autoload` untuk memuat autoload PSR-4 dari `module.json`.
- Logika bisnis khusus modul diletakkan di dalam modul; hindari penempatan di luar direktori `Modules`.
- Migration modul berada di `Database/Migrations/`, dilarang mengubah migration modul lain.
- Seeder modul berada di `Database/Seeders/`, pisahkan data dummy dan data produksi.
- Route modul hanya di `Routes/web.php`; jangan menambah route modul ke `routes/web.php` aplikasi utama.
- Gunakan helper `module_path()` atau fungsi Filament-Plugins saat merujuk file di dalam modul; hindari hard-coding namespace atau path.
- Akses konfigurasi modul menggunakan `config("{alias_module}::key")`.
- Sebelum menjalankan `artisan migrate` atau perintah serupa, pastikan modul di-enable pada konfigurasi Filament-Plugins.
- Setiap perubahan besar harus diperbarui di `CHANGELOG.md` modul dan versi dinaikkan secara semantik.
- Jangan simpan kredensial atau data sensitif di dalam modul; gunakan `.env` dan mekanisme publish config Laravel.
- Pastikan seluruh `ServiceProvider` modul terdaftar di `module.json` untuk auto-discovery.
- Semua resource (controller, model, Filament resource, page, widget, dsb.) di modul telah auto-discovery; hindari pendaftaran manual.
- Setiap modul baru wajib memiliki `Modules/{NamaPlugin}/dokumentasi/README.md` berisi tujuan, instalasi, dan contoh penggunaan.
- Setelah modul selesai dibuat tanpa error, tanyakan kepada pengguna: *"Apakah Anda ingin membuat dokumentasi?"* jika belum ada dokumentasi.



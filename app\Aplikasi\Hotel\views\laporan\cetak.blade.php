<!DOCTYPE html>
<html lang="id">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .fi-topbar { display: none;}
        .fi-sidebar { display: none;}
        /* Styling dasar */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 5px;
            font-size: 12px;
        }
        
        /* Header laporan */
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ddd;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        /* Informasi filter */
        .info-container {
            margin-bottom: 15px;
        }
        
        .info-section {
            margin-bottom: 10px;
        }
        
        .info-section-title {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        /* Styling tabel */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th {
            background-color: #f0f0f0;
            text-align: left;
            padding: 8px;
            font-size: 11px;
            border: 1px solid #ddd;
        }
        
        td {
            padding: 6px 8px;
            border: 1px solid #ddd;
            font-size: 10px;
        }
        
        .zebra-row:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        /* Ringkasan data */
        .summary {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .summary-title {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .summary-table {
            width: 50%;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 4px;
            font-size: 11px;
            border: none;
        }
        
        .summary-table .label {
            width: 50%;
            font-weight: bold;
        }
        
        /* Label status */
        .status-label {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            display: inline-block;
        }
        
        .status-bk {
            background-color: #3b82f6;
            color: white;
        }
        
        .status-sci {
            background-color: #22c55e;
            color: white;
        }
        
        .status-sco {
            background-color: #ef4444;
            color: white;
        }
        
        .lunas {
            background-color: #22c55e;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            display: inline-block;
        }
        
        /* Footer */
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        /* Tombol-tombol aksi */
        .action-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 12px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-print {
            background-color: #22c55e;
        }
        
        .btn-back {
            background-color: #6b7280;
        }
        
        /* Hanya tampilkan saat di layar (tidak saat print) */
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                padding: 0;
                font-size: 12px;
            }
            
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
<div id="laporan-container">
    <!-- Tombol-tombol aksi yang hanya muncul di layar -->
    <div class="action-buttons no-print">
        <button onclick="cetakHalaman()" class="btn btn-print">Cetak Sekarang</button>
        <button onclick="window.history.back()" class="btn btn-back">Kembali</button>
    </div>
    
    <div class="header">
        <h1 class="title">LAPORAN RESERVASI HOTEL</h1>
        <div class="subtitle">{{ date('d F Y', strtotime(now())) }}</div>
    </div>
    
    <div class="info-container">
        <!-- Filter Tanggal Reservasi -->
        <div class="info-section">
            <div class="info-section-title">Filter Tanggal Reservasi</div>
            <div class="info-grid">
                <div class="info-item"><strong>Periode:</strong> {{ $filters['tanggal_mulai'] ?? '-' }} s/d {{ $filters['tanggal_akhir'] ?? '-' }}</div>
                <div class="info-item"><strong>Bulan:</strong> {{ $filters['bulan'] ?? 'Semua Bulan' }}</div>
                <div class="info-item"><strong>Tahun:</strong> {{ $filters['tahun'] ?? 'Semua Tahun' }}</div>
            </div>
        </div>
        
        <!-- Filter Check-in/Check-out -->
        <div class="info-section">
            <div class="info-section-title">Filter Check-in/Check-out</div>
            <div class="info-grid">
                <div class="info-item"><strong>Check-in:</strong> {{ $filters['checkin_dari'] ?? '-' }} s/d {{ $filters['checkin_sampai'] ?? '-' }}</div>
                <div class="info-item"><strong>Check-out:</strong> {{ $filters['checkout_dari'] ?? '-' }} s/d {{ $filters['checkout_sampai'] ?? '-' }}</div>
            </div>
        </div>
        
        <!-- Filter Status -->
        <div class="info-section">
            <div class="info-section-title">Filter Status</div>
            <div class="info-grid">
                <div class="info-item"><strong>Status Reservasi:</strong> {{ $filters['status_reservasi'] ?? 'Semua Status' }}</div>
                <div class="info-item"><strong>Status Pembayaran:</strong> {{ $filters['status_pembayaran'] ?? 'Semua Status' }}</div>
            </div>
        </div>
        
        <!-- Filter Pengguna -->
        <div class="info-section">
            <div class="info-section-title">Filter Pengguna</div>
            <div class="info-grid">
                <div class="info-item"><strong>Tamu:</strong> {{ $filters['tamu'] ?? 'Semua Tamu' }}</div>
                <div class="info-item"><strong>Petugas:</strong> {{ $filters['petugas'] ?? 'Semua Petugas' }}</div>
            </div>
        </div>
    </div>
    
    <!-- Cek apakah variabel $columns tersedia, jika tidak, berikan array kosong -->
    @php
        $columns = $columns ?? [];
        $rows = $rows ?? [];
        $summary = $summary ?? [];
    @endphp
    
    <table>
        <thead>
            <tr>
                <th style="width: 30px;">No</th>
                @foreach($columns as $key => $label)
                    <th>{{ $label }}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @if(count($rows) > 0)
                @foreach($rows as $index => $row)
                    <tr class="zebra-row">
                        <td style="text-align: center;">{{ $index + 1 }}</td>
                        @foreach($columns as $key => $label)
                            @if($key == 'status')
                                <td>
                                    @if(isset($row[$key]))
                                        {{ $row[$key] }}
                                    @else
                                        -
                                    @endif
                                </td>
                            @elseif($key == 'sisa_tagihan' && isset($row[$key]) && $row[$key] == 'LUNAS')
                                <td> LUNAS </td>
                            @else
                                <td>{{ $row[$key] ?? '-' }}</td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="{{ count($columns) + 1 }}" style="text-align: center;">Tidak ada data yang ditemukan</td>
                </tr>
            @endif
        </tbody>
    </table>
    
    <div class="summary">
        <div class="summary-title">RINGKASAN</div>
        <table class="summary-table">
            <tr>
                <td class="label">Total Reservasi:</td>
                <td>{{ count($rows) }} Reservasi</td>
            </tr>
            @if(isset($columns['durasi']))
                <tr>
                    <td class="label">Total Durasi:</td>
                    <td>{{ $summary['durasi'] ?? '0 Malam' }}</td>
                </tr>
            @endif
            @if(isset($columns['pajak']))
                <tr>
                    <td class="label">Total Pajak:</td>
                    <td>{{ $summary['pajak'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['diskon']))
                <tr>
                    <td class="label">Total Diskon:</td>
                    <td>{{ $summary['diskon'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['servis']))
                <tr>
                    <td class="label">Total Servis:</td>
                    <td>{{ $summary['servis'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['modal']))
                <tr>
                    <td class="label">Total Modal:</td>
                    <td>{{ $summary['modal'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['bersih']))
                <tr>
                    <td class="label">Total Harga Bersih:</td>
                    <td>{{ $summary['bersih'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['keuntungan']))
                <tr>
                    <td class="label">Total Keuntungan:</td>
                    <td>{{ $summary['keuntungan'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['total_akhir']))
                <tr>
                    <td class="label">Total Tagihan:</td>
                    <td>{{ $summary['total_akhir'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['pembayaran']))
                <tr>
                    <td class="label">Total Pembayaran:</td>
                    <td>{{ $summary['pembayaran'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
            @if(isset($columns['sisa_tagihan']))
                <tr>
                    <td class="label">Total Sisa Tagihan:</td>
                    <td>{{ $summary['sisa_tagihan'] ?? 'Rp. 0' }}</td>
                </tr>
            @endif
        </table>
    </div>
    
    <div class="footer">
        <p>Dicetak oleh: {{ auth()->user()->name ?? 'Admin' }} pada {{ date('d/m/Y H:i') }}</p>
    </div>
    
    <!-- Script untuk auto print jika diperlukan -->
    <script>
        // Fungsi untuk mencetak halaman
        function cetakHalaman() {
            try {
                // Coba cetak halaman
                window.print();
                console.log("Perintah cetak dijalankan");
            } catch (e) {
                // Jika terjadi error, tampilkan di console
                console.error("Error saat mencetak:", e);
                alert("Gagal memunculkan dialog cetak. Silakan klik tombol 'Cetak Sekarang' secara manual.");
            }
        }
        
        // Registrasi event listener untuk tombol
        document.addEventListener("DOMContentLoaded", function() {
            // Ambil referensi ke tombol cetak
            var btnPrint = document.querySelector(".btn-print");
            if (btnPrint) {
                btnPrint.addEventListener("click", function(e) {
                    e.preventDefault();
                    cetakHalaman();
                });
            }
            
            // Auto print jika diperlukan
            @if(isset($auto_print) && $auto_print)
            // Delay yang lebih lama untuk memastikan halaman benar-benar siap
            setTimeout(function() {
                console.log("Mencoba auto-print...");
                cetakHalaman();
            }, 1000); // Tambah delay menjadi 1 detik
            @endif
        });
    </script>
</div><!-- Akhir dari #laporan-container -->
</body>
</html>
<?php

namespace App\Aplikasi\Hotel\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Artisan;
use Filament\Notifications\Notification;
use App\Aplikasi\Hotel\Models\Tamu;
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Models\Fasilitas;
use App\Aplikasi\Hotel\Models\Transaksi;
use App\Aplikasi\Hotel\Models\HargaJual;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Models\Pembayaran;

class DataPage extends Page
{
   // Konfigurasi halaman
   protected static ?string $navigationIcon = 'heroicon-o-archive-box';

   protected static string $view = 'hotel::pages.data-page';
   protected static bool $shouldRegisterNavigation = false;

   protected static ?string $title = 'Manager Data';

   protected static ?string $navigationLabel = 'Manager Data';

   protected ?string $heading = 'Manager Data';

   protected static ?int $navigationSort = 90;

   protected static ?string $navigationGroup = 'Pengaturan';
   // Property untuk menyimpan bulan dan tahun
   public $selectedBulan = null;
   public $selectedTahun = null;


   // Property untuk refresh otomatis
   protected $listeners = ['refreshPage' => '$refresh'];

   // Property untuk menyimpan state
   public $activeTab = 'tables';
   public $seederJumlah = [];

   // Property untuk menyimpan jumlah data untuk tab Reset
   public $resetDataCounts = [];

   // Property untuk menangani loading state
   public $isLoading = false;
   public $currentSeeder = null;

   // Property untuk console log
   public $consoleLog = [];
   public $showConsole = false;


   // Daftar bulan untuk dropdown
   public $daftarBulan = [
      1 => 'Januari',
      2 => 'Februari',
      3 => 'Maret',
      4 => 'April',
      5 => 'Mei',
      6 => 'Juni',
      7 => 'Juli',
      8 => 'Agustus',
      9 => 'September',
      10 => 'Oktober',
      11 => 'November',
      12 => 'Desember'
   ];

   // Daftar tahun akan diisi secara dinamis
   public $daftarTahun = [];


   // Inisialisasi saat halaman dimuat
   public function mount()
   {
      // Menginisialisasi default jumlah data seeder
      $this->initSeederJumlah();
      // Inisialisasi bulan saat ini
      $this->selectedBulan = intval(date('m'));

      // Inisialisasi daftar tahun (10 tahun terakhir)
      $tahunSekarang = intval(date('Y'));
      for ($i = 0; $i < 10; $i++) {
         $tahun = $tahunSekarang - $i;
         $this->daftarTahun[$tahun] = $tahun;
      }

      // Set tahun default ke tahun sekarang
      $this->selectedTahun = $tahunSekarang;

      // Hitung jumlah data untuk tab Reset
      $this->updateResetDataCounts();
   }

   /**
    * Menghitung jumlah data untuk tab Reset
    */
   public function updateResetDataCounts()
   {
      // Hitung jumlah data untuk reservasi dan transaksi
      try {
         $transaksiCount = Transaksi::count();
         $pembayaranCount = Pembayaran::count();
         $this->resetDataCounts['reservasi'] = $transaksiCount + $pembayaranCount;
      } catch (\Exception) {
         $this->resetDataCounts['reservasi'] = 0;
      }

      // Hitung jumlah data untuk kamar dan tipe kamar
      try {
         $kamarCount = Kamar::count();
         $tipeKamarCount = KamarTipe::count();
         $this->resetDataCounts['kamar'] = $kamarCount + $tipeKamarCount;
      } catch (\Exception) {
         $this->resetDataCounts['kamar'] = 0;
      }

      // Hitung jumlah data tamu
      try {
         $this->resetDataCounts['tamu'] = Tamu::count();
      } catch (\Exception) {
         $this->resetDataCounts['tamu'] = 0;
      }

      // Hitung jumlah data konfigurasi
      try {
         $this->resetDataCounts['konfigurasi'] = Konfig::count();
      } catch (\Exception) {
         $this->resetDataCounts['konfigurasi'] = 0;
      }

      // Hitung total semua data
      $this->resetDataCounts['total'] =
         $this->resetDataCounts['reservasi'] +
         $this->resetDataCounts['kamar'] +
         $this->resetDataCounts['tamu'] +
         $this->resetDataCounts['konfigurasi'] +
         HargaJual::count() +
         Fasilitas::count();
   }

   // Mengupdate bulan yang dipilih
   public function updateBulan($bulan)
   {
      $this->selectedBulan = (int) $bulan;
   }

   // Mengupdate tahun yang dipilih
   public function updateTahun($tahun)
   {
      $this->selectedTahun = (int) $tahun;
   }

   /**
    * Dipanggil saat tab aktif berubah
    */
   public function updatedActiveTab()
   {
      // Jika tab Reset dipilih, update jumlah data
      if ($this->activeTab === 'reset') {
         $this->updateResetDataCounts();
      }
   }
   // Inisialisasi jumlah data seeder
   protected function initSeederJumlah()
   {
      // Mendapatkan daftar semua seeder
      $seeders = $this->getSeeders();

      // Inisialisasi nilai default
      foreach ($seeders as $seeder) {
         if (! isset($this->seederJumlah[$seeder['nama']])) {
            $this->seederJumlah[$seeder['nama']] = 10; // Default 10 data
         }
      }
   }

   // Mendapatkan data tabel
   public function getTables()
   {
      $tableData = [];

      // Kamar (produk dengan jenis KAMAR)
      try {
         $count = Kamar::count();
         $tableData[] = [
            'nama' => 'kamar',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'kamar',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // KamarTipe (categories dengan jenis KAMAR)
      try {
         $count = KamarTipe::count();
         $tableData[] = [
            'nama' => 'tipe_kamar',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'tipe_kamar',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // Fasilitas (produk dengan jenis FASILITAS)
      try {
         $count = Fasilitas::count();
         $tableData[] = [
            'nama' => 'fasilitas',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'fasilitas',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // Transaksi
      try {
         $count = Transaksi::count();
         $tableData[] = [
            'nama' => 'transaksi',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'transaksi',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // HargaJual
      try {
         $count = HargaJual::count();
         $tableData[] = [
            'nama' => 'harga_jual',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'harga_jual',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // Konfig
      try {
         $count = Konfig::count();
         $tableData[] = [
            'nama' => 'konfig',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'konfig',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // Pembayaran
      try {
         $count = Pembayaran::count();
         $tableData[] = [
            'nama' => 'pembayaran',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'pembayaran',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      // Tamu
      try {
         $count = Tamu::count();
         $tableData[] = [
            'nama' => 'tamu',
            'jumlah' => $count,
         ];
      } catch (\Exception) {
         $tableData[] = [
            'nama' => 'tamu',
            'jumlah' => 'Tabel tidak ditemukan',
         ];
      }

      return $tableData;
   }

   public function kosongkanTabel($tableName)
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = $tableName;

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses pengosongan tabel {$tableName}...", 'info');

      try {
         // Nonaktifkan foreign key check sementara untuk menghindari error constraint
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Penanganan berdasarkan nama tabel menggunakan model yang sesuai
         switch ($tableName) {
            case 'tamu':
               $this->kosongkanModelData(Tamu::class, 'tamu');
               break;

            case 'kamar':
               $this->kosongkanModelData(Kamar::class, 'kamar');
               break;

            case 'tipe_kamar':
               $this->kosongkanModelData(KamarTipe::class, 'tipe kamar');
               break;

            case 'fasilitas':
               $this->kosongkanModelData(Fasilitas::class, 'fasilitas');
               break;

            case 'transaksi':
               $this->kosongkanModelData(Transaksi::class, 'transaksi');
               break;

            case 'harga_jual':
               $this->kosongkanModelData(HargaJual::class, 'harga jual');
               break;

            case 'konfig':
               $this->kosongkanModelData(Konfig::class, 'konfigurasi');
               break;

            case 'pembayaran':
               $this->kosongkanModelData(Pembayaran::class, 'pembayaran');
               break;

            default:
               // Untuk tabel lain yang tidak memiliki model khusus
               // Cek dulu apakah tabel ada
               if (! DB::getSchemaBuilder()->hasTable($tableName)) {
                  $this->addToConsoleLog("Tabel {$tableName} tidak ditemukan dalam database", 'error');

                  Notification::make()
                     ->title('Tabel tidak ditemukan')
                     ->body("Tabel {$tableName} tidak ditemukan dalam database")
                     ->danger()
                     ->send();
                  return;
               }

               // Hitung jumlah data sebelum dikosongkan
               $jumlahDataAwal = DB::table($tableName)->count();
               $this->addToConsoleLog("Jumlah data awal di tabel {$tableName}: {$jumlahDataAwal} baris", 'info');

               // Kosongkan tabel
               $this->addToConsoleLog("Menghapus semua data dari tabel {$tableName}...", 'warning');
               DB::table($tableName)->truncate();

               // Verifikasi pengosongan tabel
               $jumlahDataAkhir = DB::table($tableName)->count();
               $this->addToConsoleLog("Jumlah data akhir di tabel {$tableName}: {$jumlahDataAkhir} baris", 'success');
               $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data", 'success');

               Notification::make()
                  ->title('Tabel berhasil dikosongkan')
                  ->body("Semua data dalam tabel {$tableName} telah dihapus")
                  ->success()
                  ->send();
               break;
         }

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal mengosongkan tabel')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state setelah selesai (baik berhasil maupun gagal)
         $this->isLoading = false;
         $this->currentSeeder = null;

         $this->addToConsoleLog("Proses pengosongan tabel selesai", 'info');
      }
   }

   /**
    * Metode helper untuk mengosongkan data model
    *
    * @param string $modelClass Nama class model
    * @param string $displayName Nama tampilan untuk tabel
    */
   private function kosongkanModelData($modelClass, $displayName)
   {
      // Hitung jumlah data sebelum dikosongkan
      $jumlahDataAwal = $modelClass::count();
      $this->addToConsoleLog("Jumlah data awal {$displayName}: {$jumlahDataAwal} baris", 'info');

      // Kosongkan tabel menggunakan model
      $this->addToConsoleLog("Menghapus semua data {$displayName}...", 'warning');
      $modelClass::truncate();

      // Verifikasi pengosongan tabel
      $jumlahDataAkhir = $modelClass::count();
      $this->addToConsoleLog("Jumlah data akhir {$displayName}: {$jumlahDataAkhir} baris", 'success');
      $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data {$displayName}", 'success');

      Notification::make()
         ->title('Data berhasil dikosongkan')
         ->body("Semua data {$displayName} telah dihapus")
         ->success()
         ->send();
   }

   // Mendapatkan data seeder
   public function getSeeders()
   {
      $path = app_path('Aplikasi/Hotel/Models/Seeders');

      if (! File::exists($path)) {
         return [];
      }

      $files = File::files($path);
      $seeders = [];

      foreach ($files as $file) {
         $filename = pathinfo($file, PATHINFO_FILENAME);

         $seeders[] = [
            'nama' => $filename,
         ];
      }

      return $seeders;
   }

   // Mengupdate jumlah data untuk seeder
   public function updateJumlah($nama, $jumlah)
   {
      $this->seederJumlah[$nama] = (int) $jumlah;
   }

   // Menjalankan seeder
   /**
    * Menjalankan seeder yang dipilih
    *
    * @param string $nama Nama seeder yang akan dijalankan
    * @return void
    */
   public function runSeeder($nama)
   {
      // Set status loading menjadi aktif
      $this->isLoading = true;
      $this->currentSeeder = $nama;

      // Bersihkan log terminal sebelum memulai proses baru
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses seeding untuk {$nama}...", 'info');

      try {
         // Ambil jumlah data yang diinginkan dari input, default 10
         $jumlah = $this->seederJumlah[$nama] ?? 10;

         // Tentukan namespace class seeder yang akan dijalankan
         $seederClass = "App\\Aplikasi\\Hotel\\Models\\Seeders\\{$nama}";

         // Cek apakah class seeder ada
         if (!class_exists($seederClass)) {
            $this->addToConsoleLog("Class seeder {$seederClass} tidak ditemukan", 'error');
            throw new \Exception("Class seeder {$seederClass} tidak ditemukan");
         }

         // Tampilkan informasi konfigurasi seeder
         $this->addToConsoleLog("Menyiapkan seeder {$nama} untuk menambahkan {$jumlah} data", 'info');
         $this->addToConsoleLog("Menggunakan bulan: {$this->daftarBulan[$this->selectedBulan]} dan tahun: {$this->selectedTahun}", 'info');

         // Set variabel lingkungan untuk digunakan oleh seeder
         putenv("SEEDER_COUNT={$jumlah}");
         putenv("SEEDER_MONTH={$this->selectedBulan}");
         putenv("SEEDER_YEAR={$this->selectedTahun}");

         // Mulai buffer output untuk menangkap hasil eksekusi Artisan
         ob_start();

         $this->addToConsoleLog("Menjalankan seeder, mohon tunggu...", 'info');

         // Jalankan seeder menggunakan perintah Artisan
         Artisan::call('db:seed', [
            '--class' => $seederClass,
            '--force' => true,    // Paksa jalankan seeder tanpa konfirmasi
            '--verbose' => true,  // Tampilkan output detail
         ]);

         // Ambil output yang dihasilkan oleh Artisan
         $output = ob_get_clean();

         // Proses output dan tampilkan di terminal
         $outputLines = explode("\n", $output);
         foreach ($outputLines as $line) {
            if (!empty(trim($line))) {
               $this->addToConsoleLog(trim($line), 'info');
            }
         }

         // Tambah delay kecil untuk memastikan semua proses selesai
         $this->addToConsoleLog("Memproses data...", 'info');
         sleep(1);

         // Tampilkan pesan berhasil
         $this->addToConsoleLog("Seeder berhasil dijalankan! Menambahkan {$jumlah} data.", 'success');

         // Kirim notifikasi sukses ke layar
         Notification::make()
            ->title('Seeder berhasil dijalankan')
            ->body("Berhasil menambahkan {$jumlah} data dengan {$nama}")
            ->success()
            ->send();
      } catch (\Exception $e) {
         // Tangkap error yang terjadi dan tampilkan di terminal
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         // Kirim notifikasi error
         Notification::make()
            ->title('Gagal menjalankan seeder')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset status loading setelah proses selesai
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Tambahkan log selesai
         $this->addToConsoleLog("Proses seeding selesai.", 'info');
      }
   }

   // Menghapus data dari seeder
   public function hapusData($nama)
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = $nama;

      try {
         // Mendapatkan model dari nama seeder
         $modelName = str_replace('Seeder', '', $nama);

         // Coba menentukan namespace model yang benar
         $modelClass = "App\\Aplikasi\\Hotel\\Models\\" . $modelName;

         // Periksa apakah model ada
         if (class_exists($modelClass)) {
            // Nonaktifkan foreign key check sementara
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Hapus data model
            $modelClass::query()->truncate();

            // Aktifkan kembali foreign key check
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            Notification::make()
               ->title('Data berhasil dihapus')
               ->body("Semua data {$modelName} telah dihapus")
               ->success()
               ->send();
         } else {
            // Coba pendekatan alternatif dengan nama tabel umum
            $tableName = strtolower($modelName);

            // Nonaktifkan foreign key check sementara
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Hapus data dari tabel
            DB::table($tableName)->truncate();

            // Aktifkan kembali foreign key check
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            Notification::make()
               ->title('Data berhasil dihapus')
               ->body("Semua data dari tabel {$tableName} telah dihapus")
               ->success()
               ->send();
         }
      } catch (\Exception $e) {
         Notification::make()
            ->title('Gagal menghapus data')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state setelah selesai (baik berhasil maupun gagal)
         $this->isLoading = false;
         $this->currentSeeder = null;
      }
   }

   public function addToConsoleLog($message, $type = 'info')
   {
      // Tambahkan timestamp ke pesan
      $timestamp = now()->format('H:i:s');

      // Tampung maksimal 100 baris log untuk performa
      if (count($this->consoleLog) > 100) {
         // Hapus 20 log paling lama jika sudah mencapai 100
         $this->consoleLog = array_slice($this->consoleLog, 20);
      }

      // Tambahkan pesan ke console log
      $this->consoleLog[] = [
         'timestamp' => $timestamp,
         'message' => $message,
         'type' => $type
      ];

      // Tampilkan console jika belum ditampilkan
      $this->showConsole = true;
   }

   public function clearConsoleLog()
   {
      $this->consoleLog = [];
   }

   public function toggleConsole()
   {
      $this->showConsole = ! $this->showConsole;
   }

   /**
    * Reset data reservasi dan transaksi terkait
    */
   public function resetReservasi()
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = 'Reservasi';

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses reset data reservasi...", 'info');

      try {
         // Nonaktifkan foreign key check sementara
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Hapus data dari tabel-tabel terkait reservasi
         $tables = ['reservasi', 'pembayaran', 'transaksi'];

         foreach ($tables as $table) {
            $this->addToConsoleLog("Menghapus data dari tabel {$table}...", 'warning');

            try {
               // Hitung jumlah data sebelum dikosongkan
               $jumlahDataAwal = DB::table($table)->count();
               $this->addToConsoleLog("Jumlah data awal di tabel {$table}: {$jumlahDataAwal} baris", 'info');

               // Kosongkan tabel
               DB::table($table)->truncate();

               // Verifikasi pengosongan tabel
               $jumlahDataAkhir = DB::table($table)->count();
               $this->addToConsoleLog("Jumlah data akhir di tabel {$table}: {$jumlahDataAkhir} baris", 'success');
               $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data dari tabel {$table}", 'success');
            } catch (\Exception $e) {
               $this->addToConsoleLog("Error saat menghapus data dari tabel {$table}: " . $e->getMessage(), 'error');
            }
         }

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');

         $this->addToConsoleLog("Reset data reservasi berhasil dilakukan", 'success');

         Notification::make()
            ->title('Reset data reservasi berhasil')
            ->body('Semua data reservasi dan transaksi terkait telah dihapus')
            ->success()
            ->send();
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal melakukan reset data reservasi')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Update jumlah data untuk tab Reset
         $this->updateResetDataCounts();
      }
   }

   /**
    * Reset data kamar dan tipe kamar
    */
   public function resetKamar()
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = 'Kamar';

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses reset data kamar dan tipe kamar...", 'info');

      try {
         // Nonaktifkan foreign key check sementara
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Hapus data kamar
         $this->addToConsoleLog("Menghapus data kamar...", 'warning');
         try {
            // Hitung jumlah data sebelum dikosongkan
            $jumlahDataAwal = Kamar::count();


            $this->addToConsoleLog("Jumlah data awal kamar: {$jumlahDataAwal} baris", 'info');

            // Kosongkan tabel
            Kamar::truncate();

            // Verifikasi pengosongan tabel
            $jumlahDataAkhir = Kamar::count();
            $this->addToConsoleLog("Jumlah data akhir kamar: {$jumlahDataAkhir} baris", 'success');
            $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data kamar", 'success');
         } catch (\Exception $e) {
            $this->addToConsoleLog("Error saat menghapus data kamar: " . $e->getMessage(), 'error');
         }

         // Hapus data tipe kamar
         $this->addToConsoleLog("Menghapus data tipe kamar...", 'warning');
         try {
            // Hitung jumlah data sebelum dikosongkan
            $jumlahDataAwal = KamarTipe::count();
            $this->addToConsoleLog("Jumlah data awal tipe kamar: {$jumlahDataAwal} baris", 'info');

            // Kosongkan tabel
            KamarTipe::truncate();

            // Verifikasi pengosongan tabel
            $jumlahDataAkhir = KamarTipe::count();
            $this->addToConsoleLog("Jumlah data akhir tipe kamar: {$jumlahDataAkhir} baris", 'success');
            $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data tipe kamar", 'success');
         } catch (\Exception $e) {
            $this->addToConsoleLog("Error saat menghapus data tipe kamar: " . $e->getMessage(), 'error');
         }

         // Get kamar dan tipe data from config and execute query
         DB::query(config('reset.kamardantipe'));
         $this->addToConsoleLog("Menjalankan query untuk menambahkan data kamar dan tipe kamar default...", 'info');

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');

         $this->addToConsoleLog("Reset data kamar dan tipe kamar berhasil dilakukan", 'success');

         Notification::make()
            ->title('Reset data kamar berhasil')
            ->body('Semua data kamar dan tipe kamar telah dihapus')
            ->success()
            ->send();
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal melakukan reset data kamar')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Update jumlah data untuk tab Reset
         $this->updateResetDataCounts();
      }
   }

   /**
    * Reset data tamu hotel
    */
   public function resetTamu()
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = 'Tamu';

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses reset data tamu hotel...", 'info');

      try {
         // Nonaktifkan foreign key check sementara
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Hapus data dari tabel tamu
         $this->addToConsoleLog("Menghapus data dari tabel tamu...", 'warning');

         // Hitung jumlah data sebelum dikosongkan
         $jumlahDataAwal = Tamu::count();
         $this->addToConsoleLog("Jumlah data awal di tabel tamu: {$jumlahDataAwal} baris", 'info');

         // Kosongkan tabel
         Tamu::truncate();

         // Verifikasi pengosongan tabel
         $jumlahDataAkhir = Tamu::count();
         $this->addToConsoleLog("Jumlah data akhir di tabel tamu: {$jumlahDataAkhir} baris", 'success');
         $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data dari tabel tamu", 'success');

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');

         $this->addToConsoleLog("Reset data tamu hotel berhasil dilakukan", 'success');

         Notification::make()
            ->title('Reset data tamu berhasil')
            ->body('Semua data tamu hotel telah dihapus')
            ->success()
            ->send();
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal melakukan reset data tamu')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Update jumlah data untuk tab Reset
         $this->updateResetDataCounts();
      }
   }

   /**
    * Reset konfigurasi ke pengaturan default
    */
   public function resetKonfigurasi()
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = 'Konfigurasi';

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses reset konfigurasi...", 'info');

      try {
         // Nonaktifkan foreign key check sementara
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Hapus data dari tabel konfigurasi
         $this->addToConsoleLog("Menghapus data dari tabel konfigurasi...", 'warning');

         // Hitung jumlah data sebelum dikosongkan
         $jumlahDataAwal = Konfig::count();
         $this->addToConsoleLog("Jumlah data awal konfigurasi: {$jumlahDataAwal} baris", 'info');

         // Kosongkan tabel
         Konfig::truncate();

         // Verifikasi pengosongan tabel
         $jumlahDataAkhir = Konfig::count();
         $this->addToConsoleLog("Jumlah data akhir konfigurasi: {$jumlahDataAkhir} baris", 'success');
         $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data konfigurasi", 'success');

         // Jalankan seeder untuk mengisi konfigurasi default
         $this->addToConsoleLog("Menjalankan seeder untuk mengisi konfigurasi default...", 'info');

         // Jalankan seeder konfigurasi default jika ada
         try {
            Artisan::call('db:seed', [
               '--class' => 'App\\Aplikasi\\Hotel\\Models\\Seeders\\KonfigSeeder',
               '--force' => true,
            ]);
            $this->addToConsoleLog("Konfigurasi default berhasil diterapkan", 'success');
         } catch (\Exception $e) {
            $this->addToConsoleLog("Gagal menjalankan seeder konfigurasi default: " . $e->getMessage(), 'error');
         }

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');

         $this->addToConsoleLog("Reset konfigurasi berhasil dilakukan", 'success');

         Notification::make()
            ->title('Reset konfigurasi berhasil')
            ->body('Konfigurasi telah dikembalikan ke pengaturan default')
            ->success()
            ->send();
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal melakukan reset konfigurasi')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Update jumlah data untuk tab Reset
         $this->updateResetDataCounts();
      }
   }

   /**
    * Reset seluruh data aplikasi
    */
   public function resetSeluruhnya()
   {
      // Set loading state
      $this->isLoading = true;
      $this->currentSeeder = 'ResetSeluruhnya';

      // Bersihkan console log
      $this->clearConsoleLog();
      $this->addToConsoleLog("Memulai proses reset seluruh data aplikasi...", 'warning');
      $this->addToConsoleLog("PERINGATAN: Proses ini akan menghapus SEMUA DATA dari aplikasi!", 'warning');

      try {
         // Nonaktifkan foreign key check sementara
         $this->addToConsoleLog("Menonaktifkan foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=0;');

         // Penanganan untuk model-model utama
         $models = [
            Tamu::class => 'tamu',
            Kamar::class => 'kamar',
            KamarTipe::class => 'tipe kamar',
            Fasilitas::class => 'fasilitas',
            Transaksi::class => 'transaksi',
            HargaJual::class => 'harga jual',
            Konfig::class => 'konfigurasi',
            Pembayaran::class => 'pembayaran'
         ];

         foreach ($models as $modelClass => $displayName) {
            $this->addToConsoleLog("Menghapus data {$displayName}...", 'warning');
            try {
               // Hitung jumlah data sebelum dikosongkan
               $jumlahDataAwal = $modelClass::count();
               $this->addToConsoleLog("Jumlah data awal {$displayName}: {$jumlahDataAwal} baris", 'info');

               // Kosongkan tabel
               $modelClass::truncate();

               // Verifikasi pengosongan tabel
               $jumlahDataAkhir = $modelClass::count();
               $this->addToConsoleLog("Jumlah data akhir {$displayName}: {$jumlahDataAkhir} baris", 'success');
               $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data {$displayName}", 'success');
            } catch (\Exception $e) {
               $this->addToConsoleLog("Error saat menghapus data {$displayName}: " . $e->getMessage(), 'error');
            }
         }

         // Dapatkan semua tabel dalam database kecuali yang sudah ditangani oleh model dan tabel sistem
         $tables = DB::select('SHOW TABLES');
         $dbName = 'Tables_in_' . env('DB_DATABASE');
         $excludeTables = [
            // Tabel sistem
            'migrations',
            'users',
            'password_reset_tokens',
            'failed_jobs',
            'personal_access_tokens',
            // Tabel yang sudah ditangani oleh model
            'tamu',
            'produk',
            'categories',
            'transaksi',
            'harga_jual',
            'konfig',
            'pembayaran'
         ];

         foreach ($tables as $table) {
            $tableName = $table->$dbName;

            // Lewati tabel yang dikecualikan
            if (in_array($tableName, $excludeTables)) {
               $this->addToConsoleLog("Melewati tabel {$tableName} (tabel sistem atau sudah diproses)", 'info');
               continue;
            }

            $this->addToConsoleLog("Menghapus data dari tabel {$tableName}...", 'warning');

            try {
               // Hitung jumlah data sebelum dikosongkan
               $jumlahDataAwal = DB::table($tableName)->count();
               $this->addToConsoleLog("Jumlah data awal di tabel {$tableName}: {$jumlahDataAwal} baris", 'info');

               // Kosongkan tabel
               DB::table($tableName)->truncate();

               // Verifikasi pengosongan tabel
               $jumlahDataAkhir = DB::table($tableName)->count();
               $this->addToConsoleLog("Jumlah data akhir di tabel {$tableName}: {$jumlahDataAkhir} baris", 'success');
               $this->addToConsoleLog("Berhasil menghapus {$jumlahDataAwal} baris data dari tabel {$tableName}", 'success');
            } catch (\Exception $e) {
               $this->addToConsoleLog("Error saat menghapus data dari tabel {$tableName}: " . $e->getMessage(), 'error');
            }
         }

         // Aktifkan kembali foreign key check
         $this->addToConsoleLog("Mengaktifkan kembali foreign key constraints...", 'info');
         DB::statement('SET FOREIGN_KEY_CHECKS=1;');

         // Jalankan seeder untuk mengisi konfigurasi default
         $this->addToConsoleLog("Menjalankan seeder untuk mengisi konfigurasi default...", 'info');

         // Jalankan seeder konfigurasi default jika ada
         try {
            Artisan::call('db:seed', [
               '--class' => 'App\\Aplikasi\\Hotel\\Models\\Seeders\\KonfigSeeder',
               '--force' => true,
            ]);
            $this->addToConsoleLog("Konfigurasi default berhasil diterapkan", 'success');
         } catch (\Exception $e) {
            $this->addToConsoleLog("Gagal menjalankan seeder konfigurasi default: " . $e->getMessage(), 'error');
         }

         $this->addToConsoleLog("Reset seluruh data aplikasi berhasil dilakukan", 'success');
         $this->addToConsoleLog("Aplikasi telah dikembalikan ke kondisi awal", 'success');

         Notification::make()
            ->title('Reset seluruh data berhasil')
            ->body('Semua data aplikasi telah dihapus dan dikembalikan ke kondisi awal')
            ->success()
            ->send();
      } catch (\Exception $e) {
         $this->addToConsoleLog("Error: " . $e->getMessage(), 'error');
         $this->addToConsoleLog("File: " . $e->getFile() . " (Line: " . $e->getLine() . ")", 'error');

         Notification::make()
            ->title('Gagal melakukan reset seluruh data')
            ->body($e->getMessage())
            ->danger()
            ->send();
      } finally {
         // Reset loading state
         $this->isLoading = false;
         $this->currentSeeder = null;

         // Update jumlah data untuk tab Reset
         $this->updateResetDataCounts();
      }
   }
}

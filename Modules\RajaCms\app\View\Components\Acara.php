<?php

namespace Modules\RajaCms\View\Components;

use Modules\RajaCms\Models\Cms as CmsModel;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Acara extends Component
{
    public $type = "home";
    public $by = "status";
    public $key = "home";
    public $param = [];
    public $kueri = null;

    public function __construct($type, $by = null, $key = null, $param = null)
    {
        // type tersedia
        // home  -> getAcaraHome()
        // detail -> getAcaraDetail()
        // list -> getAcaraList()

        $this->kueri = CmsModel::where('jenis', 'ACARA')
            ->orderBy('created_at', 'desc')
            ->where('status', 'tampil');
        $this->by = $by;
        $this->key = $key;
        $this->type = $type;
        $this->param = $param;
    }

    public function getAcaraList()
    {
        // param tersedia
        // pagination = true/false  // default = true
        // perhalaman = 10  // default = 10
        // orderby = created_at // default = created_at
        // order = desc // default = desc

        $data = $this->kueri->get();
        return view('tema::modul.acara.list', ['data' => $data]);
    }

    public function getAcaraDetail()
    {
        $data = $this->kueri->where($this->by, $this->key)->first();
        return view('tema::modul.acara.detail', ['data' => $data]);
    }

    public function getAcaraHome()
    {
        $param = $this->param;
        $data = $this->kueri
            ->limit($param['perhalaman'])
            ->get();
        return view('tema::modul.acara.home', ['data' => $data, 'param' => $param]);
    }

    public function render(): View|Closure|string
    {
        if ($this->type === 'home') {
            return $this->getAcaraHome();
        } elseif ($this->type === 'detail') {
            return $this->getAcaraDetail();
        } elseif ($this->type === 'list') {
            return $this->getAcaraList();
        }

        return view('components.kosong');
    }
} 
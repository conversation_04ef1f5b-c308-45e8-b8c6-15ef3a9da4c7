# Panduan Pen<PERSON>an Model Ka<PERSON> di Laravel

## Daftar Isi
- [<PERSON><PERSON><PERSON><PERSON>](#pendahuluan)
- [<PERSON><PERSON><PERSON>](#konsep-dasar)
- [Mengakses Data Kamar](#mengakses-data-kamar)
- [Menambah, Mengubah, dan <PERSON>](#menambah-mengubah-dan-menghapus-kamar)
- [Mencar<PERSON> yang Terse<PERSON>](#mencari-kamar-yang-tersedia)
- [Mengelola Status Kamar](#mengelola-status-kamar)
- [Bekerja dengan Fasilitas dan <PERSON>pesif<PERSON>](#bekerja-dengan-fasilitas-dan-spesifikasi)
- [Mengelola <PERSON>rga Kamar](#mengelola-harga-kamar)
- [Sistem Housekeeping](#sistem-housekeeping)
- [Ana<PERSON><PERSON> Performa <PERSON>](#analisis-performa-kamar)
- [Integrasi dengan Model Lain](#integrasi-dengan-model-lain)
- [Tips dan <PERSON>](#tips-dan-trik)
- [<PERSON><PERSON><PERSON>](#contoh-kasus-penggunaan)

## Pendahuluan

Model `<PERSON><PERSON>` adalah salah satu komponen penting dalam aplikasi manajemen hotel. Model ini menggunakan tabel `produk` yang sudah ada, tetapi hanya mengambil data dengan jenis 'KAMAR'. Pendekatan ini memungkinkan kita menggunakan satu tabel untuk berbagai jenis produk, sekaligus memisahkan logika bisnis khusus untuk kamar hotel.

Model ini dibuat dengan beberapa fitur utama:
- Manajemen kamar hotel (nomor kamar, tipe, harga, dsb)
- Pengecekan ketersediaan kamar
- Integrasi dengan reservasi
- Manajemen status kamar dan housekeeping
- Analisis performa dan okupansi kamar
- Pengelolaan harga dinamis

File model ini terletak di `app/Aplikasi/Hotel/Models/Kamar.php`.

## Konsep Dasar

### Struktur Model Kamar

Model `Kamar` menggunakan tabel `produk` tetapi dengan filter tambahan:

```php
protected static function boot()
{
    parent::boot();

    // Filter data hanya untuk jenis='KAMAR'
    static::addGlobalScope('kamar', function (Builder $builder) {
        $builder->where('jenis', 'KAMAR');
    });

    // Otomatis isi jenis='KAMAR' saat membuat data baru
    static::creating(function ($kamar) {
        $kamar->jenis = 'KAMAR';
    });

    // Pastikan jenis tetap 'KAMAR' saat memperbarui data
    static::updating(function ($kamar) {
        $kamar->jenis = 'KAMAR';
    });
}
```

Ini berarti:
- Saat mengambil data dengan model `Kamar`, hanya data dengan jenis='KAMAR' yang akan ditampilkan
- Saat membuat kamar baru, otomatis kolom `jenis` akan diisi dengan 'KAMAR'
- Saat mengupdate kamar, nilai `jenis` akan tetap 'KAMAR'

### Atribut Penting dalam Model Kamar

Berikut adalah atribut-atribut penting dalam model `Kamar`:

```php
protected $fillable = [
    'jenis',          // Selalu 'KAMAR' (diset otomatis)
    'toko_id',        // ID toko/hotel
    'sub',            // ID kamar induk (jika ini adalah varian kamar)
    'kategori_id',    // ID kategori/tipe kamar (mis. Deluxe, Suite, dll)
    'barcode',        // Barcode kamar (opsional)
    'nama',           // Nomor kamar (disimpan di kolom nama)
    'harga',          // Harga per malam
    'harga_modal',    // Harga modal/biaya operasional
    'stok',           // Status ketersediaan (1=tersedia, 0=terpakai)
    'gambar',         // Path gambar kamar
    'ket',            // Keterangan tambahan
    'fasilitas',      // Array fasilitas kamar (disimpan sebagai JSON)
    'spesifikasi',    // Array spesifikasi kamar (disimpan sebagai JSON)
    'tampil',         // Apakah kamar ditampilkan (boolean)
    'inivarian',      // Apakah ini kamar induk (boolean)
];
```

### Atribut Virtual (Appends)

Model `Kamar` memiliki beberapa atribut virtual yang bisa diakses seperti kolom normal:

```php
protected $appends = [
    'is_available',       // Status ketersediaan (boolean)
    'nomor_kamar',        // Alias untuk nama
    'tipe_kamar',         // Nama tipe kamar
    'status_kamar',       // Status kamar (tersedia/terpakai)
    'gambar_url',         // URL lengkap gambar kamar
    'harga_format',       // Harga dalam format Rp
    'okupansi_minggu_ini', // Persentase okupansi minggu ini
];
```

## Mengakses Data Kamar

### Mengambil Semua Kamar

```php
use App\Aplikasi\Hotel\Models\Kamar;

// Mengambil semua kamar
$semuaKamar = Kamar::all();

// Menampilkan data kamar
foreach ($semuaKamar as $kamar) {
    echo "Kamar {$kamar->nomor_kamar} - Tipe: {$kamar->tipe_kamar} - Harga: {$kamar->harga_format}<br>";
}
```

### Mengambil Satu Kamar

```php
// Berdasarkan ID
$kamar = Kamar::find(1);

// Berdasarkan nomor kamar
$kamar = Kamar::where('nama', '101')->first();

if ($kamar) {
    echo "Kamar ditemukan: {$kamar->nomor_kamar} ({$kamar->tipe_kamar})";
} else {
    echo "Kamar tidak ditemukan!";
}
```

### Filter dan Pengurutan

```php
// Hanya kamar yang tersedia
$kamarTersedia = Kamar::tersedia()->get();

// Kamar berdasarkan tipe
$kamarDeluxe = Kamar::whereHas('tipe', function($query) {
    $query->where('nama', 'Deluxe');
})->get();

// Kamar urut berdasarkan harga (termurah)
$kamarMurah = Kamar::orderBy('harga', 'asc')->get();

// Kamar urut berdasarkan harga (termahal)
$kamarMahal = Kamar::orderBy('harga', 'desc')->get();
```

### Menggunakan Scope Query

Model `Kamar` menyediakan beberapa scope query untuk memudahkan filter:

```php
// Kamar yang tersedia
$kamarTersedia = Kamar::tersedia()->get();

// Kamar berdasarkan tipe
$tipeId = 1; // ID tipe kamar (misalnya Deluxe)
$kamarDeluxe = Kamar::tipe($tipeId)->get();

// Kamar yang aktif (ditampilkan)
$kamarAktif = Kamar::aktif()->get();

// Kamar dalam rentang harga tertentu
$kamarEkonomis = Kamar::hargaAntara(300000, 500000)->get();

// Kamar dengan fasilitas tertentu
$kamarWifi = Kamar::memilikiFasilitas('internet')->get();

// Pencarian kamar
$kamarResult = Kamar::cari('deluxe')->get();
```

## Menambah, Mengubah, dan Menghapus Kamar

### Menambah Kamar Baru

```php
use App\Aplikasi\Hotel\Models\Kamar;

$kamarBaru = new Kamar();
$kamarBaru->nomor_kamar = '101'; // Secara otomatis disimpan di kolom 'nama'
$kamarBaru->kategori_id = 1; // ID tipe kamar
$kamarBaru->harga = 500000;
$kamarBaru->harga_modal = 300000;
$kamarBaru->stok = 1; // 1 = tersedia
$kamarBaru->tampil = true;
$kamarBaru->fasilitas = ['Tv Led', 'minibar', 'internet'];
$kamarBaru->spesifikasi = ['double'];
$kamarBaru->save();

echo "Kamar berhasil ditambahkan dengan ID: {$kamarBaru->id}";
```

Cara lain:

```php
$kamarBaru = Kamar::create([
    'nama' => '102', // Nomor kamar
    'kategori_id' => 1,
    'harga' => 550000,
    'harga_modal' => 320000,
    'stok' => 1,
    'tampil' => true,
    'fasilitas' => ['Tv Led', 'minibar', 'internet', 'Bath hub'],
    'spesifikasi' => ['twin'],
]);
```

### Mengubah Data Kamar

```php
$kamar = Kamar::find(1);

if ($kamar) {
    $kamar->harga = 600000;
    $kamar->fasilitas = array_merge($kamar->fasilitas ?? [], ['Teko Pemanas air']);
    $kamar->save();
    
    echo "Kamar berhasil diupdate!";
} else {
    echo "Kamar tidak ditemukan!";
}
```

### Menghapus Kamar

```php
$kamar = Kamar::find(1);

if ($kamar) {
    $kamar->delete(); // Soft delete (data tidak benar-benar dihapus)
    echo "Kamar berhasil dihapus!";
} else {
    echo "Kamar tidak ditemukan!";
}
```

### Mengembalikan Kamar yang Sudah Dihapus

```php
// Mendapatkan kamar yang sudah dihapus
$kamarTerhapus = Kamar::withTrashed()->find(1);

if ($kamarTerhapus && $kamarTerhapus->trashed()) {
    $kamarTerhapus->restore();
    echo "Kamar berhasil dikembalikan!";
} else {
    echo "Kamar tidak ditemukan atau tidak dalam status deleted!";
}
```

## Mencari Kamar yang Tersedia

### Cek Ketersediaan Satu Kamar

```php
use Carbon\Carbon;

$kamar = Kamar::find(1);
$checkIn = Carbon::parse('2025-04-01');
$checkOut = Carbon::parse('2025-04-05');

if ($kamar->isAvailable($checkIn, $checkOut)) {
    echo "Kamar {$kamar->nomor_kamar} tersedia untuk tanggal tersebut!";
} else {
    echo "Kamar {$kamar->nomor_kamar} tidak tersedia untuk tanggal tersebut!";
}
```

### Mencari Semua Kamar Tersedia

```php
use App\Aplikasi\Hotel\Models\Kamar;
use Carbon\Carbon;

// Tentukan tanggal check-in dan check-out
$checkIn = Carbon::parse('2025-04-01');
$checkOut = Carbon::parse('2025-04-05');

// Cari semua kamar yang tersedia
$kamarTersedia = Kamar::getAvailableRooms($checkIn, $checkOut);

echo "Ditemukan " . $kamarTersedia->count() . " kamar tersedia.<br>";

// Menampilkan daftar kamar tersedia
foreach ($kamarTersedia as $kamar) {
    echo "- Kamar {$kamar->nomor_kamar} ({$kamar->tipe_kamar}): {$kamar->harga_format}<br>";
}
```

### Mencari Kamar Tersedia Berdasarkan Tipe

```php
// Mencari kamar tipe tertentu yang tersedia
$tipeId = 2; // ID tipe kamar (misalnya Suite)
$kamarSuiteTersedia = Kamar::getAvailableRooms($checkIn, $checkOut, $tipeId);

echo "Ditemukan " . $kamarSuiteTersedia->count() . " kamar Suite tersedia.";
```

### Mencari Kamar dengan Kriteria Kompleks

```php
// Mendapatkan semua kamar tersedia
$kamarTersedia = Kamar::getAvailableRooms($checkIn, $checkOut);

// Filter lebih lanjut berdasarkan fasilitas dan harga
$kamarFiltered = $kamarTersedia->filter(function($kamar) {
    return $kamar->hasFasilitas('Bath hub') && 
           $kamar->harga >= 400000 && 
           $kamar->harga <= 700000;
});

echo "Ditemukan " . $kamarFiltered->count() . " kamar tersedia sesuai kriteria.";
```

## Mengelola Status Kamar

### Mengubah Status Ketersediaan Kamar

```php
$kamar = Kamar::find(1);

// Mengubah status menjadi tersedia
$kamar->updateStatus('tersedia');
echo "Kamar {$kamar->nomor_kamar} sekarang {$kamar->status_kamar}.<br>";

// Mengubah status menjadi terpakai
$kamar->updateStatus('terpakai');
echo "Kamar {$kamar->nomor_kamar} sekarang {$kamar->status_kamar}.";
```

### Memeriksa Status Kamar

```php
$kamar = Kamar::find(1);

if ($kamar->getIsAvailableAttribute()) {
    echo "Kamar {$kamar->nomor_kamar} tersedia.";
} else {
    echo "Kamar {$kamar->nomor_kamar} tidak tersedia.";
}

// Cara alternatif
echo "Status kamar: " . $kamar->status_kamar; // 'tersedia' atau 'terpakai'
```

## Bekerja dengan Fasilitas dan Spesifikasi

### Mengambil Daftar Fasilitas dari Konfigurasi

```php
// Mendapatkan daftar fasilitas dari konfigurasi
$daftarFasilitas = Kamar::getDaftarFasilitas();

echo "Daftar fasilitas tersedia:<br>";
foreach ($daftarFasilitas as $fasilitas) {
    echo "- {$fasilitas}<br>";
}
```

### Mengambil Daftar Spesifikasi dari Konfigurasi

```php
// Mendapatkan daftar spesifikasi dari konfigurasi
$daftarSpesifikasi = Kamar::getDaftarSpesifikasi();

echo "Daftar spesifikasi kamar tersedia:<br>";
foreach ($daftarSpesifikasi as $spesifikasi) {
    echo "- {$spesifikasi}<br>";
}
```

### Menambah dan Menghapus Fasilitas

```php
$kamar = Kamar::find(1);

// Menambah fasilitas
$fasilitasBaru = array_merge($kamar->fasilitas ?? [], ['Kulkas']);
$kamar->fasilitas = $fasilitasBaru;
$kamar->save();

// Menghapus fasilitas
$fasilitasUpdate = array_diff($kamar->fasilitas ?? [], ['minibar']);
$kamar->fasilitas = $fasilitasUpdate;
$kamar->save();

echo "Fasilitas kamar sekarang: " . implode(", ", $kamar->fasilitas ?? []);
```

### Cek Apakah Kamar Memiliki Fasilitas Tertentu

```php
$kamar = Kamar::find(1);

if ($kamar->hasFasilitas('Tv Led')) {
    echo "Kamar {$kamar->nomor_kamar} memiliki TV LED.";
} else {
    echo "Kamar {$kamar->nomor_kamar} tidak memiliki TV LED.";
}
```

### Mencari Kamar Berdasarkan Fasilitas

```php
// Kamar dengan fasilitas tertentu
$kamarDenganWifi = Kamar::memilikiFasilitas('internet')->get();

// Kamar dengan beberapa fasilitas
$kamarPremium = Kamar::memilikiFasilitas(['Bath hub', 'minibar'])->get();

echo "Ditemukan " . $kamarPremium->count() . " kamar premium.";
```

## Mengelola Harga Kamar

### Format Harga untuk Tampilan

```php
$kamar = Kamar::find(1);

// Mendapatkan harga dalam format Rupiah
echo "Harga kamar: " . $kamar->formatHarga(); // "Rp 500.000"

// Menggunakan atribut harga_format
echo "Harga kamar: " . $kamar->harga_format; // "Rp 500.000"
```

### Mengelola Harga Dinamis dengan HargaJual

```php
use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\HargaJual;

// Mendapatkan kamar
$kamar = Kamar::find(1);

// Menambahkan harga spesial untuk periode tertentu
HargaJual::create([
    'produk_id' => $kamar->id,
    'nama' => 'Promo Lebaran',
    'harga' => 450000, // Harga promosi
    'minimal' => 2, // Minimal 2 malam
    'maksimal' => null, // Tidak ada batasan maksimal
    'tgl_mulai' => '2025-04-01',
    'tgl_selesai' => '2025-04-15',
]);

// Menambahkan harga untuk long stay
HargaJual::create([
    'produk_id' => $kamar->id,
    'nama' => 'Long Stay',
    'harga' => 350000, // Harga lebih murah
    'minimal' => 7, // Minimal 7 malam
    'tgl_mulai' => null, // Berlaku kapan saja
    'tgl_selesai' => null,
]);
```

### Mendapatkan Harga Spesial

```php
// Mendapatkan kamar
$kamar = Kamar::find(1);

// Cek apakah ada harga spesial untuk 3 malam
$hargaSpesial = $kamar->getHargaSpesial(3);

if ($hargaSpesial) {
    echo "Harga spesial: Rp " . number_format($hargaSpesial, 0, ',', '.');
} else {
    echo "Tidak ada harga spesial, harga normal: " . $kamar->harga_format;
}
```

### Mendapatkan Semua Harga Spesial Aktif

```php
// Mendapatkan kamar
$kamar = Kamar::find(1);

// Ambil semua harga spesial yang aktif
$hargaSpesial = $kamar->hargaJual()
    ->whereRaw('(tgl_mulai IS NULL OR tgl_mulai <= NOW())')
    ->whereRaw('(tgl_selesai IS NULL OR tgl_selesai >= NOW())')
    ->get();

if ($hargaSpesial->count() > 0) {
    echo "Daftar harga spesial aktif:<br>";
    foreach ($hargaSpesial as $hs) {
        echo "- {$hs->nama}: Rp " . number_format($hs->harga, 0, ',', '.') . " (min: {$hs->minimal} malam)<br>";
    }
} else {
    echo "Tidak ada harga spesial aktif.";
}
```

## Sistem Housekeeping

### Mengubah Status Housekeeping

```php
$kamar = Kamar::find(1);

// Kamar sudah dibersihkan
$kamar->checkHousekeeping('bersih', 'Dibersihkan oleh staff A');
echo "Status housekeeping: " . $kamar->getHousekeepingStatus() . "<br>";

// Kamar kotor
$kamar->checkHousekeeping('kotor', 'Menunggu pembersihan');
echo "Status housekeeping: " . $kamar->getHousekeepingStatus() . "<br>";

// Kamar dalam perbaikan
$kamar->checkHousekeeping('maintenance', 'AC rusak, sedang diperbaiki');
echo "Status housekeeping: " . $kamar->getHousekeepingStatus() . "<br>";
```

### Mendapatkan Status Housekeeping

```php
$kamar = Kamar::find(1);
$status = $kamar->getHousekeepingStatus();

// Menggunakan status housekeeping
switch ($status) {
    case 'bersih':
        echo "Kamar siap untuk tamu";
        break;
    case 'kotor':
        echo "Kamar perlu dibersihkan";
        break;
    case 'maintenance':
        echo "Kamar sedang dalam perbaikan";
        break;
    default:
        echo "Status kamar belum diperiksa";
}
```

### Mencari Kamar Berdasarkan Status Housekeeping

```php
// Mencari kamar yang perlu dibersihkan
$kamarKotor = Kamar::whereRaw("JSON_EXTRACT(ket, '$.housekeeping_status') = 'kotor'")->get();

// Mendapatkan semua kamar dalam perbaikan
$kamarMaintenance = Kamar::whereRaw("JSON_EXTRACT(ket, '$.housekeeping_status') = 'maintenance'")->get();

echo "Ada " . $kamarKotor->count() . " kamar yang perlu dibersihkan.";
```

### Mendapatkan Daftar Status Housekeeping

```php
$daftarStatus = Kamar::getDaftarStatusHousekeeping();

echo "Daftar status housekeeping:<br>";
foreach ($daftarStatus as $key => $label) {
    echo "- {$key}: {$label}<br>";
}
```

## Analisis Performa Kamar

### Menghitung Pendapatan dari Kamar

```php
use Carbon\Carbon;

$kamar = Kamar::find(1);

// Hitung pendapatan bulan ini
$awalBulan = Carbon::now()->startOfMonth();
$akhirBulan = Carbon::now()->endOfMonth();
$pendapatan = $kamar->hitungPendapatan($awalBulan, $akhirBulan);

echo "Pendapatan kamar {$kamar->nomor_kamar} bulan ini: Rp " . number_format($pendapatan, 0, ',', '.');
```

### Menghitung Okupansi Kamar

```php
$kamar = Kamar::find(1);

// Hitung okupansi bulan ini
$awalBulan = Carbon::now()->startOfMonth();
$akhirBulan = Carbon::now()->endOfMonth();
$okupansi = $kamar->hitungOkupansi($awalBulan, $akhirBulan);

echo "Okupansi kamar {$kamar->nomor_kamar} bulan ini: " . number_format($okupansi, 1) . "%";

// Menggunakan atribut virtual untuk okupansi minggu ini
echo "Okupansi minggu ini: " . number_format($kamar->okupansi_minggu_ini, 1) . "%";
```

### Analisis Keuntungan

```php
$kamar = Kamar::find(1);

// Hitung keuntungan per malam
$keuntungan = $kamar->harga - $kamar->harga_modal;
$persentaseKeuntungan = ($keuntungan / $kamar->harga_modal) * 100;

echo "Keuntungan per malam: Rp " . number_format($keuntungan, 0, ',', '.') . "<br>";
echo "Persentase keuntungan: " . number_format($persentaseKeuntungan, 2) . "%";
```

## Integrasi dengan Model Lain

### Akses ke Tipe Kamar

```php
$kamar = Kamar::find(1);

// Mendapatkan informasi tipe kamar
$tipe = $kamar->tipe;

if ($tipe) {
    echo "Tipe kamar: {$tipe->nama}<br>";
    echo "Keterangan: {$tipe->ket}";
} else {
    echo "Tipe kamar tidak ditemukan!";
}
```

### Akses ke Reservasi

```php
$kamar = Kamar::find(1);

// Mendapatkan semua reservasi untuk kamar ini
$reservasi = $kamar->reservasi;

echo "Total reservasi: " . $reservasi->count() . "<br>";

// Menampilkan 5 reservasi terbaru
$reservasiTerbaru = $kamar->reservasi()->latest()->take(5)->get();

foreach ($reservasiTerbaru as $res) {
    echo "Reservasi tanggal " . $res->check_in->format('d/m/Y') . " - " . $res->check_out->format('d/m/Y') . "<br>";
}
```

### Akses ke Varian Kamar

```php
$kamarInduk = Kamar::where('inivarian', true)->first();

if ($kamarInduk) {
    $varian = $kamarInduk->varian;
    
    echo "Kamar {$kamarInduk->nomor_kamar} memiliki " . $varian->count() . " varian:<br>";
    
    foreach ($varian as $var) {
        echo "- Kamar {$var->nomor_kamar}: {$var->harga_format}<br>";
    }
} else {
    echo "Tidak ada kamar induk!";
}
```

### Akses ke Transaksi

```php
$kamar = Kamar::find(1);

// Mendapatkan semua transaksi terkait kamar ini
$transaksi = $kamar->transaksi;

echo "Total transaksi: " . $transaksi->count() . "<br>";

// Menghitung total nilai transaksi
$totalNilai = $transaksi->sum(function($item) {
    return $item->jumlah * $item->harga;
});

echo "Total nilai transaksi: Rp " . number_format($totalNilai, 0, ',', '.');
```

## Tips dan Trik

### 1. Menyingkat Penulisan Status Ketersediaan

```php
// Cara standar
if ($kamar->getStatusKamarAttribute() === 'tersedia') {
    echo "Kamar tersedia";
}

// Cara singkat dengan atribut virtual
if ($kamar->status_kamar === 'tersedia') {
    echo "Kamar tersedia";
}

// Cara paling singkat dengan method
if ($kamar->is_available) {
    echo "Kamar tersedia";
}
```

### 2. Transformasi Data untuk API

```php
$kamar = Kamar::find(1);

// Transformasi data untuk API
$kamarData = [
    'id' => $kamar->id,
    'nomor' => $kamar->nomor_kamar,
    'tipe' => $kamar->tipe_kamar,
    'harga' => $kamar->harga,
    'harga_format' => $kamar->harga_format,
    'tersedia' => $kamar->is_available,
    'fasilitas' => $kamar->fasilitas ?? [],
    'gambar' => $kamar->gambar_url,
];

return response()->json($kamarData);
```

### 3. Pencarian Kamar Lanjutan

```php
// Mencari kamar berdasarkan berbagai kriteria
$query = Kamar::query();

// Filter berdasarkan tersedia
if ($request->has('tersedia') && $request->tersedia) {
    $query->tersedia();
}

// Filter berdasarkan tipe
if ($request->has('tipe_id') && $request->tipe_id) {
    $query->tipe($request->tipe_id);
}

// Filter berdasarkan rentang harga
if ($request->has('harga_min') && $request->has('harga_max')) {
    $query->hargaAntara($request->harga_min, $request->harga_max);
}

// Filter berdasarkan fasilitas
if ($request->has('fasilitas') && is_array($request->fasilitas)) {
    $query->memilikiFasilitas($request->fasilitas);
}

// Filter berdasarkan keyword
if ($request->has('keyword') && $request->keyword) {
    $query->cari($request->keyword);
}

// Ambil hasil
$kamarResult = $query->get();
```

### 4. Hitung Jumlah Kamar per Tipe

```php
// Menghitung jumlah kamar per tipe
$kamarPerTipe = Kamar::select('kategori_id')
    ->selectRaw('COUNT(*) as jumlah')
    ->groupBy('kategori_id')
    ->with('tipe') // Eager load tipe kamar
    ->get();

echo "Jumlah kamar per tipe:<br>";
foreach ($kamarPerTipe as $item) {
    $tipeName = $item->tipe ? $item->tipe->nama : 'Tidak ada tipe';
    echo "- {$tipeName}: {$item->jumlah} kamar<br>";
}
```

### 5. Membuat Tabel Ketersediaan Kamar

```php
use Carbon\Carbon;

// Buat array tanggal untuk periode tertentu
$startDate = Carbon::now();
$endDate = Carbon::now()->addDays(7);
$dates = [];

for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
    $dates[] = $date->format('Y-m-d');
}

// Ambil semua kamar
$kamarList = Kamar::with('tipe')->get();

echo "<table border='1'>";
echo "<tr><th>Kamar</th>";
foreach ($dates as $date) {
    echo "<th>" . Carbon::parse($date)->format('d/m') . "</th>";
}
echo "</tr>";

foreach ($kamarList as $kamar) {
    echo "<tr>";
    echo "<td>{$kamar->nomor_kamar} ({$kamar->tipe_kamar})</td>";
    
    foreach ($dates as $date) {
        $checkDate = Carbon::parse($date);
        $nextDay = (clone $checkDate)->addDay();
        
        $available = $kamar->isAvailable($checkDate, $nextDay);
        $color = $available ? 'green' : 'red';
        $status = $available ? 'Tersedia' : 'Terpakai';
        
        echo "<td style='background-color: {$color}; color: white;'>{$status}</td>";
    }
    
    echo "</tr>";
}

echo "</table>";
```

## Contoh Kasus Penggunaan

### 1. Form Pencarian Kamar untuk Reservasi

```php
// Controller
public function searchRooms(Request $request)
{
    $request->validate([
        'check_in' => 'required|date',
        'check_out' => 'required|date|after:check_in',
        'tipe_id' => 'nullable|exists:produk_kategori,id',
    ]);
    
    $checkIn = Carbon::parse($request->check_in);
    $checkOut = Carbon::parse($request->check_out);
    $tipeId = $request->tipe_id;
    
    // Cari kamar yang tersedia
    $kamarTersedia = Kamar::getAvailableRooms($checkIn, $checkOut, $tipeId);
    
    // Hitung jumlah malam
    $jumlahMalam = $checkIn->diffInDays($checkOut);
    
    return view('hotel.kamar.search-results', [
        'kamarList' => $kamarTersedia,
        'checkIn' => $checkIn,
        'checkOut' => $checkOut,
        'jumlahMalam' => $jumlahMalam,
    ]);
}
```

### 2. Dashboard Manajemen Kamar

```php
// Controller
public function dashboard()
{
    // Statistik kamar
    $totalKamar = Kamar::count();
    $kamarTersedia = Kamar::tersedia()->count();
    $kamarTerpakai = $totalKamar - $kamarTersedia;
    
    // Kamar berdasarkan tipe
    $kamarPerTipe = Kamar::select('kategori_id')
        ->selectRaw('COUNT(*) as jumlah')
        ->groupBy('kategori_id')
        ->with('tipe')
        ->get();
    
    // Kamar yang perlu housekeeping
    $kamarKotor = Kamar::whereRaw("JSON_EXTRACT(ket, '$.housekeeping_status') = 'kotor'")->count();
    
    // Analisis pendapatan
    $bulanIni = Carbon::now()->startOfMonth();
    $pendapatanPerTipe = [];
    
    foreach ($kamarPerTipe as $item) {
        $kamarIds = Kamar::where('kategori_id', $item->kategori_id)->pluck('id')->toArray();
        
        $pendapatan = Transaksi::whereIn('produk_id', $kamarIds)
            ->whereHas('penjualan', function($query) use ($bulanIni) {
                $query->where('created_at', '>=', $bulanIni)
                    ->where('status', 'SELESAI');
            })
            ->sum(\DB::raw('harga * jumlah'));
        
        $pendapatanPerTipe[] = [
            'tipe' => $item->tipe ? $item->tipe->nama : 'Tidak ada tipe',
            'pendapatan' => $pendapatan,
        ];
    }
    
    return view('hotel.kamar.dashboard', [
        'totalKamar' => $totalKamar,
        'kamarTersedia' => $kamarTersedia,
        'kamarTerpakai' => $kamarTerpakai,
        'kamarPerTipe' => $kamarPerTipe,
        'kamarKotor' => $kamarKotor,
        'pendapatanPerTipe' => $pendapatanPerTipe,
    ]);
}
```

### 3. API untuk Mobile App

```php
// Controller
public function getRooms(Request $request)
{
    $query = Kamar::with('tipe');
    
    // Filter berdasarkan ketersediaan
    if ($request->has('available') && $request->available) {
        $query->tersedia();
    }
    
    // Filter berdasarkan tipe
    if ($request->has('type_id') && $request->type_id) {
        $query->tipe($request->type_id);
    }
    
    // Ambil hasil
    $rooms = $query->get();
    
    // Transform data
    $result = $rooms->map(function($kamar) {
        return [
            'id' => $kamar->id,
            'number' => $kamar->nomor_kamar,
            'type' => $kamar->tipe_kamar,
            'price' => $kamar->harga,
            'price_formatted' => $kamar->harga_format,
            'available' => $kamar->is_available,
            'facilities' => $kamar->fasilitas ?? [],
            'specifications' => $kamar->spesifikasi ?? [],
            'image' => $kamar->gambar_url,
        ];
    });
    
    return response()->json([
        'success' => true,
        'data' => $result,
        'count' => $result->count(),
    ]);
}
```

### 4. Laporan Okupansi Kamar

```php
// Controller
public function occupancyReport(Request $request)
{
    $request->validate([
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
    ]);
    
    $startDate = Carbon::parse($request->start_date);
    $endDate = Carbon::parse($request->end_date);
    
    // Ambil semua kamar dengan tipe
    $kamarList = Kamar::with('tipe')->get();
    
    // Hitung okupansi untuk setiap kamar
    $occupancyData = [];
    
    foreach ($kamarList as $kamar) {
        $occupancyData[] = [
            'kamar_id' => $kamar->id,
            'nomor_kamar' => $kamar->nomor_kamar,
            'tipe_kamar' => $kamar->tipe_kamar,
            'okupansi' => $kamar->hitungOkupansi($startDate, $endDate),
            'pendapatan' => $kamar->hitungPendapatan($startDate, $endDate),
        ];
    }
    
    // Urutkan berdasarkan okupansi (tertinggi)
    $occupancyData = collect($occupancyData)->sortByDesc('okupansi')->values()->all();
    
    // Hitung rata-rata okupansi
    $avgOccupancy = collect($occupancyData)->avg('okupansi');
    
    // Hitung total pendapatan
    $totalRevenue = collect($occupancyData)->sum('pendapatan');
    
    return view('hotel.reports.occupancy', [
        'occupancyData' => $occupancyData,
        'startDate' => $startDate,
        'endDate' => $endDate,
        'avgOccupancy' => $avgOccupancy,
        'totalRevenue' => $totalRevenue,
    ]);
}
```

---

Dengan panduan ini, Anda sekarang memiliki pengetahuan yang cukup untuk menggunakan model Kamar secara efektif dalam aplikasi hotel Anda. Mulai dari operasi dasar seperti menambah dan mengubah kamar, hingga fitur lanjutan seperti pengecekan ketersediaan, mengelola housekeeping, dan menganalisis performa kamar.

Jika Anda memiliki pertanyaan lebih lanjut atau membutuhkan bantuan dengan fungsionalitas tertentu, jangan ragu untuk bertanya!

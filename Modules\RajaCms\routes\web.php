<?php

use Illuminate\Support\Facades\Route;
use Modules\RajaCms\Http\Controllers\RajaCmsController;
use Modules\RajaCms\Http\Controllers\CmsController;
use Modules\RajaCms\Http\Controllers\TemaController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::resource('rajacms', RajaCmsController::class)->names('rajacms');
});

// Route untuk CMS website
Route::group([], function () {
    Route::get('/', [CmsController::class, 'beranda'])->name('home');
    Route::get('/artikel', [CmsController::class, 'daftarArtikel'])->name('artikel.daftar');
    Route::get('/artikel/{slug}', [CmsController::class, 'detailArtikel'])->name('artikel.detail');
    Route::get('/event/{slug}', [CmsController::class, 'detailEvent'])->name('event.detail');
    Route::get('/halaman/{slug}', [CmsController::class, 'halaman'])->name('halaman');
    Route::get('/acara', [CmsController::class, 'daftarAcara'])->name('acara.daftar');
    Route::get('/acara/{slug}', [CmsController::class, 'detailAcara'])->name('acara.detail');
    Route::get('/marketplace', [CmsController::class, 'marketplace'])->name('marketplace');
});

// Route untuk tema
Route::group([], function () {
    Route::get('/tema', [TemaController::class, 'daftar'])->name('tema.daftar');
});

# Panduan Penggunaan Model - Plugin Kasir

Dokumen ini berisi panduan lengkap untuk menggunakan model-model dalam plugin <PERSON><PERSON> (ridwans2/kasir). Panduan ini mencakup penjelasan setiap model, hubungan antar model, dan contoh penggunaan dalam berbagai konteks seperti Filament Resources, Blade views, komponen Livewire, dan halaman kustom.

## Daftar Model

Plugin Kasir menyediakan model-model berikut:

1. [Penjualan](#model-penjualan) - Model transaksi penjualan utama
2. [Transaksi](#model-transaksi) - Model item transaksi dalam penjualan
3. [Produk](#model-produk) - Model produk yang dijual
4. [ProdukKategori](#model-produkkategori) - Model kategori produk
5. [HargaJual](#model-hargajual) - Model harga jual produk
6. [MetodePembayaran](#model-metodepembayaran) - Model metode pembayaran
7. [Pembayaran](#model-pembayaran) - Model data pembayaran
8. [Tamu](#model-tamu) - Model pelanggan/member
9. [PenjualanShift](#model-penjualanshift) - Model shift penjualan
10. [Refund](#model-refund) - Model refund/pengembalian
11. [RefundTransaksi](#model-refundtransaksi) - Model item refund
12. [PrinterSetting](#model-printersetting) - Model pengaturan printer
13. [StrukTemplate](#model-struktemplate) - Model template struk

---

## Model Penjualan

### Deskripsi
Model ini merepresentasikan transaksi penjualan utama. Setiap Penjualan memiliki beberapa Transaksi (item) dan informasi terkait pembayaran.

### Tabel Database
```
penjualan
```

### Field Utama
- `id` - ID unik penjualan
- `penjualan_invoice` - Nomor invoice penjualan
- `kategori` - Kategori penjualan (KASIR)
- `jenis_pesanan` - Jenis pesanan (makanditempat, takeaway, delivery)
- `nama` - Nomor meja (untuk makanditempat)
- `nama_tamu` - Nama pelanggan
- `tamu_id` - ID tamu/member
- `diskon` - Persentase diskon
- `pajak` - Persentase pajak
- `servis` - Persentase biaya servis
- `jumlah_pembayaran` - Total pembayaran
- `ket_pesanan` - Keterangan pesanan
- `karyawan_id` - ID karyawan/user
- `status` - Status penjualan (SELESAI, PENDING, BATAL)

### Relasi
```php
// Item-item dalam penjualan
public function transaksi()
{
    return $this->hasMany(Transaksi::class);
}

// Tamu/pelanggan
public function tamu()
{
    return $this->belongsTo(Tamu::class);
}

// User/karyawan
public function user()
{
    return $this->belongsTo(User::class, 'karyawan_id');
}

// Shift penjualan
public function shift()
{
    return $this->belongsTo(PenjualanShift::class);
}

// Pembayaran
public function pembayaran()
{
    return $this->hasMany(Pembayaran::class);
}
```

### Method Penting
```php
// Mendapatkan total pembayaran
public function totalPembayaran()
{
    return $this->pembayaran->sum('jumlah');
}

// Mendapatkan total transaksi sebelum diskon, pajak, dan servis
public function subtotal()
{
    return $this->transaksi->sum(function ($item) {
        return $item->harga * $item->jumlah;
    });
}

// Mendapatkan total akhir setelah diskon, pajak, dan servis
public function grandTotal()
{
    $subtotal = $this->subtotal();
    $diskonNominal = $subtotal * ($this->diskon / 100);
    $afterDiskon = $subtotal - $diskonNominal;
    $servisNominal = $afterDiskon * ($this->servis / 100);
    $pajakNominal = $afterDiskon * ($this->pajak / 100);
    
    return $afterDiskon + $servisNominal + $pajakNominal;
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function table(Table $table): Table
{
    return $table
        ->columns([
            TextColumn::make('penjualan_invoice')
                ->label('Invoice')
                ->searchable(),
                
            TextColumn::make('created_at')
                ->label('Tanggal')
                ->dateTime('d M Y H:i')
                ->sortable(),
                
            TextColumn::make('jenis_pesanan')
                ->formatStateUsing(fn (string $state): string => match ($state) {
                    'makanditempat' => 'Makan di Tempat',
                    'takeaway' => 'Take Away',
                    'delivery' => 'Delivery',
                    default => ucfirst($state),
                }),
                
            TextColumn::make('nama_tamu')
                ->label('Pelanggan'),
                
            TextColumn::make('grandTotal')
                ->money('IDR')
                ->sortable(),
                
            TextColumn::make('status')
                ->badge()
                ->color(fn (string $state): string => match ($state) {
                    'SELESAI' => 'success',
                    'PENDING' => 'warning',
                    'BATAL' => 'danger',
                    default => 'gray',
                }),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="p-4">
    <h2 class="text-lg font-bold">Detail Transaksi {{ $penjualan->penjualan_invoice }}</h2>
    
    <div class="mt-4">
        <p><strong>Tanggal:</strong> {{ $penjualan->created_at->format('d M Y H:i') }}</p>
        <p><strong>Pelanggan:</strong> {{ $penjualan->nama_tamu }}</p>
        <p><strong>Jenis Pesanan:</strong> 
            @if($penjualan->jenis_pesanan === 'makanditempat')
                Makan di Tempat (Meja {{ $penjualan->nama }})
            @elseif($penjualan->jenis_pesanan === 'takeaway')
                Take Away
            @elseif($penjualan->jenis_pesanan === 'delivery')
                Delivery
            @endif
        </p>
    </div>
    
    <table class="mt-4 w-full">
        <thead>
            <tr>
                <th class="text-left">Item</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Jumlah</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($penjualan->transaksi as $item)
                <tr>
                    <td>{{ $item->nama_item }}</td>
                    <td class="text-right">{{ number_format($item->harga, 0, ',', '.') }}</td>
                    <td class="text-right">{{ $item->jumlah }}</td>
                    <td class="text-right">{{ number_format($item->harga * $item->jumlah, 0, ',', '.') }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="mt-4 text-right">
        <p><strong>Subtotal:</strong> Rp {{ number_format($penjualan->subtotal(), 0, ',', '.') }}</p>
        
        @if($penjualan->diskon > 0)
            <p><strong>Diskon ({{ $penjualan->diskon }}%):</strong> Rp {{ number_format($penjualan->subtotal() * $penjualan->diskon / 100, 0, ',', '.') }}</p>
        @endif
        
        @if($penjualan->servis > 0)
            <p><strong>Biaya Layanan ({{ $penjualan->servis }}%):</strong> Rp {{ number_format(($penjualan->subtotal() - ($penjualan->subtotal() * $penjualan->diskon / 100)) * $penjualan->servis / 100, 0, ',', '.') }}</p>
        @endif
        
        @if($penjualan->pajak > 0)
            <p><strong>Pajak ({{ $penjualan->pajak }}%):</strong> Rp {{ number_format(($penjualan->subtotal() - ($penjualan->subtotal() * $penjualan->diskon / 100)) * $penjualan->pajak / 100, 0, ',', '.') }}</p>
        @endif
        
        <p class="text-lg font-bold">Total: Rp {{ number_format($penjualan->grandTotal(), 0, ',', '.') }}</p>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\Penjualan;

class TransaksiDetail extends Component
{
    public $penjualanId;
    public $penjualan;
    
    public function mount($penjualanId)
    {
        $this->penjualanId = $penjualanId;
        $this->loadPenjualan();
    }
    
    public function loadPenjualan()
    {
        $this->penjualan = Penjualan::with(['transaksi', 'pembayaran.metodePembayaran', 'tamu', 'user'])
            ->findOrFail($this->penjualanId);
    }
    
    public function cetakStruk()
    {
        // Contoh memanggil controller untuk cetak struk
        return redirect()->route('kasir.cetak.struk', ['penjualanId' => $this->penjualanId]);
    }
    
    public function render()
    {
        return view('kasir::livewire.transaksi-detail');
    }
}
```

---

## Model Transaksi

### Deskripsi
Model ini merepresentasikan item dalam transaksi penjualan. Setiap Transaksi terkait dengan satu Penjualan dan satu Produk.

### Tabel Database
```
transaksi
```

### Field Utama
- `id` - ID unik transaksi
- `penjualan_id` - ID penjualan
- `produk_id` - ID produk
- `nama_item` - Nama item
- `harga_modal` - Harga modal
- `harga` - Harga jual
- `jumlah` - Jumlah item
- `ket` - Keterangan

### Relasi
```php
// Penjualan
public function penjualan()
{
    return $this->belongsTo(Penjualan::class);
}

// Produk
public function produk()
{
    return $this->belongsTo(Produk::class);
}
```

### Method Penting
```php
// Total harga item
public function totalHarga()
{
    return $this->harga * $this->jumlah;
}

// Total modal item
public function totalModal()
{
    return $this->harga_modal * $this->jumlah;
}

// Keuntungan
public function keuntungan()
{
    return $this->totalHarga() - $this->totalModal();
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function table(Table $table): Table
{
    return $table
        ->columns([
            TextColumn::make('penjualan.penjualan_invoice')
                ->label('Invoice')
                ->searchable(),
                
            TextColumn::make('nama_item')
                ->label('Item')
                ->searchable(),
                
            TextColumn::make('harga')
                ->money('IDR')
                ->sortable(),
                
            TextColumn::make('jumlah')
                ->sortable(),
                
            TextColumn::make('totalHarga')
                ->money('IDR')
                ->sortable()
                ->state(fn (Transaksi $record): float => $record->harga * $record->jumlah),
                
            TextColumn::make('created_at')
                ->dateTime('d M Y H:i')
                ->sortable(),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="overflow-x-auto">
    <table class="min-w-full">
        <thead>
            <tr>
                <th class="text-left">Item</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Jumlah</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($transaksiList as $item)
                <tr>
                    <td>{{ $item->nama_item }}</td>
                    <td class="text-right">Rp {{ number_format($item->harga, 0, ',', '.') }}</td>
                    <td class="text-right">{{ $item->jumlah }}</td>
                    <td class="text-right">Rp {{ number_format($item->totalHarga(), 0, ',', '.') }}</td>
                </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3" class="text-right font-bold">Total</td>
                <td class="text-right font-bold">Rp {{ number_format($transaksiList->sum(fn($item) => $item->totalHarga()), 0, ',', '.') }}</td>
            </tr>
        </tfoot>
    </table>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\Transaksi;
use Livewire\Component;

class LaporanPenjualanItems extends Component
{
    public $tanggalMulai;
    public $tanggalSelesai;
    public $items = [];
    
    public function mount()
    {
        $this->tanggalMulai = now()->startOfMonth()->format('Y-m-d');
        $this->tanggalSelesai = now()->format('Y-m-d');
        $this->loadItems();
    }
    
    public function loadItems()
    {
        $this->items = Transaksi::whereHas('penjualan', function ($query) {
            $query->whereBetween('created_at', [$this->tanggalMulai . ' 00:00:00', $this->tanggalSelesai . ' 23:59:59'])
                  ->where('status', 'SELESAI');
        })->with('penjualan')->get();
    }
    
    public function getTotalPenjualanProperty()
    {
        return $this->items->sum(fn($item) => $item->totalHarga());
    }
    
    public function getTotalKeuntunganProperty()
    {
        return $this->items->sum(fn($item) => $item->keuntungan());
    }
    
    public function render()
    {
        return view('kasir::livewire.laporan-penjualan-items');
    }
}
```

---

## Model Produk

### Deskripsi
Model ini merepresentasikan produk yang dijual. Produk dapat dikategorikan dan memiliki beberapa harga jual.

### Tabel Database
```
produk
```

### Field Utama
- `id` - ID unik produk
- `jenis` - Jenis produk (PRODUK, KAMAR, FASILITAS)
- `sub` - ID produk induk (untuk varian)
- `kategori_id` - ID kategori produk
- `barcode` - Barcode produk
- `nama` - Nama produk
- `harga_modal` - Harga modal produk
- `harga` - Harga jual produk
- `stok` - Stok produk
- `gambar` - Path gambar produk
- `ket` - Keterangan produk
- `fasilitas` - Fasilitas produk (array)
- `spesifikasi` - Spesifikasi produk (array)
- `inivarian` - Flag apakah produk memiliki varian
- `tampil` - Flag apakah produk ditampilkan

### Casts
```php
protected $casts = [
    'harga_modal' => 'integer',
    'harga' => 'integer',
    'stok' => 'integer',
    'sub' => 'integer',
    'kategori_id' => 'integer',
    'fasilitas' => 'array',
    'spesifikasi' => 'array',
    'inivarian' => 'boolean',
    'tampil' => 'boolean',
];
```

### Relasi
```php
// Kategori produk
public function kategori()
{
    return $this->belongsTo(ProdukKategori::class, 'kategori_id');
}

// Daftar harga jual
public function hargaJual()
{
    return $this->hasMany(HargaJual::class);
}

// Varian produk
public function varian()
{
    return $this->hasMany(Produk::class, 'sub', 'id');
}

// Produk induk
public function parent()
{
    return $this->belongsTo(Produk::class, 'sub');
}

// Transaksi
public function transaksi()
{
    return $this->hasMany(Transaksi::class);
}
```

### Method Penting
```php
// Cek apakah produk memiliki varian
public function hasVariants()
{
    return $this->inivarian && $this->varian()->count() > 0;
}

// Mendapatkan harga jual aktif
public function getActivePrice($quantity = 1)
{
    // Cek harga jual berdasarkan kuantitas dan tanggal aktif
    return $this->hargaJual()
        ->active()
        ->where(function($query) use ($quantity) {
            $query->where(function($q) use ($quantity) {
                $q->where('minimal', '<=', $quantity)
                  ->whereNull('maksimal');
            })->orWhere(function($q) use ($quantity) {
                $q->where('minimal', '<=', $quantity)
                  ->where('maksimal', '>=', $quantity);
            })->orWhere(function($q) {
                $q->whereNull('minimal')
                  ->whereNull('maksimal');
            });
        })
        ->orderBy('harga')
        ->first();
}

// Mendapatkan harga final
public function getFinalPrice($quantity = 1)
{
    $activePrice = $this->getActivePrice($quantity);
    
    if ($activePrice) {
        return $activePrice->harga;
    }
    
    return $this->harga;
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('jenis')
                ->label('Jenis Produk')
                ->options(config('kasir.enums.jenis_produk'))
                ->required(),
                
            Select::make('kategori_id')
                ->label('Kategori')
                ->relationship('kategori', 'nama')
                ->searchable()
                ->preload()
                ->createOptionForm([
                    TextInput::make('nama')
                        ->required(),
                    Select::make('jenis')
                        ->options(config('kasir.enums.jenis_produk'))
                        ->required(),
                ])
                ->required(),
                
            TextInput::make('barcode')
                ->label('Barcode')
                ->unique(Produk::class, 'barcode', ignoreRecord: true),
                
            TextInput::make('nama')
                ->label('Nama Produk')
                ->required(),
                
            TextInput::make('harga_modal')
                ->label('Harga Modal')
                ->numeric()
                ->prefix('Rp')
                ->required(),
                
            TextInput::make('harga')
                ->label('Harga Jual')
                ->numeric()
                ->prefix('Rp')
                ->required(),
                
            TextInput::make('stok')
                ->label('Stok')
                ->numeric()
                ->default(0)
                ->required(),
                
            FileUpload::make('gambar')
                ->label('Gambar Produk')
                ->image()
                ->directory('produk')
                ->visibility('public'),
                
            RichEditor::make('ket')
                ->label('Keterangan')
                ->columnSpanFull(),
                
            Repeater::make('fasilitas_list')
                ->label('Fasilitas')
                ->schema([
                    TextInput::make('nama')
                        ->label('Nama Fasilitas')
                        ->required(),
                    TextInput::make('value')
                        ->label('Nilai')
                        ->required(),
                ])
                ->visible(fn (Get $get): bool => in_array($get('jenis'), ['KAMAR', 'FASILITAS']))
                ->afterStateHydrated(function (Repeater $component, $state, Set $set) {
                    // Mengubah array fasilitas menjadi repeater items
                    if (is_array($state)) {
                        $items = collect($state)->map(function ($value, $key) {
                            return [
                                'nama' => $key,
                                'value' => $value,
                            ];
                        })->toArray();
                        
                        $set('fasilitas_list', $items);
                    }
                })
                ->dehydrateStateUsing(function ($state) {
                    // Mengubah repeater items menjadi array fasilitas
                    if (is_array($state)) {
                        return collect($state)->mapWithKeys(function ($item) {
                            return [$item['nama'] => $item['value']];
                        })->toArray();
                    }
                    
                    return [];
                }),
                
            Toggle::make('inivarian')
                ->label('Produk Memiliki Varian')
                ->default(false),
                
            Toggle::make('tampil')
                ->label('Tampilkan Produk')
                ->default(true),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    @foreach($produkList as $produk)
        <div class="bg-white rounded-lg shadow overflow-hidden">
            @if($produk->gambar)
                <img src="{{ asset('storage/' . $produk->gambar) }}" class="w-full h-40 object-cover" alt="{{ $produk->nama }}">
            @else
                <div class="w-full h-40 bg-gray-200 flex items-center justify-center text-gray-500">
                    <span>Tidak ada gambar</span>
                </div>
            @endif
            
            <div class="p-4">
                <h3 class="font-medium text-gray-900">{{ $produk->nama }}</h3>
                
                @if($produk->hasVariants())
                    <div class="text-sm text-gray-600 mt-1">
                        {{ $produk->varian->count() }} varian tersedia
                    </div>
                    <div class="mt-2">
                        <span class="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                            Mulai dari Rp {{ number_format($produk->varian->min('harga'), 0, ',', '.') }}
                        </span>
                    </div>
                @else
                    <div class="text-sm text-gray-600 mt-1">
                        Stok: {{ $produk->stok }}
                    </div>
                    <div class="mt-2 font-bold text-blue-600">
                        Rp {{ number_format($produk->harga, 0, ',', '.') }}
                    </div>
                @endif
                
                <div class="mt-4 flex justify-between">
                    <button class="text-blue-600 hover:text-blue-800" onclick="showDetail({{ $produk->id }})">
                        Detail
                    </button>
                    
                    @if(!$produk->hasVariants())
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded" onclick="addToCart({{ $produk->id }})">
                            Tambah
                        </button>
                    @else
                        <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded" onclick="showVariants({{ $produk->id }})">
                            Pilih Varian
                        </button>
                    @endif
                </div>
            </div>
        </div>
    @endforeach
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\Produk;
use Livewire\Component;

class ProdukDetail extends Component
{
    public $produkId;
    public $produk;
    public $varianList = [];
    public $selectedVariantId = null;
    
    protected $listeners = ['showProdukDetail' => 'loadProduk'];
    
    public function loadProduk($produkId)
    {
        $this->produkId = $produkId;
        $this->produk = Produk::with(['kategori', 'varian'])->find($produkId);
        
        if ($this->produk && $this->produk->inivarian) {
            $this->varianList = $this->produk->varian;
        } else {
            $this->varianList = [];
        }
        
        $this->selectedVariantId = null;
    }
    
    public function selectVariant($variantId)
    {
        $this->selectedVariantId = $variantId;
    }
    
    public function addToCart()
    {
        if ($this->produk->inivarian && $this->selectedVariantId) {
            // Tambahkan varian terpilih ke keranjang
            $this->emit('tambahKeKeranjang', $this->selectedVariantId);
        } elseif (!$this->produk->inivarian) {
            // Tambahkan produk utama ke keranjang
            $this->emit('tambahKeKeranjang', $this->produkId);
        } else {
            // Tampilkan pesan error jika belum memilih varian
            $this->emit('showError', 'Silakan pilih varian produk terlebih dahulu');
            return;
        }
        
        // Tutup modal setelah menambahkan produk ke keranjang
        $this->emit('closeModal');
    }
    
    public function render()
    {
        return view('kasir::livewire.produk-detail');
    }
}
```

---

## Model ProdukKategori

### Deskripsi
Model ini merepresentasikan kategori produk. Kategori dapat memiliki sub-kategori (parent-child relationship).

### Tabel Database
```
produk_kategori
```

### Field Utama
- `id` - ID unik kategori
- `jenis` - Jenis kategori (PRODUK, KAMAR, FASILITAS)
- `nama` - Nama kategori
- `sub` - ID kategori induk (0 jika kategori utama)

### Relasi
```php
// Produk dalam kategori
public function produk()
{
    return $this->hasMany(Produk::class, 'kategori_id');
}

// Sub kategori
public function subKategori()
{
    return $this->hasMany(ProdukKategori::class, 'sub', 'id');
}

// Kategori induk
public function parent()
{
    return $this->belongsTo(ProdukKategori::class, 'sub');
}
```

### Method Penting
```php
// Cek apakah kategori memiliki sub kategori
public function hasChildren()
{
    return $this->subKategori()->count() > 0;
}

// Mendapatkan semua produk dalam kategori dan sub kategorinya
public function getAllProducts()
{
    $categoryIds = [$this->id];
    
    // Tambahkan ID sub kategori
    foreach ($this->subKategori as $subCategory) {
        $categoryIds[] = $subCategory->id;
    }
    
    return Produk::whereIn('kategori_id', $categoryIds)->get();
}

// Mendapatkan path kategori (breadcrumb)
public function getPath()
{
    $path = [$this];
    
    $currentCategory = $this;
    while ($currentCategory->sub != 0) {
        $currentCategory = $currentCategory->parent;
        if ($currentCategory) {
            array_unshift($path, $currentCategory);
        } else {
            break;
        }
    }
    
    return $path;
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('jenis')
                ->label('Jenis Kategori')
                ->options(config('kasir.enums.jenis_produk'))
                ->required(),
                
            TextInput::make('nama')
                ->label('Nama Kategori')
                ->required(),
                
            Select::make('sub')
                ->label('Kategori Induk')
                ->options(function () {
                    return ProdukKategori::where('sub', 0)
                        ->pluck('nama', 'id')
                        ->prepend('Kategori Utama', 0);
                })
                ->default(0)
                ->required(),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Kategori Produk</h3>
    
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        @foreach($kategoriUtama as $kategori)
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-4">
                    <h4 class="font-medium">{{ $kategori->nama }}</h4>
                    <p class="text-sm text-gray-600 mt-1">{{ $kategori->produk->count() }} produk</p>
                    
                    @if($kategori->hasChildren())
                        <div class="mt-4 space-y-2">
                            <p class="text-xs font-medium text-gray-500">Sub Kategori:</p>
                            
                            @foreach($kategori->subKategori as $subKategori)
                                <a href="{{ route('produk.kategori', $subKategori->id) }}" class="block text-sm text-blue-600 hover:text-blue-800">
                                    {{ $subKategori->nama }} ({{ $subKategori->produk->count() }})
                                </a>
                            @endforeach
                        </div>
                    @endif
                    
                    <div class="mt-4">
                        <a href="{{ route('produk.kategori', $kategori->id) }}" class="text-blue-600 hover:text-blue-800 text-sm">
                            Lihat Semua Produk
                        </a>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\ProdukKategori;
use Livewire\Component;

class KategoriFilter extends Component
{
    public $kategoris = [];
    public $selectedKategoriId = null;
    
    protected $listeners = ['refreshKategoris' => 'loadKategoris'];
    
    public function mount()
    {
        $this->loadKategoris();
    }
    
    public function loadKategoris()
    {
        $this->kategoris = ProdukKategori::where('sub', 0)
            ->with('subKategori')
            ->orderBy('nama')
            ->get();
    }
    
    public function selectKategori($kategoriId)
    {
        $this->selectedKategoriId = $kategoriId;
        $this->emit('kategoriSelected', $kategoriId);
    }
    
    public function render()
    {
        return view('kasir::livewire.kategori-filter');
    }
}
```

---

## Model HargaJual

### Deskripsi
Model ini merepresentasikan harga jual produk. Setiap produk dapat memiliki beberapa harga jual dengan ketentuan berbeda (minimal pembelian, tanggal promo, dll).

### Tabel Database
```
harga_jual
```

### Field Utama
- `id` - ID unik harga jual
- `nama` - Nama harga jual (misal: "Harga Promo", "Harga Grosir")
- `harga` - Harga jual
- `minimal` - Minimal pembelian
- `maksimal` - Maksimal pembelian
- `produk_id` - ID produk
- `tgl_mulai` - Tanggal mulai berlaku
- `tgl_selesai` - Tanggal selesai berlaku

### Casts
```php
protected $casts = [
    'tgl_mulai' => 'datetime',
    'tgl_selesai' => 'datetime',
    'harga' => 'integer',
    'minimal' => 'integer',
    'maksimal' => 'integer',
];
```

### Relasi
```php
// Produk
public function produk()
{
    return $this->belongsTo(Produk::class);
}
```

### Method Penting
```php
// Cek apakah harga jual aktif
public function isActive(): bool
{
    $now = now();
    
    // Jika tidak ada tanggal, selalu aktif
    if (!$this->tgl_mulai && !$this->tgl_selesai) {
        return true;
    }
    
    // Jika hanya ada tanggal mulai
    if ($this->tgl_mulai && !$this->tgl_selesai) {
        return $now->gte($this->tgl_mulai);
    }
    
    // Jika hanya ada tanggal selesai
    if (!$this->tgl_mulai && $this->tgl_selesai) {
        return $now->lte($this->tgl_selesai);
    }
    
    // Jika ada keduanya
    return $now->between($this->tgl_mulai, $this->tgl_selesai);
}

// Scope untuk mendapatkan harga aktif
public function scopeActive($query)
{
    $now = now();
    
    return $query->where(function ($q) use ($now) {
        $q->where(function ($q) {
            $q->whereNull('tgl_mulai')->whereNull('tgl_selesai');
        })->orWhere(function ($q) use ($now) {
            $q->whereNull('tgl_selesai')->where('tgl_mulai', '<=', $now);
        })->orWhere(function ($q) use ($now) {
            $q->whereNull('tgl_mulai')->where('tgl_selesai', '>=', $now);
        })->orWhere(function ($q) use ($now) {
            $q->where('tgl_mulai', '<=', $now)->where('tgl_selesai', '>=', $now);
        });
    });
}

// Cek apakah harga berlaku untuk jumlah tertentu
public function isApplicableForQuantity(int $quantity): bool
{
    // Jika tidak ada min/max, berlaku untuk semua jumlah
    if (!$this->minimal && !$this->maksimal) {
        return true;
    }
    
    // Jika hanya ada minimal
    if ($this->minimal && !$this->maksimal) {
        return $quantity >= $this->minimal;
    }
    
    // Jika hanya ada maksimal
    if (!$this->minimal && $this->maksimal) {
        return $quantity <= $this->maksimal;
    }
    
    // Jika ada keduanya
    return $quantity >= $this->minimal && $quantity <= $this->maksimal;
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('produk_id')
                ->label('Produk')
                ->relationship('produk', 'nama')
                ->searchable()
                ->preload()
                ->required(),
                
            TextInput::make('nama')
                ->label('Nama Harga')
                ->required(),
                
            TextInput::make('harga')
                ->label('Harga')
                ->numeric()
                ->prefix('Rp')
                ->required(),
                
            TextInput::make('minimal')
                ->label('Minimal Pembelian')
                ->numeric()
                ->default(1),
                
            TextInput::make('maksimal')
                ->label('Maksimal Pembelian')
                ->numeric()
                ->nullable(),
                
            DateTimePicker::make('tgl_mulai')
                ->label('Tanggal Mulai Berlaku')
                ->nullable(),
                
            DateTimePicker::make('tgl_selesai')
                ->label('Tanggal Selesai Berlaku')
                ->nullable()
                ->after('tgl_mulai'),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Daftar Harga Produk: {{ $produk->nama }}</h3>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nama Harga
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Harga
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ketentuan
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Periode
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach($produk->hargaJual as $harga)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ $harga->nama }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Rp {{ number_format($harga->harga, 0, ',', '.') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            @if($harga->minimal && $harga->maksimal)
                                {{ $harga->minimal }} - {{ $harga->maksimal }} item
                            @elseif($harga->minimal)
                                Min. {{ $harga->minimal }} item
                            @elseif($harga->maksimal)
                                Maks. {{ $harga->maksimal }} item
                            @else
                                Semua pembelian
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            @if($harga->tgl_mulai && $harga->tgl_selesai)
                                {{ $harga->tgl_mulai->format('d/m/Y') }} - {{ $harga->tgl_selesai->format('d/m/Y') }}
                            @elseif($harga->tgl_mulai)
                                Mulai {{ $harga->tgl_mulai->format('d/m/Y') }}
                            @elseif($harga->tgl_selesai)
                                Sampai {{ $harga->tgl_selesai->format('d/m/Y') }}
                            @else
                                Tidak dibatasi
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            @if($harga->isActive())
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Aktif
                                </span>
                            @else
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Tidak Aktif
                                </span>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\HargaJual;
use Ridwans2\Kasir\Models\Produk;
use Livewire\Component;

class HargaJualManager extends Component
{
    public $produkId;
    public $produk;
    public $hargaList = [];
    
    // Form fields
    public $nama;
    public $harga;
    public $minimal = 1;
    public $maksimal;
    public $tglMulai;
    public $tglSelesai;
    
    protected $rules = [
        'nama' => 'required|string|max:100',
        'harga' => 'required|numeric|min:0',
        'minimal' => 'nullable|numeric|min:1',
        'maksimal' => 'nullable|numeric|min:1',
        'tglMulai' => 'nullable|date',
        'tglSelesai' => 'nullable|date|after_or_equal:tglMulai',
    ];
    
    public function mount($produkId)
    {
        $this->produkId = $produkId;
        $this->loadProduk();
        $this->loadHargaList();
    }
    
    public function loadProduk()
    {
        $this->produk = Produk::findOrFail($this->produkId);
    }
    
    public function loadHargaList()
    {
        $this->hargaList = HargaJual::where('produk_id', $this->produkId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
    
    public function saveHarga()
    {
        $this->validate();
        
        HargaJual::create([
            'produk_id' => $this->produkId,
            'nama' => $this->nama,
            'harga' => $this->harga,
            'minimal' => $this->minimal,
            'maksimal' => $this->maksimal,
            'tgl_mulai' => $this->tglMulai,
            'tgl_selesai' => $this->tglSelesai,
        ]);
        
        $this->resetForm();
        $this->loadHargaList();
        
        $this->emit('notifikasi', 'Harga jual berhasil ditambahkan');
    }
    
    public function deleteHarga($hargaId)
    {
        HargaJual::destroy($hargaId);
        $this->loadHargaList();
        
        $this->emit('notifikasi', 'Harga jual berhasil dihapus');
    }
    
    public function resetForm()
    {
        $this->nama = null;
        $this->harga = null;
        $this->minimal = 1;
        $this->maksimal = null;
        $this->tglMulai = null;
        $this->tglSelesai = null;
    }
    
    public function render()
    {
        return view('kasir::livewire.harga-jual-manager');
    }
}
```

---

## Model MetodePembayaran

### Deskripsi
Model ini merepresentasikan metode pembayaran yang tersedia (tunai, kartu kredit, dll).

### Tabel Database
```
metode_pembayaran
```

### Field Utama
- `id` - ID unik metode pembayaran
- `nama` - Nama metode pembayaran
- `jenis` - Jenis metode pembayaran (cash, debit, transfer, kartukredit, qris, api)
- `gambar` - Path gambar/icon metode pembayaran
- `info_tujuan` - Informasi tujuan pembayaran
- `info_pengirim` - Informasi pengirim pembayaran
- `status` - Status aktif (1) atau tidak aktif (0)
- `ket` - Keterangan tambahan

### Casts
```php
protected $casts = [
    'status' => 'boolean',
    'info_pengirim' => 'array',
];
```

### Relasi
```php
// Pembayaran
public function pembayaran()
{
    return $this->hasMany(Pembayaran::class);
}
```

### Method Penting
```php
// Mendapatkan icon metode pembayaran
public function getIcon()
{
    $icons = [
        'cash' => 'heroicon-o-cash',
        'debit' => 'heroicon-o-credit-card',
        'transfer' => 'heroicon-o-currency-dollar',
        'kartukredit' => 'heroicon-o-credit-card',
        'qris' => 'heroicon-o-qr-code',
        'api' => 'heroicon-o-globe-alt',
    ];
    
    return $icons[$this->jenis] ?? 'heroicon-o-credit-card';
}

// Mendapatkan label jenis
public function getJenisLabel()
{
    $labels = [
        'cash' => 'Tunai',
        'debit' => 'Kartu Debit',
        'transfer' => 'Transfer Bank',
        'kartukredit' => 'Kartu Kredit',
        'qris' => 'QRIS',
        'api' => 'Payment Gateway',
    ];
    
    return $labels[$this->jenis] ?? ucfirst($this->jenis);
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            TextInput::make('nama')
                ->label('Nama Metode Pembayaran')
                ->required(),
                
            Select::make('jenis')
                ->label('Jenis Metode')
                ->options(config('kasir.enums.metode_pembayaran'))
                ->required(),
                
            FileUpload::make('gambar')
                ->label('Icon/Gambar')
                ->image()
                ->directory('metode-pembayaran')
                ->visibility('public'),
                
            RichEditor::make('info_tujuan')
                ->label('Informasi Tujuan Pembayaran')
                ->helperText('Informasi rekening, nomor virtual account, dll'),
                
            Repeater::make('info_pengirim')
                ->label('Informasi Pengirim')
                ->schema([
                    TextInput::make('label')
                        ->required(),
                    TextInput::make('value')
                        ->required(),
                ])
                ->columns(2),
                
            Textarea::make('ket')
                ->label('Keterangan Tambahan'),
                
            Toggle::make('status')
                ->label('Aktif')
                ->default(true),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    @foreach($metodePembayaran as $metode)
        <div class="bg-white rounded-lg shadow overflow-hidden {{ $metode->status ? '' : 'opacity-50' }}">
            <div class="p-4">
                <div class="flex items-center">
                    @if($metode->gambar)
                        <img src="{{ asset('storage/' . $metode->gambar) }}" class="w-8 h-8 mr-3" alt="{{ $metode->nama }}">
                    @else
                        <div class="w-8 h-8 mr-3 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                @if($metode->jenis === 'cash')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                @elseif($metode->jenis === 'debit' || $metode->jenis === 'kartukredit')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                @elseif($metode->jenis === 'transfer')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                @elseif($metode->jenis === 'qris')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                @endif
                            </svg>
                        </div>
                    @endif
                    <h4 class="font-medium">{{ $metode->nama }}</h4>
                </div>
                
                <div class="mt-2">
                    <span class="inline-block px-2 py-1 text-xs rounded {{ $metode->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $metode->status ? 'Aktif' : 'Tidak Aktif' }}
                    </span>
                    <span class="inline-block px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                        {{ $metode->getJenisLabel() }}
                    </span>
                </div>
                
                @if($metode->ket)
                    <div class="mt-2 text-sm text-gray-600">
                        {{ $metode->ket }}
                    </div>
                @endif
            </div>
        </div>
    @endforeach
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\MetodePembayaran;
use Livewire\Component;

class PembayaranMethod extends Component
{
    public $metodePembayaran = [];
    public $selectedMetode = null;
    
    protected $listeners = ['refreshMetodePembayaran' => 'loadMetodePembayaran'];
    
    public function mount()
    {
        $this->loadMetodePembayaran();
    }
    
    public function loadMetodePembayaran()
    {
        $this->metodePembayaran = MetodePembayaran::where('status', true)
            ->orderBy('nama')
            ->get();
    }
    
    public function selectMetode($metodeId)
    {
        $this->selectedMetode = $metodeId;
        $this->emit('metodePembayaranDipilih', $metodeId);
    }
    
    public function render()
    {
        return view('kasir::livewire.pembayaran-method');
    }
}
```

---

## Model Pembayaran

### Deskripsi
Model ini merepresentasikan data pembayaran dalam transaksi penjualan.

### Tabel Database
```
pembayaran
```

### Field Utama
- `id` - ID unik pembayaran
- `nama` - Nama pembayaran
- `penjualan_id` - ID penjualan
- `metode_pembayaran_id` - ID metode pembayaran
- `pengirim` - Data pengirim (array)
- `tujuan` - Data tujuan pembayaran
- `jumlah` - Jumlah pembayaran
- `bukti` - Path file bukti pembayaran
- `status` - Status pembayaran (SELESAI, PENDING)
- `ket` - Keterangan tambahan

### Casts
```php
protected $casts = [
    'jumlah' => 'integer',
    'pengirim' => 'array',
];
```

### Relasi
```php
// Penjualan
public function penjualan()
{
    return $this->belongsTo(Penjualan::class);
}

// Metode pembayaran
public function metodePembayaran()
{
    return $this->belongsTo(MetodePembayaran::class);
}
```

### Method Penting
```php
// Cek apakah pembayaran berhasil
public function isSuccess()
{
    return $this->status === 'SELESAI' || $this->status === 'berhasil';
}

// Cek apakah pembayaran tunai
public function isCash()
{
    return $this->metodePembayaran && $this->metodePembayaran->jenis === 'cash';
}

// Mendapatkan nama metode pembayaran
public function getMetodeName()
{
    return $this->metodePembayaran ? $this->metodePembayaran->nama : 'Tidak diketahui';
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('penjualan_id')
                ->label('Transaksi')
                ->relationship('penjualan', 'penjualan_invoice')
                ->searchable()
                ->preload()
                ->required(),
                
            Select::make('metode_pembayaran_id')
                ->label('Metode Pembayaran')
                ->relationship('metodePembayaran', 'nama')
                ->searchable()
                ->preload()
                ->required(),
                
            TextInput::make('nama')
                ->label('Nama Pembayaran')
                ->required(),
                
            TextInput::make('jumlah')
                ->label('Jumlah Pembayaran')
                ->numeric()
                ->prefix('Rp')
                ->required(),
                
            Repeater::make('pengirim')
                ->label('Informasi Pengirim')
                ->schema([
                    TextInput::make('nama')
                        ->label('Nama')
                        ->required(),
                    TextInput::make('value')
                        ->label('Nilai')
                        ->required(),
                ])
                ->columns(2),
                
            Textarea::make('tujuan')
                ->label('Informasi Tujuan'),
                
            FileUpload::make('bukti')
                ->label('Bukti Pembayaran')
                ->image()
                ->directory('bukti-pembayaran')
                ->visibility('public'),
                
            Select::make('status')
                ->label('Status Pembayaran')
                ->options([
                    'berhasil' => 'Berhasil',
                    'pending' => 'Pending',
                    'gagal' => 'Gagal',
                ])
                ->default('berhasil')
                ->required(),
                
            Textarea::make('ket')
                ->label('Keterangan'),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Detail Pembayaran</h3>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 mr-3">
                    @if($pembayaran->metodePembayaran && $pembayaran->metodePembayaran->gambar)
                        <img src="{{ asset('storage/' . $pembayaran->metodePembayaran->gambar) }}" class="w-10 h-10" alt="{{ $pembayaran->metodePembayaran->nama }}">
                    @else
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                @if($pembayaran->metodePembayaran && $pembayaran->metodePembayaran->jenis === 'cash')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                @endif
                            </svg>
                        </div>
                    @endif
                </div>
                <div>
                    <h4 class="font-medium">{{ $pembayaran->nama }}</h4>
                    <p class="text-sm text-gray-600">{{ $pembayaran->getMetodeName() }}</p>
                </div>
                
                <div class="ml-auto">
                    <span class="px-2 py-1 text-sm rounded {{ $pembayaran->isSuccess() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($pembayaran->status) }}
                    </span>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Jumlah Pembayaran</h5>
                        <p class="text-lg font-bold text-gray-900">Rp {{ number_format($pembayaran->jumlah, 0, ',', '.') }}</p>
                    </div>
                    
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Tanggal</h5>
                        <p class="text-gray-900">{{ $pembayaran->created_at->format('d M Y H:i') }}</p>
                    </div>
                    
                    @if($pembayaran->tujuan)
                        <div class="md:col-span-2">
                            <h5 class="text-sm font-medium text-gray-500">Informasi Tujuan</h5>
                            <div class="mt-1 text-gray-900 p-3 bg-gray-50 rounded">
                                {!! $pembayaran->tujuan !!}
                            </div>
                        </div>
                    @endif
                    
                    @if($pembayaran->pengirim)
                        <div class="md:col-span-2">
                            <h5 class="text-sm font-medium text-gray-500">Informasi Pengirim</h5>
                            <div class="mt-1 space-y-2">
                                @foreach($pembayaran->pengirim as $key => $value)
                                    <div class="flex">
                                        <span class="font-medium mr-2">{{ is_string($key) ? $key : 'Info' }}:</span>
                                        <span>{{ $value }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    @if($pembayaran->bukti)
                        <div class="md:col-span-2">
                            <h5 class="text-sm font-medium text-gray-500">Bukti Pembayaran</h5>
                            <div class="mt-1">
                                <a href="{{ asset('storage/' . $pembayaran->bukti) }}" target="_blank" class="inline-block">
                                    <img src="{{ asset('storage/' . $pembayaran->bukti) }}" class="max-w-xs rounded-lg border" alt="Bukti Pembayaran">
                                </a>
                            </div>
                        </div>
                    @endif
                    
                    @if($pembayaran->ket)
                        <div class="md:col-span-2">
                            <h5 class="text-sm font-medium text-gray-500">Keterangan</h5>
                            <p class="text-gray-900">{{ $pembayaran->ket }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\MetodePembayaran;
use Ridwans2\Kasir\Models\Pembayaran;
use Livewire\Component;
use Livewire\WithFileUploads;

class PembayaranForm extends Component
{
    use WithFileUploads;
    
    public $penjualanId;
    public $selectedMetodeId;
    public $jumlahBayar;
    public $bukti;
    public $metodePembayaran = [];
    
    protected $rules = [
        'selectedMetodeId' => 'required',
        'jumlahBayar' => 'required|numeric|min:0',
        'bukti' => 'nullable|image|max:2048',
    ];
    
    public function mount($penjualanId, $totalTagihan)
    {
        $this->penjualanId = $penjualanId;
        $this->jumlahBayar = $totalTagihan;
        $this->loadMetodePembayaran();
    }
    
    public function loadMetodePembayaran()
    {
        $this->metodePembayaran = MetodePembayaran::where('status', true)
            ->orderBy('nama')
            ->get();
            
        // Set metode default ke cash jika tersedia
        $cash = $this->metodePembayaran->firstWhere('jenis', 'cash');
        if ($cash) {
            $this->selectedMetodeId = $cash->id;
        } elseif ($this->metodePembayaran->isNotEmpty()) {
            $this->selectedMetodeId = $this->metodePembayaran->first()->id;
        }
    }
    
    public function simpanPembayaran()
    {
        $this->validate();
        
        $buktiPath = null;
        if ($this->bukti) {
            $buktiPath = $this->bukti->store('bukti-pembayaran', 'public');
        }
        
        $metode = MetodePembayaran::find($this->selectedMetodeId);
        
        Pembayaran::create([
            'penjualan_id' => $this->penjualanId,
            'metode_pembayaran_id' => $this->selectedMetodeId,
            'nama' => 'Pembayaran via ' . ($metode ? $metode->nama : 'Unknown'),
            'jumlah' => $this->jumlahBayar,
            'bukti' => $buktiPath,
            'status' => 'berhasil',
        ]);
        
        $this->emit('pembayaranBerhasil');
        $this->reset(['bukti']);
    }
    
    public function render()
    {
        return view('kasir::livewire.pembayaran-form');
    }
}
```

---

## Model Tamu

### Deskripsi
Model ini merepresentasikan pelanggan atau member yang melakukan transaksi.

### Tabel Database
```
tamu
```

### Field Utama
- `id` - ID unik tamu
- `jenis_tamu` - Jenis tamu (HOTEL, KASIR)
- `nama` - Nama tamu
- `email` - Email tamu
- `telpon` - Nomor telepon
- `jenis_identitas` - Jenis identitas (KTP, SIM, Passport)
- `no_identitas` - Nomor identitas
- `foto_identitas` - Path file foto identitas

### Relasi
```php
// Penjualan
public function penjualan()
{
    return $this->hasMany(Penjualan::class);
}
```

### Method Penting
```php
// Total transaksi
public function totalTransaksi()
{
    return $this->penjualan()->where('status', 'SELESAI')->count();
}

// Total nominal transaksi
public function totalNominal()
{
    return $this->penjualan()
        ->where('status', 'SELESAI')
        ->sum('jumlah_pembayaran');
}

// Transaksi terakhir
public function lastTransaction()
{
    return $this->penjualan()
        ->where('status', 'SELESAI')
        ->latest()
        ->first();
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('jenis_tamu')
                ->label('Jenis Tamu')
                ->options(config('kasir.enums.jenis_tamu'))
                ->default('KASIR')
                ->required(),
                
            TextInput::make('nama')
                ->label('Nama Lengkap')
                ->required(),
                
            TextInput::make('email')
                ->label('Email')
                ->email(),
                
            TextInput::make('telpon')
                ->label('Nomor Telepon')
                ->tel(),
                
            Select::make('jenis_identitas')
                ->label('Jenis Identitas')
                ->options([
                    'KTP' => 'KTP',
                    'SIM' => 'SIM',
                    'Passport' => 'Passport',
                    'Lainnya' => 'Lainnya',
                ]),
                
            TextInput::make('no_identitas')
                ->label('Nomor Identitas'),
                
            FileUpload::make('foto_identitas')
                ->label('Foto Identitas')
                ->image()
                ->directory('identitas')
                ->visibility('private'),
                
            Grid::make()
                ->schema([
                    TextInput::make('alamat')
                        ->label('Alamat'),
                        
                    TextInput::make('kota')
                        ->label('Kota'),
                ])
                ->columns(2),
                
            Textarea::make('catatan')
                ->label('Catatan'),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Profil Pelanggan</h3>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 mr-3">
                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                        <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-lg">{{ $tamu->nama }}</h4>
                    <p class="text-sm text-gray-600">
                        <span class="inline-block px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                            {{ $tamu->jenis_tamu }}
                        </span>
                        
                        @if($tamu->email)
                            <span class="ml-2">{{ $tamu->email }}</span>
                        @endif
                        
                        @if($tamu->telpon)
                            <span class="ml-2">{{ $tamu->telpon }}</span>
                        @endif
                    </p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-gray-200 pt-4">
                @if($tamu->jenis_identitas || $tamu->no_identitas)
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Identitas</h5>
                        <p class="text-gray-900">
                            {{ $tamu->jenis_identitas ?: 'ID' }}: {{ $tamu->no_identitas ?: '-' }}
                        </p>
                    </div>
                @endif
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Total Transaksi</h5>
                    <p class="text-gray-900">{{ $tamu->totalTransaksi() }} transaksi</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Total Nominal</h5>
                    <p class="text-gray-900">Rp {{ number_format($tamu->totalNominal(), 0, ',', '.') }}</p>
                </div>
                
                @if($tamu->lastTransaction())
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Transaksi Terakhir</h5>
                        <p class="text-gray-900">{{ $tamu->lastTransaction()->created_at->format('d M Y') }}</p>
                    </div>
                @endif
                
                @if($tamu->catatan)
                    <div class="md:col-span-2">
                        <h5 class="text-sm font-medium text-gray-500">Catatan</h5>
                        <p class="text-gray-900">{{ $tamu->catatan }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\Tamu;
use Livewire\Component;

class CariTamu extends Component
{
    public $search = '';
    public $tamuList = [];
    public $selectedTamuId = null;
    
    public function updatedSearch()
    {
        if (strlen($this->search) >= 3) {
            $this->tamuList = Tamu::where('nama', 'like', '%' . $this->search . '%')
                ->orWhere('email', 'like', '%' . $this->search . '%')
                ->orWhere('telpon', 'like', '%' . $this->search . '%')
                ->orWhere('no_identitas', 'like', '%' . $this->search . '%')
                ->get();
        } else {
            $this->tamuList = [];
        }
    }
    
    public function selectTamu($tamuId)
    {
        $this->selectedTamuId = $tamuId;
        $this->emit('tamuDipilih', $tamuId);
    }
    
    public function tambahTamu()
    {
        $this->emit('tambahTamuBaru', $this->search);
    }
    
    public function render()
    {
        return view('kasir::livewire.cari-tamu');
    }
}
```

---

## Model PenjualanShift

### Deskripsi
Model ini merepresentasikan shift penjualan (periode kerja kasir).

### Tabel Database
```
penjualan_shift
```

### Field Utama
- `id` - ID unik shift
- `jenis` - Jenis shift (KASIR)
- `jadwal` - Jadwal shift (pagi, siang, sore, malam)
- `buka` - Waktu buka shift
- `tutup` - Waktu tutup shift
- `saldo_awal` - Saldo awal
- `saldo_akhir` - Saldo akhir
- `selisih_saldo` - Selisih saldo
- `diserahkan` - ID user yang menerima shift berikutnya
- `ket` - Keterangan
- `status` - Status shift (BUKA, TUTUP)

### Casts
```php
protected $casts = [
    'buka' => 'datetime',
    'tutup' => 'datetime',
    'saldo_awal' => 'integer',
    'saldo_akhir' => 'integer',
    'selisih_saldo' => 'integer',
    'diserahkan' => 'integer',
];
```

### Relasi
```php
// Penjualan dalam shift
public function penjualan()
{
    return $this->hasMany(Penjualan::class, 'shift_id');
}

// User yang menerima shift
public function penerima()
{
    return $this->belongsTo(User::class, 'diserahkan');
}
```

### Method Penting
```php
// Total penjualan dalam shift
public function totalPenjualan()
{
    return $this->penjualan()
        ->where('status', 'SELESAI')
        ->sum('jumlah_pembayaran');
}

// Total penjualan tunai dalam shift
public function totalCash()
{
    return $this->penjualan()
        ->where('status', 'SELESAI')
        ->whereHas('pembayaran', function ($query) {
            $query->whereHas('metodePembayaran', function ($q) {
                $q->where('jenis', 'cash');
            });
        })
        ->sum('jumlah_pembayaran');
}

// Jumlah transaksi dalam shift
public function jumlahTransaksi()
{
    return $this->penjualan()
        ->where('status', 'SELESAI')
        ->count();
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('jenis')
                ->label('Jenis Shift')
                ->options([
                    'KASIR' => 'Kasir',
                ])
                ->default('KASIR')
                ->required(),
                
            Select::make('jadwal')
                ->label('Jadwal Shift')
                ->options([
                    'pagi' => 'Pagi',
                    'siang' => 'Siang',
                    'sore' => 'Sore',
                    'malam' => 'Malam',
                ])
                ->required(),
                
            DateTimePicker::make('buka')
                ->label('Waktu Buka')
                ->required()
                ->default(now()),
                
            DateTimePicker::make('tutup')
                ->label('Waktu Tutup')
                ->after('buka'),
                
            TextInput::make('saldo_awal')
                ->label('Saldo Awal')
                ->numeric()
                ->prefix('Rp')
                ->default(0)
                ->required(),
                
            TextInput::make('saldo_akhir')
                ->label('Saldo Akhir')
                ->numeric()
                ->prefix('Rp')
                ->default(0),
                
            Select::make('diserahkan')
                ->label('Diserahkan Kepada')
                ->relationship('penerima', 'name')
                ->searchable()
                ->preload(),
                
            Textarea::make('ket')
                ->label('Keterangan'),
                
            Select::make('status')
                ->label('Status')
                ->options([
                    'BUKA' => 'Buka',
                    'TUTUP' => 'Tutup',
                ])
                ->default('BUKA')
                ->required(),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Detail Shift</h3>
    
    <div class="bg-white rounded-lg shadow">
        <div class="p-4">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h4 class="font-medium">Shift {{ ucfirst($shift->jadwal) }}</h4>
                    <p class="text-sm text-gray-600">
                        <span class="inline-block px-2 py-1 text-xs rounded {{ $shift->status === 'BUKA' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $shift->status }}
                        </span>
                    </p>
                </div>
                
                <div class="text-right">
                    <p class="text-sm text-gray-600">Periode:</p>
                    <p class="font-medium">
                        {{ $shift->buka->format('d M Y H:i') }}
                        @if($shift->tutup)
                            - {{ $shift->tutup->format('d M Y H:i') }}
                        @endif
                    </p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-gray-200 pt-4">
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Saldo Awal</h5>
                    <p class="text-gray-900">Rp {{ number_format($shift->saldo_awal, 0, ',', '.') }}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Saldo Akhir</h5>
                    <p class="text-gray-900">
                        @if($shift->status === 'TUTUP')
                            Rp {{ number_format($shift->saldo_akhir, 0, ',', '.') }}
                        @else
                            -
                        @endif
                    </p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Total Penjualan</h5>
                    <p class="text-gray-900">Rp {{ number_format($shift->totalPenjualan(), 0, ',', '.') }}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Total Cash</h5>
                    <p class="text-gray-900">Rp {{ number_format($shift->totalCash(), 0, ',', '.') }}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Jumlah Transaksi</h5>
                    <p class="text-gray-900">{{ $shift->jumlahTransaksi() }} transaksi</p>
                </div>
                
                @if($shift->status === 'TUTUP' && $shift->penerima)
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Diserahkan Kepada</h5>
                        <p class="text-gray-900">{{ $shift->penerima->name }}</p>
                    </div>
                @endif
                
                @if($shift->status === 'TUTUP' && $shift->selisih_saldo !== null)
                    <div class="md:col-span-2">
                        <h5 class="text-sm font-medium text-gray-500">Selisih Saldo</h5>
                        <p class="text-gray-900 {{ $shift->selisih_saldo < 0 ? 'text-red-600' : 'text-green-600' }}">
                            Rp {{ number_format($shift->selisih_saldo, 0, ',', '.') }}
                        </p>
                    </div>
                @endif
                
                @if($shift->ket)
                    <div class="md:col-span-2">
                        <h5 class="text-sm font-medium text-gray-500">Keterangan</h5>
                        <p class="text-gray-900">{{ $shift->ket }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\PenjualanShift;
use Livewire\Component;

class ShiftManager extends Component
{
    public $activeShift = null;
    public $jadwal = 'pagi';
    public $saldoAwal = 0;
    public $saldoAkhir = 0;
    public $keterangan = '';
    public $penerimaId = null;
    public $users = [];
    
    protected $rules = [
        'jadwal' => 'required|in:pagi,siang,sore,malam',
        'saldoAwal' => 'required|numeric|min:0',
        'saldoAkhir' => 'nullable|numeric|min:0',
        'keterangan' => 'nullable|string',
        'penerimaId' => 'nullable|exists:users,id',
    ];
    
    public function mount()
    {
        $this->checkActiveShift();
        $this->loadUsers();
    }
    
    public function checkActiveShift()
    {
        $this->activeShift = PenjualanShift::where('status', 'BUKA')
            ->latest('id')
            ->first();
    }
    
    public function loadUsers()
    {
        $this->users = \App\Models\User::all();
    }
    
    public function bukaShift()
    {
        $this->validate([
            'jadwal' => 'required|in:pagi,siang,sore,malam',
            'saldoAwal' => 'required|numeric|min:0',
        ]);
        
        PenjualanShift::create([
            'jenis' => 'KASIR',
            'jadwal' => $this->jadwal,
            'buka' => now(),
            'saldo_awal' => $this->saldoAwal,
            'status' => 'BUKA',
        ]);
        
        $this->reset(['jadwal', 'saldoAwal']);
        $this->checkActiveShift();
        
        $this->emit('notifikasi', 'Shift berhasil dibuka');
    }
    
    public function tutupShift()
    {
        $this->validate([
            'saldoAkhir' => 'required|numeric|min:0',
            'penerimaId' => 'required|exists:users,id',
        ]);
        
        if (!$this->activeShift) {
            $this->emit('notifikasi', 'Tidak ada shift aktif yang perlu ditutup', 'error');
            return;
        }
        
        $selisihSaldo = $this->saldoAkhir - ($this->activeShift->saldo_awal + $this->activeShift->totalCash());
        
        $this->activeShift->update([
            'tutup' => now(),
            'saldo_akhir' => $this->saldoAkhir,
            'selisih_saldo' => $selisihSaldo,
            'diserahkan' => $this->penerimaId,
            'ket' => $this->keterangan,
            'status' => 'TUTUP',
        ]);
        
        $this->reset(['saldoAkhir', 'penerimaId', 'keterangan']);
        $this->checkActiveShift();
        
        $this->emit('notifikasi', 'Shift berhasil ditutup');
        $this->emit('refreshShiftInfo');
    }
    
    public function render()
    {
        return view('kasir::livewire.shift-manager');
    }
}
```

---

## Model Refund

### Deskripsi
Model ini merepresentasikan refund/pengembalian.

### Tabel Database
```
refund
```

## Model Refund (continued)

### Field Utama (continued)
- `jenis_pesanan` - Jenis pesanan (makanditempat, takeaway, delivery)
- `nama` - Nama/Meja
- `nama_tamu` - Nama tamu
- `tamu_id` - ID tamu
- `jumlah_pembayaran` - Total pembayaran
- `metode_pembayaran` - Metode pembayaran
- `status_pembayaran` - Status pembayaran
- `ket_pembayaran` - Keterangan pembayaran
- `ket_pesanan` - Keterangan pesanan
- `karyawan_id` - ID karyawan/user

### Relasi
```php
// Penjualan yang direfund
public function penjualan()
{
    return $this->belongsTo(Penjualan::class);
}

// Tamu/pelanggan
public function tamu()
{
    return $this->belongsTo(Tamu::class);
}

// User/karyawan
public function user()
{
    return $this->belongsTo(User::class, 'karyawan_id');
}

// Item refund
public function refundTransaksi()
{
    return $this->hasMany(RefundTransaksi::class);
}
```

### Method Penting
```php
// Total item refund
public function totalItem()
{
    return $this->refundTransaksi->sum('jumlah');
}

// Total nominal refund
public function totalRefund()
{
    return $this->refundTransaksi->sum(function ($item) {
        return $item->harga * $item->jumlah;
    });
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('penjualan_id')
                ->label('Penjualan')
                ->relationship('penjualan', 'penjualan_invoice')
                ->searchable()
                ->preload()
                ->required(),
                
            Hidden::make('kategori')
                ->default('KASIR'),
                
            Select::make('jenis_pesanan')
                ->label('Jenis Pesanan')
                ->options(config('kasir.enums.jenis_pesanan'))
                ->required(),
                
            TextInput::make('nama_tamu')
                ->label('Nama Pelanggan'),
                
            Select::make('tamu_id')
                ->label('Member')
                ->relationship('tamu', 'nama')
                ->searchable(),
                
            TextInput::make('jumlah_pembayaran')
                ->label('Jumlah Pembayaran')
                ->numeric()
                ->prefix('Rp')
                ->required(),
                
            Select::make('metode_pembayaran')
                ->label('Metode Pengembalian')
                ->options(MetodePembayaran::pluck('nama', 'id'))
                ->required(),
                
            Select::make('status_pembayaran')
                ->label('Status')
                ->options([
                    'SELESAI' => 'Selesai',
                    'PENDING' => 'Pending',
                ])
                ->default('SELESAI')
                ->required(),
                
            Textarea::make('ket_pembayaran')
                ->label('Keterangan Pembayaran'),
                
            Textarea::make('ket_pesanan')
                ->label('Keterangan Pesanan'),
                
            Hidden::make('karyawan_id')
                ->default(fn() => auth()->id()),
        ]);
}
```

#### Dalam Blade View
```blade
<div class="space-y-4">
    <h3 class="text-lg font-medium">Detail Refund</h3>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-4">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 mr-3">
                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium">Refund dari {{ $refund->penjualan->penjualan_invoice }}</h4>
                    <p class="text-sm text-gray-600">{{ $refund->created_at->format('d M Y H:i') }}</p>
                </div>
                
                <div class="ml-auto">
                    <span class="px-2 py-1 text-sm rounded {{ $refund->status_pembayaran === 'SELESAI' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ $refund->status_pembayaran }}
                    </span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-gray-200 pt-4">
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Pelanggan</h5>
                    <p class="text-gray-900">{{ $refund->nama_tamu ?: ($refund->tamu->nama ?? '-') }}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Jenis Pesanan</h5>
                    <p class="text-gray-900">
                        @if($refund->jenis_pesanan === 'makanditempat')
                            Makan di Tempat (Meja {{ $refund->nama }})
                        @elseif($refund->jenis_pesanan === 'takeaway')
                            Take Away
                        @elseif($refund->jenis_pesanan === 'delivery')
                            Delivery
                        @else
                            {{ $refund->jenis_pesanan }}
                        @endif
                    </p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Jumlah Pembayaran</h5>
                    <p class="text-gray-900">Rp {{ number_format($refund->jumlah_pembayaran, 0, ',', '.') }}</p>
                </div>
                
                <div>
                    <h5 class="text-sm font-medium text-gray-500">Metode Pengembalian</h5>
                    <p class="text-gray-900">{{ $refund->metode_pembayaran }}</p>
                </div>
                
                @if($refund->ket_pembayaran)
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Keterangan Pembayaran</h5>
                        <p class="text-gray-900">{{ $refund->ket_pembayaran }}</p>
                    </div>
                @endif
                
                @if($refund->ket_pesanan)
                    <div>
                        <h5 class="text-sm font-medium text-gray-500">Keterangan Pesanan</h5>
                        <p class="text-gray-900">{{ $refund->ket_pesanan }}</p>
                    </div>
                @endif
            </div>
            
            <div class="mt-6">
                <h5 class="text-sm font-medium text-gray-500 mb-2">Item Refund</h5>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Item
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Harga
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jumlah
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Alasan
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($refund->refundTransaksi as $item)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $item->nama_item }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                                        Rp {{ number_format($item->harga, 0, ',', '.') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                                        {{ $item->jumlah }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500">
                                        Rp {{ number_format($item->harga * $item->jumlah, 0, ',', '.') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $item->alasan }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="bg-gray-50">
                                <td colspan="3" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                                    Total Refund:
                                </td>
                                <td class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                                    Rp {{ number_format($refund->totalRefund(), 0, ',', '.') }}
                                </td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### Dalam Livewire Component
```php
use Ridwans2\Kasir\Models\Penjualan;
use Ridwans2\Kasir\Models\Refund;
use Ridwans2\Kasir\Models\RefundTransaksi;
use Livewire\Component;

class BuatRefund extends Component
{
    public $penjualanId;
    public $penjualan;
    public $selectedItems = [];
    public $alasan = [];
    public $metodePembayaran;
    public $keterangan;
    
    protected $rules = [
        'penjualanId' => 'required|exists:penjualan,id',
        'selectedItems' => 'required|array|min:1',
        'selectedItems.*' => 'required|numeric|min:1',
        'alasan.*' => 'required|string',
        'metodePembayaran' => 'required|string',
        'keterangan' => 'nullable|string',
    ];
    
    public function mount($penjualanId)
    {
        $this->penjualanId = $penjualanId;
        $this->loadPenjualan();
    }
    
    public function loadPenjualan()
    {
        $this->penjualan = Penjualan::with('transaksi')->findOrFail($this->penjualanId);
    }
    
    public function simpanRefund()
    {
        $this->validate();
        
        // Hitung total refund
        $totalRefund = 0;
        foreach ($this->penjualan->transaksi as $index => $item) {
            if (isset($this->selectedItems[$index]) && $this->selectedItems[$index] > 0) {
                $jumlah = min($this->selectedItems[$index], $item->jumlah);
                $totalRefund += $item->harga * $jumlah;
            }
        }
        
        // Buat refund
        $refund = Refund::create([
            'penjualan_id' => $this->penjualanId,
            'kategori' => 'KASIR',
            'jenis_pesanan' => $this->penjualan->jenis_pesanan,
            'nama' => $this->penjualan->nama,
            'nama_tamu' => $this->penjualan->nama_tamu,
            'tamu_id' => $this->penjualan->tamu_id,
            'jumlah_pembayaran' => $totalRefund,
            'metode_pembayaran' => $this->metodePembayaran,
            'status_pembayaran' => 'SELESAI',
            'ket_pembayaran' => $this->keterangan,
            'ket_pesanan' => $this->penjualan->ket_pesanan,
            'karyawan_id' => auth()->id(),
        ]);
        
        // Buat detail refund
        foreach ($this->penjualan->transaksi as $index => $item) {
            if (isset($this->selectedItems[$index]) && $this->selectedItems[$index] > 0) {
                $jumlah = min($this->selectedItems[$index], $item->jumlah);
                
                RefundTransaksi::create([
                    'refund_id' => $refund->id,
                    'produk_id' => $item->produk_id,
                    'nama_item' => $item->nama_item,
                    'harga' => $item->harga,
                    'jumlah' => $jumlah,
                    'alasan' => $this->alasan[$index] ?? 'Pengembalian barang',
                ]);
            }
        }
        
        $this->emit('refundBerhasil');
        return redirect()->route('refund.view', $refund->id);
    }
    
    public function render()
    {
        return view('kasir::livewire.buat-refund');
    }
}
```

---

## Model RefundTransaksi

### Deskripsi
Model ini merepresentasikan item dalam transaksi refund.

### Tabel Database
```
refund_transaksi
```

### Field Utama
- `id` - ID unik refund transaksi
- `refund_id` - ID refund
- `produk_id` - ID produk
- `nama_item` - Nama item
- `harga` - Harga jual
- `jumlah` - Jumlah item
- `alasan` - Alasan refund

### Relasi
```php
// Refund
public function refund()
{
    return $this->belongsTo(Refund::class);
}

// Produk
public function produk()
{
    return $this->belongsTo(Produk::class);
}
```

### Method Penting
```php
// Total harga item
public function totalHarga()
{
    return $this->harga * $this->jumlah;
}
```

### Contoh Penggunaan

#### Dalam Filament Resource
```php
public static function table(Table $table): Table
{
    return $table
        ->columns([
            TextColumn::make('refund.id')
                ->label('Refund ID')
                ->sortable(),
                
            TextColumn::make('nama_item')
                ->label('Item')
                ->searchable(),
                
            TextColumn::make('harga')
                ->money('IDR')
                ->sortable(),
                
            TextColumn::make('jumlah')
                ->sortable(),
                
            TextColumn::make('totalHarga')
                ->money('IDR')
                ->state(fn (RefundTransaksi $record): float => $record->harga * $record->jumlah),
                
            TextColumn::make('alasan')
                ->label('Alasan'),
                
            TextColumn::make('created_at')
                ->dateTime('d M Y H:i')
                ->sortable(),
        ]);
}
```

---

## Model PrinterSetting

### Deskripsi
Model ini merepresentasikan konfigurasi printer.

### Tabel Database
```
printer_settings
```

### Field Utama
- `id` - ID unik printer
- `nama_printer` - Nama printer
- `jenis_koneksi` - Jenis koneksi (windows, network, file)
- `alamat_printer` - Alamat printer
- `keterangan` - Keterangan
- `aktif` - Status aktif (1) atau tidak aktif (0)
- `default` - Status default (1) atau tidak (0)
- `struk_layout` - Layout struk (58mm, 80mm)

### Casts
```php
protected $casts = [
    'aktif' => 'boolean',
    'default' => 'boolean',
];
```

### Method Penting
```php
// Mengatur printer ini sebagai default
public function setAsDefault(): bool
{
    // Hapus status default dari semua printer
    self::where('default', true)->update(['default' => false]);
    
    // Set printer ini sebagai default
    return $this->update(['default' => true]);
}

// Mengambil printer default
public static function getDefault(): ?PrinterSetting
{
    return self::where('default', true)->first();
}

// Mengambil semua printer aktif
public static function getActive()
{
    return self::where('aktif', true)->get();
}

// Mendapatkan deskripsi jenis koneksi
public function getJenisKoneksiTextAttribute(): string
{
    return match($this->jenis_koneksi) {
        'windows' => 'Windows Printer',
        'network' => 'Network Printer',
        'file' => 'File Output',
        default => 'Tidak Diketahui',
    };
}

// Mendapatkan deskripsi ukuran struk
public function getStrukLayoutTextAttribute(): string
{
    return match($this->struk_layout) {
        '58mm' => 'Thermal 58mm (32-35 karakter per baris)',
        '80mm' => 'Thermal 80mm (48 karakter per baris)',
        default => 'Thermal 58mm',
    };
}
```

### Contoh Penggunaan

#### Dalam Filament Pages
```php
public function form(Form $form): Form
{
    return $form
        ->schema([
            TextInput::make('nama_printer')
                ->label('Nama Printer')
                ->required()
                ->maxLength(100),
                
            Select::make('jenis_koneksi')
                ->label('Jenis Koneksi')
                ->options([
                    'windows' => 'Windows Printer',
                    'network' => 'Network Printer',
                    'file' => 'File Output',
                ])
                ->required()
                ->live()
                ->afterStateUpdated(function (Select $component, $state, Set $set) {
                    if ($state === 'network') {
                        $set('alamat_printer', '192.168.1.100:9100');
                    } elseif ($state === 'windows') {
                        $set('alamat_printer', 'Printer Name');
                    } elseif ($state === 'file') {
                        $set('alamat_printer', '/tmp/output.txt');
                    }
                }),
                
            TextInput::make('alamat_printer')
                ->label(function (Get $get) {
                    $jenisKoneksi = $get('jenis_koneksi');
                    if ($jenisKoneksi === 'network') {
                        return 'IP Address:Port';
                    } elseif ($jenisKoneksi === 'windows') {
                        return 'Nama Printer';
                    } elseif ($jenisKoneksi === 'file') {
                        return 'Path File Output';
                    }
                    return 'Alamat Printer';
                })
                ->required()
                ->maxLength(255),
                
            Select::make('struk_layout')
                ->label('Layout Struk')
                ->options([
                    '58mm' => 'Thermal 58mm (32-35 karakter per baris)',
                    '80mm' => 'Thermal 80mm (48 karakter per baris)',
                ])
                ->default('58mm')
                ->required(),
                
            Toggle::make('aktif')
                ->label('Printer Aktif')
                ->default(true),
                
            Toggle::make('default')
                ->label('Printer Default')
                ->default(false),
                
            Textarea::make('keterangan')
                ->label('Keterangan')
                ->maxLength(500),
        ]);
}
```

#### Dalam LayananPrinter Service
```php
use Ridwans2\Kasir\Models\PrinterSetting;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\PrintConnectors\NetworkPrintConnector;
use Mike42\Escpos\PrintConnectors\FilePrintConnector;
use Mike42\Escpos\Printer;

class LayananPrinter
{
    protected $printer;
    protected $connector;
    
    // Mendapatkan instance printer default
    public function getPrinterDefault()
    {
        $printer = PrinterSetting::getDefault();
        
        if (!$printer) {
            throw new Exception('Tidak ada printer default yang dikonfigurasi');
        }
        
        return $printer;
    }
    
    // Menghubungkan ke printer
    public function hubungkanPrinter($printerSetting = null)
    {
        if (!$printerSetting) {
            $printerSetting = $this->getPrinterDefault();
        }
        
        switch ($printerSetting->jenis_koneksi) {
            case 'windows':
                $this->connector = new WindowsPrintConnector($printerSetting->alamat_printer);
                break;
                
            case 'network':
                $parts = explode(':', $printerSetting->alamat_printer);
                $ip = $parts[0];
                $port = isset($parts[1]) ? (int) $parts[1] : 9100;
                $this->connector = new NetworkPrintConnector($ip, $port);
                break;
                
            case 'file':
                $this->connector = new FilePrintConnector($printerSetting->alamat_printer);
                break;
                
            default:
                throw new Exception("Tipe koneksi '{$printerSetting->jenis_koneksi}' tidak didukung");
        }
        
        $this->printer = new Printer($this->connector);
        
        return $this->printer;
    }
    
    // Mencetak teks
    public function cetakTeks($teks, $printerSetting = null)
    {
        try {
            if (!$this->printer) {
                $this->hubungkanPrinter($printerSetting);
            }
            
            $this->printer->text($teks);
            $this->printer->feed(3);
            $this->printer->cut();
            $this->tutupKoneksi();
            
            return true;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    
    // Menutup koneksi printer
    public function tutupKoneksi()
    {
        if ($this->printer) {
            $this->printer->close();
        }
    }
}
```

---

## Model StrukTemplate

### Deskripsi
Model ini merepresentasikan template struk.

### Tabel Database
```
struk_templates
```

### Field Utama
- `id` - ID unik template
- `nama` - Nama template
- `jenis` - Jenis printer (58mm, 80mm)
- `konten` - Konfigurasi template dalam bentuk JSON
- `tampilkan_logo` - Flag tampilkan logo
- `tampilkan_pajak` - Flag tampilkan pajak
- `tampilkan_diskon` - Flag tampilkan diskon
- `default` - Status default (true/false)
- `aktif` - Status aktif (true/false)

### Casts
```php
protected $casts = [
    'konten' => 'array',
    'tampilkan_logo' => 'boolean',
    'tampilkan_pajak' => 'boolean',
    'tampilkan_diskon' => 'boolean',
    'default' => 'boolean',
    'aktif' => 'boolean',
];
```

### Method Penting
```php
// Mengatur template ini sebagai default
public function setAsDefault(): bool
{
    // Hapus status default dari semua template
    self::where('default', true)->update(['default' => false]);
    
    // Set template ini sebagai default
    return $this->update(['default' => true]);
}

// Mendapatkan template default
public static function getDefault(): ?self
{
    return self::where('default', true)->first();
}

// Mendapatkan template aktif
public static function getActive()
{
    return self::where('aktif', true)->get();
}

// Mendapatkan deskripsi jenis template
public function getJenisTextAttribute(): string
{
    return match($this->jenis) {
        '58mm' => 'Thermal 58mm (32-35 karakter per baris)',
        '80mm' => 'Thermal 80mm (48 karakter per baris)',
        default => 'Tidak Diketahui',
    };
}

// Mendapatkan lebar kertas dalam karakter
public function getLebarKarakterAttribute(): int
{
    return match($this->jenis) {
        '58mm' => 32,
        '80mm' => 48,
        default => 32,
    };
}
```

### Contoh Penggunaan

#### Dalam Filament Page
```php
public function form(Form $form): Form
{
    return $form
        ->schema([
            TextInput::make('nama')
                ->label('Nama Template')
                ->required()
                ->maxLength(100),
                
            Select::make('jenis')
                ->label('Jenis Printer')
                ->options([
                    '58mm' => 'Thermal 58mm (32-35 karakter per baris)',
                    '80mm' => 'Thermal 80mm (48 karakter per baris)',
                ])
                ->default('58mm')
                ->required()
                ->live(),
                
            Repeater::make('konten')
                ->label('Konten Template')
                ->schema([
                    Select::make('type')
                        ->label('Tipe Blok')
                        ->options([
                            'teks' => 'Teks',
                            'garis' => 'Garis',
                            'spasi' => 'Spasi',
                            'judul' => 'Judul',
                            'data_transaksi' => 'Data Transaksi',
                            'item_layout' => 'Layout Item',
                            'info_total' => 'Informasi Total',
                            'info_pembayaran' => 'Informasi Pembayaran',
                            'footer' => 'Footer',
                        ])
                        ->required()
                        ->live()
                        ->afterStateUpdated(fn($state, Set $set) => $set('data', [])),
                        
                    Placeholder::make('preview')
                        ->label('Preview')
                        ->content(function (Get $get) {
                            $type = $get('type');
                            $data = $get('data');
                            
                            return view('kasir::components.struk-preview-item', [
                                'type' => $type,
                                'data' => $data,
                                'jenis' => $this->jenis,
                            ]);
                        }),
                        
                    KeyValue::make('data')
                        ->label('Data Blok')
                        ->keyLabel('Properti')
                        ->valueLabel('Nilai')
                        ->default([])
                        ->schema(function (Get $get) {
                            $type = $get('type');
                            
                            switch ($type) {
                                case 'teks':
                                    return [
                                        TextInput::make('isi')
                                            ->label('Teks')
                                            ->required(),
                                        Select::make('posisi')
                                            ->label('Posisi')
                                            ->options([
                                                'kiri' => 'Kiri',
                                                'tengah' => 'Tengah',
                                                'kanan' => 'Kanan',
                                            ])
                                            ->default('kiri')
                                            ->required(),
                                        Toggle::make('tebal')
                                            ->label('Teks Tebal')
                                            ->default(false),
                                    ];
                                    
                                case 'garis':
                                    return [
                                        Select::make('tipe')
                                            ->label('Tipe Garis')
                                            ->options([
                                                '-' => 'Dash (-)',
                                                '=' => 'Equal (=)',
                                                '*' => 'Asterisk (*)',
                                                '.' => 'Dot (.)',
                                            ])
                                            ->default('-')
                                            ->required(),
                                    ];
                                    
                                case 'spasi':
                                    return [
                                        TextInput::make('jumlah')
                                            ->label('Jumlah Baris')
                                            ->numeric()
                                            ->default(1)
                                            ->required(),
                                    ];
                                    
                                case 'judul':
                                    return [
                                        TextInput::make('isi')
                                            ->label('Teks Judul')
                                            ->required(),
                                        Select::make('ukuran')
                                            ->label('Ukuran')
                                            ->options([
                                                'normal' => 'Normal',
                                                'besar' => 'Besar',
                                                'tinggi' => 'Tinggi',
                                                'besartinggi' => 'Besar & Tinggi',
                                            ])
                                            ->default('besar')
                                            ->required(),
                                    ];
                                    
                                // dan seterusnya untuk tipe blok lainnya
                                
                                default:
                                    return [];
                            }
                        }),
                ])
                ->collapsed()
                ->itemLabel(function (array $state) {
                    $type = $state['type'] ?? '';
                    $data = $state['data'] ?? [];
                    
                    $labels = [
                        'teks' => 'Teks: ' . ($data['isi'] ?? ''),
                        'garis' => 'Garis: ' . ($data['tipe'] ?? '-'),
                        'spasi' => 'Spasi: ' . ($data['jumlah'] ?? 1) . ' baris',
                        'judul' => 'Judul: ' . ($data['isi'] ?? ''),
                        'data_transaksi' => 'Data Transaksi: ' . ($data['tipe'] ?? ''),
                        'item_layout' => 'Layout Item: ' . ($data['format'] ?? 'standard'),
                        'info_total' => 'Informasi Total: ' . ($data['format'] ?? 'standard'),
                        'info_pembayaran' => 'Informasi Pembayaran: ' . ($data['format'] ?? 'standard'),
                        'footer' => 'Footer: ' . Str::limit($data['pesan'] ?? '', 20),
                    ];
                    
                    return $labels[$type] ?? $type;
                })
                ->reorderable()
                ->defaultItems(5)
                ->addActionLabel('Tambah Blok'),
                
            Toggle::make('tampilkan_logo')
                ->label('Tampilkan Logo')
                ->default(true),
                
            Toggle::make('tampilkan_pajak')
                ->label('Tampilkan Pajak')
                ->default(true),
                
            Toggle::make('tampilkan_diskon')
                ->label('Tampilkan Diskon')
                ->default(true),
                
            Toggle::make('default')
                ->label('Template Default')
                ->default(false),
                
            Toggle::make('aktif')
                ->label('Template Aktif')
                ->default(true),
        ]);
}
```

#### Dalam CetakController
```php
use Ridwans2\Kasir\Models\StrukTemplate;
use Ridwans2\Kasir\Models\Penjualan;
use Ridwans2\Kasir\Services\LayananPrinter;

class CetakController extends Controller
{
    // Cetak struk
    public function cetakStruk($penjualanId)
    {
        $penjualan = Penjualan::with(['transaksi', 'pembayaran.metodePembayaran'])
            ->findOrFail($penjualanId);
            
        // Ambil template default
        $template = StrukTemplate::getDefault();
        
        if (!$template) {
            return response()->json(['success' => false, 'message' => 'Template struk default tidak ditemukan']);
        }
        
        // Siapkan data
        $data = [
            'noTransaksi' => $penjualan->penjualan_invoice,
            'tanggal' => $penjualan->created_at->format('d/m/Y H:i'),
            'kasir' => $penjualan->user->name ?? 'Admin',
            'items' => $penjualan->transaksi->map(function ($item) {
                return [
                    'nama' => $item->nama_item,
                    'harga' => $item->harga,
                    'qty' => $item->jumlah,
                    'total' => $item->harga * $item->jumlah,
                ];
            }),
            'subtotal' => $penjualan->transaksi->sum(function ($item) {
                return $item->harga * $item->jumlah;
            }),
            'diskon' => $penjualan->diskon,
            'pajak' => $penjualan->pajak,
            'servis' => $penjualan->servis,
            'totalBayar' => $penjualan->jumlah_pembayaran,
            'metodePembayaran' => $penjualan->pembayaran->first()->metodePembayaran->nama ?? 'Tunai',
            'jumlahBayar' => $penjualan->pembayaran->sum('jumlah'),
            'kembalian' => $penjualan->pembayaran->sum('jumlah') - $penjualan->jumlah_pembayaran,
        ];
        
        // Cetak struk
        $printerService = new LayananPrinter();
        
        try {
            // Hubungkan ke printer
            $printerService->hubungkanPrinter();
            
            // Cetak struk dengan template
            $hasil = $printerService->cetakStrukKustom($data, $template->id);
            
            if ($hasil === true) {
                return response()->json(['success' => true, 'message' => 'Struk berhasil dicetak']);
            } else {
                return response()->json(['success' => false, 'message' => 'Gagal mencetak struk: ' . $hasil]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
        }
    }
}
```

## Integrasi Antar Model

Bagian ini menjelaskan bagaimana model-model dalam plugin Kasir saling berintegrasi untuk membentuk sistem yang komprehensif.

### Alur Transaksi Penjualan

1. **Pembuatan Keranjang Belanja**
   - Menggunakan `Service\Keranjang` untuk mengumpulkan item yang dibeli
   - Setiap item berasal dari model `Produk`
   - Jika produk memiliki varian, maka akan menggunakan produk anak (yang memiliki nilai `sub` sesuai dengan ID produk induk)

2. **Proses Pembayaran**
   - Menggunakan model `MetodePembayaran` untuk menyimpan metode pembayaran yang dipilih
   - Membuat record baru di model `Penjualan` untuk menyimpan data transaksi utama
   - Membuat record-record baru di model `Transaksi` untuk menyimpan item-item yang dibeli
   - Membuat record baru di model `Pembayaran` untuk menyimpan detail pembayaran

3. **Pencetakan Struk**
   - Menggunakan model `PrinterSetting` untuk konfigurasi printer
   - Menggunakan model `StrukTemplate` untuk template struk yang akan dicetak
   - Menggunakan `Service\LayananPrinter` untuk mencetak struk ke printer yang dipilih

4. **Pengelolaan Shift**
   - Menggunakan model `PenjualanShift` untuk mencatat shift penjualan
   - Setiap `Penjualan` terhubung dengan `PenjualanShift` yang aktif saat transaksi dibuat

5. **Refund (Pengembalian)**
   - Membuat record baru di model `Refund` untuk mencatat pengembalian
   - Membuat record-record baru di model `RefundTransaksi` untuk mencatat item yang dikembalikan
   - Terhubung dengan `Penjualan` asli yang akan di-refund

### Diagram Relasi Model

```
Penjualan ────┬──> Transaksi ───> Produk <───── ProdukKategori
              │                     │
              │                     └───> HargaJual
              │
              ├──> Pembayaran ───> MetodePembayaran
              │
              ├──> Tamu
              │
              └──> PenjualanShift

Refund ───────┬──> RefundTransaksi ───> Produk
              │
              └──> Penjualan
```

## Panduan Penggunaan Lanjutan

### Mengelola Model Produk dengan Varian

Produk varian adalah fitur yang memungkinkan suatu produk memiliki beberapa varian dengan harga dan detail berbeda. Contohnya seperti ukuran, warna, atau spesifikasi lainnya.

#### Membuat Produk dengan Varian

```php
// Buat produk induk
$produkInduk = Produk::create([
    'nama' => 'Baju Kemeja Premium',
    'kategori_id' => 1, // ID Kategori Pakaian
    'jenis' => 'PRODUK',
    'harga_modal' => 80000,
    'harga' => 120000,
    'inivarian' => true, // Tanda bahwa produk ini memiliki varian
    'tampil' => true,
]);

// Buat varian-varian produk
$varianS = Produk::create([
    'nama' => 'Ukuran S',
    'kategori_id' => 1,
    'jenis' => 'PRODUK',
    'sub' => $produkInduk->id, // ID dari produk induk
    'harga_modal' => 80000,
    'harga' => 120000,
    'stok' => 10,
    'tampil' => true,
]);

$varianM = Produk::create([
    'nama' => 'Ukuran M',
    'kategori_id' => 1,
    'jenis' => 'PRODUK',
    'sub' => $produkInduk->id, // ID dari produk induk
    'harga_modal' => 85000,
    'harga' => 125000,
    'stok' => 8,
    'tampil' => true,
]);

$varianL = Produk::create([
    'nama' => 'Ukuran L',
    'kategori_id' => 1,
    'jenis' => 'PRODUK',
    'sub' => $produkInduk->id, // ID dari produk induk
    'harga_modal' => 90000,
    'harga' => 130000,
    'stok' => 5,
    'tampil' => true,
]);
```

#### Mendapatkan Semua Varian dari Produk Induk

```php
$produkInduk = Produk::find(1);

if ($produkInduk->inivarian) {
    $varians = $produkInduk->varian; // Mengambil semua varian

    foreach ($varians as $varian) {
        echo "Varian: {$varian->nama}, Harga: Rp " . number_format($varian->harga, 0, ',', '.') . ", Stok: {$varian->stok}\n";
    }
}
```

### Harga Multi-Level dan Promo dengan Model HargaJual

HargaJual memungkinkan produk memiliki harga yang berbeda berdasarkan jumlah pembelian atau periode waktu tertentu.

#### Membuat Harga Bertingkat (Grosir)

```php
// Produk dasar
$produk = Produk::find(1);

// Harga normal (retail)
HargaJual::create([
    'produk_id' => $produk->id,
    'nama' => 'Harga Retail',
    'harga' => 10000,
    'minimal' => 1,
    'maksimal' => 9,
]);

// Harga grosir kecil
HargaJual::create([
    'produk_id' => $produk->id,
    'nama' => 'Harga Grosir Kecil',
    'harga' => 9000,
    'minimal' => 10,
    'maksimal' => 49,
]);

// Harga grosir besar
HargaJual::create([
    'produk_id' => $produk->id,
    'nama' => 'Harga Grosir Besar',
    'harga' => 8500,
    'minimal' => 50,
    'maksimal' => null, // Tidak ada batas maksimal
]);
```

#### Membuat Harga Promo

```php
// Harga promo terbatas waktu
HargaJual::create([
    'produk_id' => $produk->id,
    'nama' => 'Promo Akhir Tahun',
    'harga' => 8000,
    'minimal' => 1, // Berlaku untuk pembelian apapun
    'tgl_mulai' => '2023-12-20',
    'tgl_selesai' => '2023-12-31',
]);
```

#### Mendapatkan Harga yang Tepat untuk Pembelian

```php
// Misalkan pelanggan membeli 15 unit
$jumlahBeli = 15;
$tanggalBeli = now();

// Cara mendapatkan harga yang tepat
$hargaTepat = HargaJual::where('produk_id', $produk->id)
    ->where(function ($query) use ($jumlahBeli) {
        $query->where(function ($q) use ($jumlahBeli) {
            $q->where('minimal', '<=', $jumlahBeli)
              ->whereNull('maksimal');
        })->orWhere(function ($q) use ($jumlahBeli) {
            $q->where('minimal', '<=', $jumlahBeli)
              ->where('maksimal', '>=', $jumlahBeli);
        });
    })
    ->where(function ($query) use ($tanggalBeli) {
        $query->where(function ($q) {
            $q->whereNull('tgl_mulai')
              ->whereNull('tgl_selesai');
        })->orWhere(function ($q) use ($tanggalBeli) {
            $q->whereNull('tgl_selesai')
              ->where('tgl_mulai', '<=', $tanggalBeli);
        })->orWhere(function ($q) use ($tanggalBeli) {
            $q->whereNull('tgl_mulai')
              ->where('tgl_selesai', '>=', $tanggalBeli);
        })->orWhere(function ($q) use ($tanggalBeli) {
            $q->where('tgl_mulai', '<=', $tanggalBeli)
              ->where('tgl_selesai', '>=', $tanggalBeli);
        });
    })
    ->orderBy('harga')
    ->first();

if ($hargaTepat) {
    echo "Harga yang berlaku untuk pembelian {$jumlahBeli} unit: Rp " . number_format($hargaTepat->harga, 0, ',', '.') . " ({$hargaTepat->nama})";
} else {
    echo "Tidak ada harga khusus, menggunakan harga default: Rp " . number_format($produk->harga, 0, ',', '.');
}

// Lebih mudah menggunakan method di model Produk
$hargaOtomatis = $produk->getFinalPrice($jumlahBeli);
echo "Harga otomatis untuk {$jumlahBeli} unit: Rp " . number_format($hargaOtomatis, 0, ',', '.');
```

### Menggunakan Service Keranjang

Service Keranjang memungkinkan pengelolaan item belanja dengan lebih mudah. Service ini menyimpan data keranjang dalam session.

#### Menambahkan Item ke Keranjang

```php
// Di controller atau livewire component
public function tambahKeKeranjang($produkId, $jumlah = 1)
{
    try {
        $produk = Produk::findOrFail($produkId);
        
        // Cek stok
        if ($produk->stok < $jumlah) {
            throw new \Exception("Stok tidak mencukupi. Stok tersedia: {$produk->stok}");
        }
        
        // Buat array item
        $item = [
            'id' => $produk->id,
            'jenis' => 'barang',
            'produk_id' => $produk->id,
            'nama_item' => $produk->nama,
            'harga_modal' => $produk->harga_modal,
            'harga' => $produk->getFinalPrice($jumlah), // Dapatkan harga yang sesuai
            'jumlah' => $jumlah,
            'ket' => $produk->ket ?? '-',
            'barcode' => $produk->barcode ?? '',
        ];
        
        // Tambahkan ke keranjang menggunakan service
        $keranjang = app(Keranjang::class);
        $keranjang->tambahItem($item);
        
        // Tampilkan notifikasi
        $this->dispatch('notif', [
            'type' => 'success',
            'message' => "Berhasil menambahkan {$produk->nama} ke keranjang"
        ]);
        
        // Muat ulang keranjang
        $this->dispatch('keranjang-updated');
        
    } catch (\Exception $e) {
        $this->dispatch('notif', [
            'type' => 'error',
            'message' => $e->getMessage()
        ]);
    }
}
```

#### Mengelola Keranjang dalam Livewire Component

```php
use Ridwans2\Kasir\Services\Keranjang;
use Livewire\Component;

class KeranjangBelanja extends Component
{
    public $items = [];
    public $totalHarga = 0;
    public $totalAkhir = 0;
    public $komponenBiaya = [];
    public $persenValues = [];
    
    protected $listeners = [
        'keranjang-updated' => 'refreshKeranjang',
    ];
    
    public function mount(Keranjang $keranjang)
    {
        $this->keranjang = $keranjang;
        $this->refreshKeranjang();
    }
    
    public function refreshKeranjang()
    {
        $this->items = $this->keranjang->items();
        $this->totalHarga = $this->keranjang->totalHarga();
        
        // Ambil biaya tambahan dari session
        $biayaTambahan = session('biaya_tambahan', [
            'pajak' => 0,
            'servis' => 0,
            'diskon' => 0,
        ]);
        
        $this->persenValues = $biayaTambahan;
        
        // Hitung total akhir
        $this->totalAkhir = $this->keranjang->hitungTotalAkhir(
            $biayaTambahan['diskon'],
            0, // diskon nominal
            $biayaTambahan['servis'],
            $biayaTambahan['pajak']
        );
        
        // Hitung komponen biaya
        $this->komponenBiaya = [
            'diskonNominal' => round(($biayaTambahan['diskon'] / 100) * $this->totalHarga),
            'servisNominal' => round(($biayaTambahan['servis'] / 100) * $this->totalHarga),
            'pajakNominal' => round(($biayaTambahan['pajak'] / 100) * $this->totalHarga),
        ];
    }
    
    public function tambahJumlah($id)
    {
        try {
            $this->keranjang->tambahJumlah($id, 1);
            $this->refreshKeranjang();
        } catch (\Exception $e) {
            $this->dispatch('notif', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    public function kurangiJumlah($id)
    {
        try {
            $this->keranjang->kurangiJumlah($id, 1);
            $this->refreshKeranjang();
        } catch (\Exception $e) {
            $this->dispatch('notif', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    public function hapusItem($id)
    {
        try {
            $this->keranjang->hapusItem($id);
            $this->refreshKeranjang();
        } catch (\Exception $e) {
            $this->dispatch('notif', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    public function kosongkanKeranjang()
    {
        try {
            $this->keranjang->kosongkanKeranjang();
            $this->refreshKeranjang();
        } catch (\Exception $e) {
            $this->dispatch('notif', [
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    public function render()
    {
        return view('kasir::livewire.keranjang-belanja');
    }
}
```

### Menggunakan Service PendingTransaksiService

Service ini memungkinkan penyimpanan transaksi sementara yang belum selesai diproses (pending).

#### Menyimpan Transaksi Pending

```php
use Ridwans2\Kasir\Services\PendingTransaksiService;
use Ridwans2\Kasir\Services\Keranjang;

// Di controller atau livewire component
public function simpanDraft()
{
    // Validasi
    if ($this->keranjang->items()->isEmpty()) {
        $this->dispatch('notif', [
            'type' => 'error',
            'message' => 'Keranjang kosong, tidak dapat menyimpan draft'
        ]);
        return;
    }
    
    // Ambil data form
    $data = $this->form->getState();
    
    // Simpan ke service PendingTransaksi
    $pendingService = app(PendingTransaksiService::class);
    $draftId = $pendingService->tambahPendingTransaksi($data, $this->keranjang);
    
    // Kosongkan keranjang setelah disimpan sebagai draft
    $this->keranjang->kosongkanKeranjang();
    
    // Reset form
    $this->form->fill();
    
    // Notifikasi
    $this->dispatch('notif', [
        'type' => 'success',
        'message' => 'Transaksi berhasil disimpan sebagai draft'
    ]);
    
    // Refresh halaman
    $this->dispatch('refresh-halaman');
}
```

#### Memuat Transaksi Pending

```php
public function muatTransaksi($id)
{
    try {
        // Cek apakah keranjang saat ini kosong
        if ($this->keranjang->items()->isNotEmpty()) {
            // Jika keranjang tidak kosong, tampilkan konfirmasi
            $this->dispatch('konfirmasi-muat-transaksi', [
                'id' => $id
            ]);
        } else {
            // Jika keranjang kosong, langsung muat transaksi
            $this->muatTransaksiKonfirmasi($id);
        }
    } catch (\Exception $e) {
        $this->dispatch('notif', [
            'type' => 'error',
            'message' => 'Gagal memuat transaksi: ' . $e->getMessage()
        ]);
    }
}

public function muatTransaksiKonfirmasi($id)
{
    try {
        $pendingService = app(PendingTransaksiService::class);
        $transaksi = $pendingService->getPendingTransaksi($id);
        
        if (!$transaksi) {
            throw new \Exception('Transaksi tidak ditemukan');
        }
        
        // Kosongkan keranjang saat ini
        $this->keranjang->kosongkanKeranjang();
        
        // Muat data keranjang dari draft
        $this->keranjang->importData($transaksi['keranjang_data']);
        
        // Muat biaya tambahan
        session()->put('biaya_tambahan', $transaksi['biaya_tambahan']);
        
        // Set form data untuk digunakan di komponen Pembayaran
        session()->put('form_data_draft', $transaksi['form_data']);
        
        // Hapus transaksi pending setelah dimuat (opsional)
        if (config('kasir.transaksi_pending.hapus_setelah_dimuat', true)) {
            $pendingService->hapusPendingTransaksi($id);
        }
        
        // Refresh komponen terkait
        $this->dispatch('keranjang-updated');
        $this->dispatch('pembayaran-updated');
        $this->dispatch('muat-form-data-draft');
        
        // Notifikasi
        $this->dispatch('notif', [
            'type' => 'success',
            'message' => 'Transaksi berhasil dimuat'
        ]);
    } catch (\Exception $e) {
        $this->dispatch('notif', [
            'type' => 'error',
            'message' => 'Gagal memuat transaksi: ' . $e->getMessage()
        ]);
    }
}
```

### Menggunakan Service LayananPrinter

Service ini digunakan untuk berkomunikasi dengan printer dan mencetak struk atau laporan.

#### Mencetak Struk Transaksi

```php
use Ridwans2\Kasir\Services\LayananPrinter;
use Ridwans2\Kasir\Models\Penjualan;
use Ridwans2\Kasir\Models\StrukTemplate;

class CetakController extends Controller
{
    public function cetakStruk($penjualanId)
    {
        try {
            // Ambil data penjualan
            $penjualan = Penjualan::with(['transaksi', 'pembayaran.metodePembayaran', 'user'])
                ->findOrFail($penjualanId);
                
            // Ambil template struk
            $template = StrukTemplate::getDefault();
            
            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template struk default tidak ditemukan'
                ]);
            }
            
            // Siapkan data untuk struk
            $data = [
                'namaUsaha' => config('app.name', 'Toko Anda'),
                'alamat' => config('kasir.toko.alamat', 'Alamat Toko'),
                'telepon' => config('kasir.toko.telepon', '-'),
                'noTransaksi' => $penjualan->penjualan_invoice,
                'tanggal' => $penjualan->created_at->format('d/m/Y H:i'),
                'kasir' => $penjualan->user->name ?? 'Admin',
                'namaTamu' => $penjualan->nama_tamu,
                'namaMeja' => $penjualan->nama,
                
                // Item transaksi
                'items' => $penjualan->transaksi->map(function ($item) {
                    return [
                        'nama' => $item->nama_item,
                        'harga' => $item->harga,
                        'qty' => $item->jumlah,
                        'total' => $item->harga * $item->jumlah,
                    ];
                })->toArray(),
                
                // Perhitungan
                'subtotal' => $penjualan->transaksi->sum(function ($item) {
                    return $item->harga * $item->jumlah;
                }),
                'diskon' => $penjualan->diskon,
                'pajak' => $penjualan->pajak,
                'servis' => $penjualan->servis,
                'totalBayar' => $penjualan->jumlah_pembayaran,
                
                // Informasi pembayaran
                'metodePembayaran' => $penjualan->pembayaran->first()->metodePembayaran->nama ?? 'Tunai',
                'jumlahBayar' => $penjualan->pembayaran->sum('jumlah'),
                'kembalian' => $penjualan->pembayaran->sum('jumlah') - $penjualan->jumlah_pembayaran,
            ];
            
            // Inisialisasi service printer
            $printerService = app(LayananPrinter::class);
            
            // Cetak struk
            $hasil = $printerService->cetakStrukKustom($data, $template->id);
            
            if ($hasil === true) {
                return response()->json([
                    'success' => true,
                    'message' => 'Struk berhasil dicetak'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mencetak struk: ' . $hasil
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }
}
```

## Panduan Debugging dan Pemecahan Masalah

### Mengecek Relasi Model

Jika Anda mengalami masalah dengan relasi model, Anda dapat melakukan pengecekan dengan cara berikut:

```php
// Cek relasi one-to-many
$penjualan = Penjualan::find(1);
dd($penjualan->transaksi->count()); // Harus mengembalikan jumlah transaksi

// Cek relasi belongs-to
$transaksi = Transaksi::find(1);
dd($transaksi->penjualan); // Harus mengembalikan objek Penjualan

// Cek relasi dengan eager loading
$penjualan = Penjualan::with(['transaksi', 'pembayaran.metodePembayaran'])->find(1);
dd($penjualan->toArray()); // Lihat struktur data lengkap
```

### Mengatasi Masalah N+1 Query

Problem N+1 query terjadi ketika Anda memuat data utama dan kemudian melakukan query terpisah untuk setiap relasi. Ini bisa memperlambat aplikasi secara signifikan.

```php
// Contoh problem N+1 query
$penjualans = Penjualan::all(); // 1 query
foreach ($penjualans as $penjualan) {
    $penjualan->transaksi; // N query tambahan
}

// Solusi: Gunakan eager loading
$penjualans = Penjualan::with('transaksi')->get(); // Hanya 2 query
foreach ($penjualans as $penjualan) {
    $penjualan->transaksi; // Tidak ada query tambahan
}
```

## Kesimpulan

Model-model dalam plugin Kasir dirancang untuk memberikan fleksibilitas dan kemudahan dalam pengembangan aplikasi point of sale. Dengan memahami hubungan antar model dan cara menggunakannya, Anda dapat memanfaatkan semua fitur yang disediakan oleh plugin ini.

Selalu perhatikan performa aplikasi dengan menggunakan eager loading dan query optimasi lainnya, terutama jika aplikasi digunakan untuk transaksi dengan volume tinggi.

Jika Anda ingin mengembangkan fitur tambahan, pastikan untuk menguji secara menyeluruh dan mempertimbangkan integrasi dengan model yang sudah ada untuk konsistensi data.

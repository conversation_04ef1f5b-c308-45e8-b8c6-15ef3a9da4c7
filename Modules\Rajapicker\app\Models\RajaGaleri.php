<?php

namespace Modules\Rajapicker\Models;

use <PERSON><PERSON><PERSON>\RajaJson\Models\Rajajson;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Image\Enums\Fit;

class <PERSON><PERSON><PERSON><PERSON> extends <PERSON><PERSON>son implements HasMedia
{
    use InteractsWithMedia;

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('default')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])
            ->singleFile();

        $this->addMediaCollection('gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('avatars')
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->singleFile();

        $this->addMediaCollection('banners')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('products')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('cms')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        // Thumbnail conversion untuk semua gambar
        $this->addMediaConversion('thumb')
            ->fit(Fit::Crop, 300, 300)
            ->quality(80)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms')
            ->nonQueued();

        // Preview conversion untuk gambar
      //   $this->addMediaConversion('preview')
      //       ->fit(Fit::Contain, 800, 600)
      //       ->quality(85)
      //       ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms')
      //       ->nonQueued();

        // Avatar conversion khusus untuk avatar
      //   $this->addMediaConversion('avatar')
      //       ->fit(Fit::Crop, 150, 150)
      //       ->quality(90)
      //       ->performOnCollections('avatars')
      //       ->nonQueued();

        // Banner conversion untuk banner
      //   $this->addMediaConversion('banner')
      //       ->fit(Fit::Contain, 1200, 400)
      //       ->quality(85)
      //       ->performOnCollections('banners')
      //       ->nonQueued();
    }

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'GALERI');
        });

        static::creating(function ($model) {
            $model->jenis = 'GALERI';
        });

        static::updating(function ($model) {
            $model->jenis = 'GALERI';
        });
    }
}

<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\Pembayaran as ModelsPembayaran;
use App\Models\Penjualan;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

/**
 * @mixin IdeHelperPembayaran
 */
class Pembayaran extends ModelsPembayaran
{
    use HasFactory;

    // protected $table = 'pembayaran';

    // protected $fillable = [
    //     'nama',
    //     'penjualan_id',
    //     'reservasi_id',
    //     'metode_pembayaran_id',
    //     'jumlah',
    //     'bukti',
    //     'status',
    //     'pengirim',
    //     'tujuan',
    //     'ket',
    //     'karyawan_id',
    // ];

   protected $casts = [
        'pengirim' => 'array', // Menggunakan array agar Laravel otomatis encode/decode
    ];

      protected static function boot()
    {
        parent::boot();

 
        static::creating(function ($model) {
            $model->karyawan_id = Auth::id();
        });
        
        static::updating(function ($model) {
            $model->karyawan_id = Auth::id();
        });
    }


    public function penjualan(): BelongsTo
    {
        return $this->belongsTo(Penjualan::class);
    }

    public function metodePembayaran(): BelongsTo
    {
        return $this->belongsTo(MetodePembayaran::class);
    }

    public function reservasi(): BelongsTo
    {
        return $this->belongsTo(Reservasi::class);
    }
 
 
}
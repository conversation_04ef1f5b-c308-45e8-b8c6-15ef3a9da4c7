<?php

namespace App\Aplikasi\Hotel\Resources\PembayaranResource\Pages;

use App\Aplikasi\Hotel\Resources\PembayaranResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManagePembayarans extends ManageRecords
{
    protected static string $resource = PembayaranResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

# Panduan Penggunaan Modul RajaMenu

Modul RajaMenu adalah solusi untuk mengelola navigasi flyout di panel FilamentPHP, dengan menu dropdown yang tampil di samping saat mouse over (hover).

## 📋 Daftar Isi

1. [Instalasi](#-instalasi)
2. [<PERSON>an](#-cara-penggunaan)
3. [Mode Navigasi](#-mode-navigasi)
4. [Plugin RajaMenu](#-plugin-rajamenu)
5. [Auto-Render](#-auto-render)
6. [Contoh Implementasi](#-contoh-implementasi)
7. [Troubleshooting](#-troubleshooting)

## 🚀 Instalasi

### 1. Aktifkan Modul
```bash
php artisan module:enable RajaMenu
```

### 2. Publish Assets
```bash
php artisan vendor:publish --tag=rajamenu-flyout-assets
```

### 3. Integrasikan dengan Panel Provider
```php
use Modules\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        $panel = $panel
            ->default()
            ->id('admin')
            ->topNavigation(); // Pastikan top navigation aktif

        // Terapkan flyout navigation
        $panel = $this->applyFlyoutNavigation($panel);

        return $panel;
    }
}
```

## 💡 Cara Penggunaan

### Menggunakan Trait HasFlyoutNavigation

```php
use Modules\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        // Terapkan flyout navigation dengan satu baris
        $panel = $this->applyFlyoutNavigation($panel);
        
        return $panel;
    }
}
```

### Menggunakan Plugin (Direkomendasikan)

```php
use Modules\RajaMenu\Plugins\RajaMenuPlugin;

public function panel(Panel $panel): Panel
{
    return $panel
        ->default()
        ->id('admin')
        ->topNavigation()
        ->plugins([
            RajaMenuPlugin::make()
                ->enabled(true)
                ->auto()
                ->panel('admin'),
        ]);
}
```

## 🔍 Mode Navigasi

### 1. Mode Auto (Auto-Discovery)

Mode yang otomatis mengambil navigasi dari FilamentPHP resources dan pages.

**Konfigurasi Resource atau Page:**
```php
class MyCmsResource extends Resource
{
    protected static ?string $navigationGroup = 'Cms';
    protected static ?string $navigationParentItem = 'Content';
    protected static ?string $navigationLabel = 'Konten';
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?int $navigationSort = 1;
    
    // Untuk badge
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
```

### 2. Mode Manual

Kontrol penuh dengan konfigurasi manual di file konfigurasi.

```php
// config/config.php
'navigation_mode' => 'manual',
'manual_navigation' => [
    'groups' => [
        'Dashboard' => [
            'label' => 'Dashboard',
            'icon' => 'heroicon-o-home',
            'sort' => -10,
            'items' => [
                [
                    'label' => 'Dashboard',
                    'icon' => 'heroicon-o-squares-2x2',
                    'url' => '/admin',
                    'sort' => 1,
                    'type' => 'page',
                ],
            ],
        ],
        'Content' => [
            'label' => 'Content',
            'icon' => 'heroicon-o-document-text',
            'sort' => -5,
            'items' => [
                // Parent item dengan children (flyout submenu)
                [
                    'label' => 'Manajemen Konten',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                    'sort' => 1,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Artikel',
                            'icon' => 'heroicon-o-newspaper',
                            'url' => '/admin/articles',
                            'sort' => 1,
                            'badge' => '25',
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Halaman',
                            'icon' => 'heroicon-o-document',
                            'url' => '/admin/pages',
                            'sort' => 2,
                            'type' => 'resource',
                        ],
                    ],
                ],
                // Item langsung tanpa children
                [
                    'label' => 'Media',
                    'icon' => 'heroicon-o-photo',
                    'url' => '/admin/media',
                    'sort' => 2,
                    'badge' => '156',
                    'type' => 'resource',
                ],
            ],
        ],
    ],
],
```

## 🔌 Plugin RajaMenu

Cara termudah menggunakan RajaMenu adalah dengan plugin:

```php
use Modules\RajaMenu\Plugins\RajaMenuPlugin;

// Di PanelProvider
->plugins([
    // Mode Auto (Auto-Discovery)
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),

    // Mode Manual dengan Helper Methods
    RajaMenuPlugin::make()
        ->enabled(true)
        ->manual()
        ->panel('marketplace')
        ->dashboard()
        ->contentManagement()
        ->userManagement()
        ->systemSettings(),
])
```

### Method Plugin yang Tersedia

- `enabled(true/false)` - Mengaktifkan/menonaktifkan plugin
- `auto()` - Menggunakan mode auto-discovery
- `manual()` - Menggunakan mode konfigurasi manual
- `panel('admin')` - Menentukan ID panel
- `autoRender(true/false)` - Mengaktifkan/menonaktifkan auto-render
- `dashboard()` - Menambahkan group dashboard (mode manual)
- `contentManagement()` - Menambahkan group manajemen konten (mode manual)
- `userManagement()` - Menambahkan group manajemen pengguna (mode manual)
- `systemSettings()` - Menambahkan group pengaturan sistem (mode manual)
- `addGroup(name, config)` - Menambahkan group kustom (mode manual)

## 🚀 Auto-Render

Fitur Auto-Render memungkinkan navigasi muncul otomatis tanpa perlu render hook manual.

### Auto-Render (Direkomendasikan)

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
    // autoRender(true) adalah default - navigasi muncul otomatis
])
```

### Manual Render

```php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin')
        ->manualRender(), // Nonaktifkan auto-render
])
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

## 📚 Contoh Implementasi

### Contoh 1: Mode Auto dengan Plugin

```php
use Modules\RajaMenu\Plugins\RajaMenuPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->topNavigation()
            ->plugins([
                RajaMenuPlugin::make()
                    ->enabled(true)
                    ->auto()
                    ->panel('admin'),
            ]);
    }
}
```

### Contoh 2: Mode Manual dengan Trait

```php
use Modules\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        $panel = $panel
            ->default()
            ->id('admin')
            ->topNavigation();

        // Apply manual flyout navigation
        $panel = $this->applyCustomFlyoutNavigation($panel, [
            'groups' => [
                'Dashboard' => [
                    'label' => 'Dashboard',
                    'icon' => 'heroicon-o-home',
                    'items' => [
                        [
                            'label' => 'Dashboard Utama',
                            'icon' => 'heroicon-o-squares-2x2',
                            'url' => '/admin',
                        ],
                    ],
                ],
                'Konten' => [
                    'label' => 'Konten',
                    'icon' => 'heroicon-o-document-text',
                    'items' => [
                        [
                            'label' => 'Manajemen Konten',
                            'icon' => 'heroicon-o-book-open',
                            'url' => '#',
                            'type' => 'parent',
                            'children' => [
                                [
                                    'label' => 'Artikel',
                                    'icon' => 'heroicon-o-newspaper',
                                    'url' => '/admin/articles',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        return $panel;
    }
}
```

### Contoh 3: Multiple Panels

```php
// AdminPanelProvider.php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->auto()
        ->panel('admin'),
])

// MarketplacePanelProvider.php
->plugins([
    RajaMenuPlugin::make()
        ->enabled(true)
        ->manual()
        ->panel('marketplace')
        ->dashboard()
        ->addGroup('Products', [
            'label' => 'Produk',
            'icon' => 'heroicon-o-shopping-bag',
            'items' => [
                // Item-item menu...
            ],
        ]),
])
```

## ❓ Troubleshooting

### Navigasi Tidak Muncul

1. Pastikan modul diaktifkan:
```bash
php artisan module:list
```

2. Pastikan assets dipublish:
```bash
php artisan vendor:publish --tag=rajamenu-flyout-assets
```

3. Pastikan top navigation aktif:
```php
->topNavigation()
```

4. Periksa panel ID:
```php
->panel('admin') // Harus sesuai dengan panel ID
```

5. Clear cache:
```bash
php artisan cache:clear
php artisan view:clear
```

### Navigasi Duplikat

Hapus render hook manual jika menggunakan auto-render:
```php
// HAPUS ini jika menggunakan auto-render
->renderHook(
    'panels::page.header.actions.before',
    fn() => view('rajamenu::navigation.auto-flyout')->render()
)
```

### CSS Tidak Load

Periksa file CSS publikasi:
```bash
ls public/modules/rajamenu/css/
```

Dan rebuild jika diperlukan:
```bash
npm run build
``` 
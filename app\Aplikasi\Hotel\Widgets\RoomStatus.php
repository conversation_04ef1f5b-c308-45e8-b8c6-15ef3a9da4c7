<?php

namespace App\Aplikasi\Hotel\Widgets;


use App\Aplikasi\Hotel\Models\KamarTipe;
use App\Aplikasi\Hotel\Models\Reservasi;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class RoomStatus extends ChartWidget
{
    protected static ?string $heading = 'Status Kamar Berdasarkan Tipe';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 1;

    protected function getData(): array
    {
        // Mendapatkan semua tipe kamar
        $tipeKamar = KamarTipe::with('kamar')->get();
        
        if ($tipeKamar->isEmpty()) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $labels = [];
        $tersediaData = [];
        $terpakaiaData = [];

        foreach ($tipeKamar as $tipe) {
            $labels[] = $tipe->nama;
            
            // Hitung kamar tersedia dan terpakai per tipe
            $totalPerTipe = $tipe->kamar->count();
            
            // Kamar terpakai adalah yang saat ini sedang digunakan (check-in tapi belum check-out)
            $terpakai = Reservasi::whereIn('kamar_id', $tipe->kamar->pluck('id'))
                ->where('check_in', '<=', Carbon::now())
                ->where('check_out', '>=', Carbon::now())
                ->where('status_reservasi', '!=', 'SCO')
                ->count();
            
            $tersedia = $totalPerTipe - $terpakai;
            
            $tersediaData[] = $tersedia;
            $terpakaiaData[] = $terpakai;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Kamar Tersedia',
                    'data' => $tersediaData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.7)',
                ],
                [
                    'label' => 'Kamar Terpakai',
                    'data' => $terpakaiaData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.7)',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected static ?array $options = [
        'plugins' => [
            'legend' => [
                'display' => true,
            ],
            'tooltip' => [
                'enabled' => true,
            ],
        ],
        'scales' => [
            'x' => [
                'stacked' => true,
            ],
            'y' => [
                'stacked' => true,
                'beginAtZero' => true,
            ],
        ],
    ];
}
<x-filament-widgets::widget>
    <x-filament::section>
        @php
            $data = $this->getData();
            $user = $data['user'];
            $info = $data['info'];
        @endphp

        @if($user)
            <!-- User Info Card -->
            <div class="  rounded-xl p-6 shadow-lg">
                <div class="flex items-start space-x-4">
                    <!-- Avatar -->
                    <div class="flex-shrink-0">
                        @if($info['avatar_url'])
                            <img class="h-20 w-20 rounded-full object-cover border-3 border-white/30 shadow-lg"
                                 src="{{ $info['avatar_url'] }}"
                                 alt="{{ $info['name'] }}">
                        @else
                            <div class="h-20 w-20 rounded-full   flex items-center justify-center border-3 border-white/30 shadow-lg">
                                <span class=" text-2xl font-bold drop-shadow-sm">
                                    {{ strtoupper(substr($info['name'], 0, 1)) }}
                                </span>
                            </div>
                        @endif
                    </div>

                    <!-- User Details -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between mb-4">
                            <div class="min-w-0 flex-1">
                                <h2 class="text-2xl font-bold  mb-1 truncate">
                                    {{ $info['name'] }}
                                </h2>
                                <p class="text-gray-300   mb-1 truncate">
                                    {{ $info['email'] }}
                                </p>
                                @if($info['username'] !== 'Belum diset')
                                    <p class="   text-sm">
                                        {{ $info['username'] }}
                                    </p>
                                @endif
                            </div>

                            <!-- Status Badges -->
                            <div class="flex flex-col space-y-2 ml-4">
                                <!-- Email Verification Badge -->
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ $info['email_verified'] === 'Terverifikasi' ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white' }}">
                                    {{ $info['email_verified'] }}
                                </span>

                                <!-- Role Badge -->
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                                    {{ $info['role'] }}
                                </span>
                            </div>
                        </div>

                        <!-- User Info Grid -->
                        <div class="grid grid-cols-2 gap-4 pt-4 border-t border-white/20">
                            <div>
                                <p class="text-gray-400 text-xs uppercase tracking-wide font-medium">
                                    Nama Lengkap
                                </p>
                                <p class=" text-sm font-semibold mt-1">
                                    {{ $info['name'] }}
                                </p>
                            </div>

                            <div>
                                <p class="text-gray-400 text-xs uppercase tracking-wide font-medium">
                                    Role/Peran
                                </p>
                                <p class="  text-sm font-semibold mt-1">
                                    {{ $info['role'] }}
                                </p>
                            </div>

                            <div>
                                <p class="text-gray-400 text-xs uppercase tracking-wide font-medium">
                                    Email
                                </p>
                                <p class="  text-sm font-semibold mt-1 truncate">
                                    {{ $info['email'] }}
                                </p>
                            </div>

                            <div>
                                <p class="text-gray-400 text-xs uppercase tracking-wide font-medium">
                                    Username
                                </p>
                                <p class="  text-sm font-semibold mt-1">
                                    {{ $info['username'] }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-12">
                <x-heroicon-o-user class="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <div class="text-gray-500 text-lg">
                    Silakan login untuk melihat informasi user
                </div>
            </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>

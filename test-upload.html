<!DOCTYPE html>
<html>
<head>
    <title>Test RajaGambar Upload</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover { border-color: #999; }
        .result { margin: 20px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test RajaGambar Upload</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>Click here to select an image file</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div>
            <label>Directory: <input type="text" id="directory" value="test" placeholder="test"></label><br><br>
            <label><input type="checkbox" id="autoOptimize" checked> Auto Optimize</label><br><br>
            <button onclick="uploadFile()">Upload & Process</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.querySelector('.upload-area p').textContent = `Selected: ${file.name}`;
            }
        });

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const directory = document.getElementById('directory').value || 'test';
            const autoOptimize = document.getElementById('autoOptimize').checked;
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="result error">Please select a file first!</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('directory', directory);
            formData.append('auto_optimize', autoOptimize ? '1' : '0');
            
            resultDiv.innerHTML = '<div class="result">Uploading...</div>';
            
            try {
                const response = await fetch('https://hotel.rid/api/rajagambar/upload-process', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>Upload Successful!</h3>
                            <p><strong>File:</strong> ${data.data.filename}</p>
                            <p><strong>URL:</strong> <a href="${data.data.url}" target="_blank">${data.data.url}</a></p>
                            <p><strong>Size:</strong> ${data.data.size} bytes</p>
                            <p><strong>Dimensions:</strong> ${data.data.width}x${data.data.height}</p>
                            <img src="${data.data.url}" style="max-width: 300px; margin-top: 10px;" alt="Processed image">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                console.error('Upload error:', error);
                resultDiv.innerHTML = `<div class="result error">Upload failed: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>

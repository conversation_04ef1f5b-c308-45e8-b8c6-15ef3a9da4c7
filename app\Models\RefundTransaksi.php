<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperRefundTransaksi
 */
class RefundTransaksi extends Model
{
    use HasFactory;
    public function getTable()
    {
         return config('tabel.t_refund_transaksi.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_refund_transaksi.kolom', []);
    }
	    public function refund()
    {
        return $this->belongsTo(Refund::class, 'refund_id', 'id');
    }
}

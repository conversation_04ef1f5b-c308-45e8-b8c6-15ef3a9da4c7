<?php

namespace App\Aplikasi\Hotel\Resources\KamarResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class KamarTipeRelationManagers extends RelationManager
{
    protected static string $relationship = 'tipe';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nama')
                    ->required()
                    ->maxLength(255)
                    ->label('Nama Tipe Kamar'),
                Forms\Components\TextInput::make('harga_modal')
                    ->numeric()
                    ->label('Harga Modal'),
                Forms\Components\FileUpload::make('foto')
                    ->image()
                    ->directory('kamar-tipe')
                    ->label('Foto'),
                Forms\Components\Textarea::make('ket')
                    ->maxLength(65535)
                    ->label('Keterangan'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nama')
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama Tipe Kamar'),
                Tables\Columns\TextColumn::make('harga_modal')
                    ->money('IDR')
                    ->label('Harga Modal'),
                Tables\Columns\ImageColumn::make('foto')
                    ->label('Foto'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
<?php

namespace App\Aplikasi\Hotel\Resources;


use App\Aplikasi\Hotel\Resources\LaporanResource\Pages;
use App\Aplikasi\Hotel\Models\Reservasi;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;


class LaporanResource extends Resource
{
    protected static ?string $model = Reservasi::class;
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'laporan';


    protected static ?string $navigationLabel = 'Laporan reservasi';
    protected static ?string $navigationGroup = 'Laporan';
    protected static ?string $slug = 'laporan/reservasi';
    protected static ?string $recordTitleAttribute = 'LAPORAN RESERVASI';
    protected static ?string $title = 'LAPORAN RESERVASI';

    protected static bool $canCreate = false;


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLaporans::route('/'),
            'create' => Pages\CreateLaporan::route('/create'),
            'print' => Pages\PrintLaporan::route('/print'),
            'edit' => Pages\EditLaporan::route('/{record}/edit'),
        ];
    }
}

<?php

namespace App\Aplikasi\Hotel\Forms\Components;



use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\KamarTipe;
 
use App\Aplikasi\Hotel\Services\Keranjang;
use Carbon\Carbon;
use Closure;
use Filament\Forms\Components\Field;
use Illuminate\Support\Collection;

class PilihKamar extends Field
{
    protected string $view = 'hotel::components.pilih_kamar';
    public ?Closure $getKamarTersediaUsing = null;

    // public function getKamarTersedia(?string $checkIn = null, ?string $checkOut = null): Collection
    // {
    //     if ($this->getKamarTersediaUsing) {
    //         return call_user_func($this->getKamarTersediaUsing, $checkIn, $checkOut);
    //     }

    //     if (empty($checkIn) || empty($checkOut)) {
    //         return collect([]);
    //     }

    //     $checkInDate = Carbon::parse($checkIn);
    //     $checkOutDate = Carbon::parse($checkOut);

    //     // Dapatkan semua tipe kamar
    //     $tipeKamars = KamarTipe::all();
    //     $result = collect();

    //     foreach ($tipeKamars as $tipe) {
    //         $kamars = Kamar::where('kategori_id', $tipe->id)->get();
            
    //         if ($kamars->count() > 0) {
    //             // Ambil informasi dari kamar pertama
    //             $firstKamar = $kamars->first();
    //             $spesifikasi = $this->processJsonField($firstKamar->spesifikasi);
                
    //             // Transformasi koleksi kamar
    //             $mappedKamars = $kamars->map(function ($kamar) use ($checkInDate, $checkOutDate) {
    //                 return [
    //                     'id' => $kamar->id,
    //                     'nama' => $kamar->nama,
    //                     'tersedia' => $kamar->isAvailable($checkInDate, $checkOutDate),
    //                 ];
    //             });
                
    //             $result->push([
    //                 'tipe_id' => $tipe->id,
    //                 'tipe_nama' => $tipe->nama,
    //                 'harga' => $firstKamar->harga, // Mengambil harga dari kamar pertama
    //                 'fasilitas' => $firstKamar->fasilitas,
    //                 'spesifikasi' => $spesifikasi,
    //                 'kamars' => $mappedKamars
    //             ]);
    //         }
    //     }

    //     return $result;
    // }

    public function getKamarTersedia(?string $checkIn = null, ?string $checkOut = null): Collection
{
    if ($this->getKamarTersediaUsing) {
        return call_user_func($this->getKamarTersediaUsing, $checkIn, $checkOut);
    }

    if (empty($checkIn) || empty($checkOut)) {
        return collect([]);
    }

    $checkInDate = Carbon::parse($checkIn);
    $checkOutDate = Carbon::parse($checkOut);

    // Dapatkan semua tipe kamar
    $tipeKamars = KamarTipe::all();
    $result = collect();

    foreach ($tipeKamars as $tipe) {
        $kamars = Kamar::where('kategori_id', $tipe->id)->get();
        
        if ($kamars->count() > 0) {
            // Ambil informasi dari kamar pertama
            $firstKamar = $kamars->first();
            $spesifikasi = $this->processJsonField($firstKamar->spesifikasi);
            
            // Transformasi koleksi kamar
            $mappedKamars = $kamars->map(function ($kamar) use ($checkInDate, $checkOutDate) {
                return [
                    'id' => $kamar->id,
                    'nama' => $kamar->nama,
                    'tersedia' => $kamar->isAvailable($checkInDate, $checkOutDate),
                ];
            });
            
            // Hitung jumlah kamar tersedia dalam grup ini
            $jumlahTersedia = $mappedKamars->where('tersedia', true)->count();
            
            $result->push([
                'tipe_id' => $tipe->id,
                'tipe_nama' => $tipe->nama,
                'harga' => $firstKamar->harga,
                'fasilitas' => $firstKamar->fasilitas,
                'spesifikasi' => $spesifikasi,
                'kamars' => $mappedKamars,
                'jumlah_tersedia' => $jumlahTersedia
            ]);
        }
    }

    // Urutkan hasil berdasarkan jumlah kamar tersedia (yang paling banyak tersedia muncul pertama)
    return $result->sortByDesc('jumlah_tersedia')->values();
}

    protected function processJsonField($field)
    {
        if (is_string($field) && $this->isJson($field)) {
            return json_decode($field, true);
        }
        return $field ?? [];
    }

    protected function isJson($string) {
        if (!is_string($string)) return false;
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    public function getKamarTersediaUsing(?Closure $callback): static
    {
        $this->getKamarTersediaUsing = $callback;
        return $this;
    }



  
public function tambahKeKeranjang(int $kamarId, string $checkIn, string $checkOut): void
{ 
    $keranjang = app(Keranjang::class);
    $checkInDate = Carbon::parse($checkIn);
    $checkOutDate = Carbon::parse($checkOut);
    $durasi = max(1, $checkOutDate->diffInDays($checkInDate));
    
    // Ambil data kamar
    $kamar = Kamar::find($kamarId);
    if (!$kamar) {
        return;
    }
    
    $tipeKamar = KamarTipe::find($kamar->kategori_id);
    $namaItem = $tipeKamar ? "{$tipeKamar->nama} - {$kamar->nama}" : $kamar->nama;
    
    // Siapkan item untuk keranjang
    $item = [
        'id' => $kamarId,
        'jenis' => 'jasa',
        'produk_id' => $kamar->id,
        'kamar_id' => $kamar->id,
        'nama_item' => $namaItem,
        'harga_modal' => $kamar->harga_modal ?: 0,
        'harga' => $kamar->harga,
        'jumlah' => $durasi,
        'ket' => "Check-in: {$checkInDate->format('d/m/Y')} - Check-out: {$checkOutDate->format('d/m/Y')}",
        'check_in' => $checkIn,
        'check_out' => $checkOut
    ];
    
    $keranjang->tambahItem($item);
}


 

}
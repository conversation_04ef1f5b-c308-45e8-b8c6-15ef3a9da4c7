<?php

namespace Mo<PERSON>les\RajaGambar\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\RajaGambar\Services\RajaGambarService;

class SpatieServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind RajaGambarService ke container
        $this->app->singleton(RajaGambarService::class, function ($app) {
            return new RajaGambarService();
        });

        // Alias untuk kemudahan akses
        $this->app->alias(RajaGambarService::class, 'rajagambar');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish konfigurasi jika diperlukan
        $this->publishes([
            __DIR__ . '/../../config/rajagambar.php' => config_path('rajagambar.php'),
        ], 'rajagambar-config');

        // Merge konfigurasi default
        $this->mergeConfigFrom(
            __DIR__ . '/../../config/rajagambar.php',
            'rajagambar'
        );
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            RajaGambarService::class,
            'rajagambar',
        ];
    }
}

<?php

namespace App\Models;

use App\Traits\PakaiJcol;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperProduk
 */
class Produk extends Model
{
    use PakaiJcol;

   public function getTable()
   {
        return config('tabel.t_produk.nama_tabel', 'produk');
   }

   public function getFillable()
   {
        return config('tabel.t_produk.kolom', []);
   }

    protected $casts = [
       
        'stok' => 'integer',
     
       'json' => 'array',
        'fasilitas' => 'array',
        'spesifikasi' => 'array',
    ];


    // protected $appends = [
    //     'price_range',
    // ];


     



    public function hargajual()
    {
        return $this->hasMany(HargaJual::class, 'produk_id', 'id');
    }

 

 

    public function induk(): BelongsTo
    {
        return $this->belongsTo(self::class, 'sub');
    }


    public function transaksi(): HasMany
    {
        return $this->hasMany(Transaksi::class);
    }


    public function tersedia(): bool
    {
        return $this->stok > 0;
    }



 
    public function getGambarUrlAttribute(): ?string
    {
        if (empty($this->gambar)) {
            return null;
        }

        // Jika gambar sudah berupa URL lengkap
        if (filter_var($this->gambar, FILTER_VALIDATE_URL)) {
            return $this->gambar;
        }

        // Jika gambar disimpan di storage
        return asset('storage/'.$this->gambar);
    }


    public function scopeAktif($query)
    {
        return $query->where('tampil', true);
    }


 

    public function scopePencarian($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('nama', 'like', "%{$keyword}%")
                ->orWhere('barcode', 'like', "%{$keyword}%")
                ->orWhere('ket', 'like', "%{$keyword}%");
        });
    }


    public function getHargaUntukJumlah(int $jumlah): float
    {
        // Cari harga jual yang sesuai dengan jumlah dan masih aktif
        $hargaJual = $this->hargaJual()
            ->whereRaw('(tgl_mulai IS NULL OR tgl_mulai <= NOW())')
            ->whereRaw('(tgl_selesai IS NULL OR tgl_selesai >= NOW())')
            ->where(function ($query) use ($jumlah) {
                $query->whereNull('minimal')
                    ->orWhere('minimal', '<=', $jumlah);
            })
            ->where(function ($query) use ($jumlah) {
                $query->whereNull('maksimal')
                    ->orWhere('maksimal', '>=', $jumlah);
            })
            ->orderBy('harga', 'asc')
            ->first();

        // Jika harga jual ditemukan, gunakan itu
        if ($hargaJual) {
            return $hargaJual->harga;
        }

        // Jika tidak, gunakan harga default produk
        return $this->harga;
    }

    public function hitungKeuntungan(): float
    {
        return $this->harga - $this->harga_modal;
    }


    public function hitungPersentaseKeuntungan(): float
    {
        if ($this->harga_modal <= 0) {
            return 0;
        }

        return ($this->hitungKeuntungan() / $this->harga_modal) * 100;
    }


    public function formatHarga(): string
    {
        if ($this->punyaVarian()) {
            $minHarga = number_format($this->price_range['min'], 0, ',', '.');
            $maxHarga = number_format($this->price_range['max'], 0, ',', '.');

            if ($this->price_range['min'] === $this->price_range['max']) {
                return "Rp {$minHarga}";
            }

            return "Rp {$minHarga} - Rp {$maxHarga}";
        }

        return "Rp ".number_format($this->harga, 0, ',', '.');
    }


    public function updateStok(int $jumlah, string $operasi = 'kurang'): bool
    {
        if ($operasi === 'kurang') {
            // Cek apakah stok cukup
            if ($this->stok < $jumlah) {
                return false;
            }

            $this->stok -= $jumlah;
        } else {
            $this->stok += $jumlah;
        }

        return $this->save();
    }
}
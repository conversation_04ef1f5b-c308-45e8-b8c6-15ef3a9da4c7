<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;

use App\Aplikasi\Hotel;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Resources\ReservasiResource;
use App\Aplikasi\Hotel\Resources\ReservasiResource\Pages\KamarManager;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ListReservasis extends ListRecords
{
    protected static string $resource = ReservasiResource::class;
    protected static ?string $title = 'List Reservasi';
    protected static ?string $createActionLabel = 'Tambah Reservasi';
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        // Cache konfig reservasi_status untuk mengurangi query berulang
        $reservasiStatus = cache()->remember('reservasi_status', now()->addHour(), function () {
            return Konfig::where('nama', 'reservasi_status')->first();
        });

        return $table
            ->striped()
            ->defaultSort('id', 'desc')
            ->emptyStateActions([
                Action::make('create')
                    ->label('Tambah Reservasi')
                    ->url(ReservasiResource::getUrl('create'))
                    ->icon('heroicon-m-plus')
                    ->button(),
            ])
            ->emptyStateDescription('RESERVASI MASIH KOSONG ')
            // Eager load relasi untuk mengurangi N+1 query
            ->modifyQueryUsing(function (Builder $query) {
                return $query->with(['tamu', 'kamar.tipe', 'user', 'transaksi'])
                             ->withSum('pembayaran as total_pembayaran', 'jumlah');
            })

            ->columns([


                Tables\Columns\TextColumn::make('id')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('tamu.nama')->limit(20)
                    ->description(fn($record) => $record->tamu ? new HtmlString("<span class='text-xs text-gray-500 text-center'>" . $record->tamu->telpon . "</span>") : ''),

                TextColumn::make('kamar.nama')->prefix("NO : ")
                    ->description(fn($record) => $record->kamar && $record->kamar->tipe ? new HtmlString("<span class='text-xs text-gray-500 text-center'>" . $record->kamar->tipe->nama . "</span>") : ''),

                Tables\Columns\TextColumn::make('check_in')->label('check in/ check out')
                    ->formatStateUsing(fn($record) => Carbon::parse($record->check_in)->format("d/m/Y H:i") . " - " . Carbon::parse($record->check_out)->format("d/m/Y H:i"))
                    ->description(fn($record) => new HtmlString("<span class='text-xs text-gray-500 text-center'>" . Carbon::parse($record->check_in)->diffInDays($record->check_out) . " Malam  </span>")),

                TextColumn::make('status_reservasi')
                    ->formatStateUsing(fn($record) => $record->statusReservasi())
                    ->badge()
                    ->colors([
                        'success' => fn($state) => $state == "SCI",
                        'danger' => fn($state) => $state == "SCO",
                        'primary' => fn($state) => $state == "BK",
                    ])
                    ->searchable(),

                TextColumn::make('pembayaran')
                    ->badge()
                    ->getStateUsing(function($record) {
                        // Gunakan data yang sudah di-eager load
                        $totalPembayaran = $record->total_pembayaran ?? 0;
                        $sisaTagihan = $record->totalHarga() - $totalPembayaran;
                        return number_format($sisaTagihan);
                    })
                    ->formatStateUsing(fn($state) => $state < 1 ? "lunas" : "belum")
                    ->colors([
                        'success' => fn($state) => $state < 1,
                        'danger' => fn($state) => $state > 1,
                    ])
                    ->description(function($record) {
                        // Gunakan data yang sudah di-eager load
                        $totalPembayaran = $record->total_pembayaran ?? 0;
                        $sisaTagihan = $record->totalHarga() - $totalPembayaran;
                        return $sisaTagihan > 1 ? new HtmlString("<span class='text-xs text-gray-500 text-center'> -Rp. " . number_format($sisaTagihan) . "</span>") : "";
                    }),


                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal')
                    ->dateTime('d F Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                SelectFilter::make('status_reservasi')
                    ->options(function() use ($reservasiStatus) {
                        // Gunakan data yang sudah di-cache
                        return $reservasiStatus ? json_decode($reservasiStatus->value, true) : [];
                    }),
                SelectFilter::make('tamu_id')->label('Tamu')->relationship('tamu', 'nama'),
                Filter::make('check_in')->form([
                    DateTimePicker::make('created_from')->label('Check in dari'),
                    DatetimePicker::make('created_until')->label('Check in sampai'),
                ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('check_in', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('check_in', '<=', $date),
                            );
                    }),
                Filter::make('check_out')->form([
                    DateTimePicker::make('created_from')->label('Check out dari'),
                    DatetimePicker::make('created_until')->label('Check out sampai'),
                ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('check_out', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('check_out', '<=', $date),
                            );
                    }),



            ], layout: FiltersLayout::Modal)->filtersFormMaxHeight('600px')->filtersFormColumns(2)
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make()->visible(Auth::user()->can('adminhotel.reservasi.semua') || Auth::user()->can('adminhotel.reservasi.edit'))
                    ,
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\Action::make('invoice')
                        ->label('Invoice')
                        ->icon('heroicon-o-document-text')
                        ->url(fn($record) => ReservasiResource::getUrl('invoice', ['record' => $record]))
                        ->color('success')
                        ->openUrlInNewTab(),
                ]),


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ])->visible(Auth::user()->can('adminhotel.reservasi.semua')),
            ]);
    }


    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            // ...

        ]);
    }
}

<?php

declare(strict_types=1);

namespace Modules\Rajapicker\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Modules\Rajapicker\Models\Media;
use Illuminate\Support\Facades\Cache;

class GambarController extends Controller
{
    /**
     * Render gambar secara dinamis berdasarkan query string.
     *  - /gambar?id=1
     *  - /gambar?uuid=xxx
     *  - /gambar?nama=filename.jpg
     */
    public function render(Request $request)
    {
        // Jika mode debug diaktifkan, kita akan mengumpulkan detail langkah dan mengembalikan JSON
        $debugMode = $request->boolean('debug') || $request->has('debug');
        $debugData = [
            'request' => $request->query(),
            'found_media' => false,
            'path' => null,
            'status' => null,
        ];

        try {
            // 1. Temukan media berdasarkan id, uuid, atau nama
            $media = null;

            if ($request->filled('id')) {
                $media = Cache::remember("media_{$request->query('id')}", 3600, function() use ($request) {
                    return Media::find($request->query('id'));
                });
            } elseif ($request->filled('uuid')) {
                $media = Media::where('uuid', $request->query('uuid'))->first();
            } elseif ($request->filled('nama')) {
                $nama = $request->query('nama');
                $media = Media::where('file_name', $nama)
                    ->orWhere('name', $nama)
                    ->first();
            }

            // 2. Jika media ditemukan dan file ada, tampilkan file
            if ($media) {
                $debugData['found_media'] = true;
                $debugData['media_id'] = $media->id;
                $debugData['file_name'] = $media->file_name;
                $debugData['disk'] = $media->disk;
                $debugData['collection'] = $media->collection_name;

                // Ambil path file fisik dengan fallback jika terjadi error pada Spatie
                try {
                    $path = $media->getPath();
                } catch (\Throwable $e) {
                    // Jika getPath() gagal (mis. FileDoesNotExist atau disk selain lokal)
                    try {
                        $path = \Illuminate\Support\Facades\Storage::disk($media->disk ?? 'public')
                            ->path($media->getFullPath());
                    } catch (\Throwable $e2) {
                        Log::warning('GambarController: gagal menentukan path media ID ' . $media->id);
                        $path = null;
                    }
                }

                $debugData['path'] = $path;

                if ($path && file_exists($path)) {
                    $debugData['status'] = 'file_exists';

                    $mimeType = $media->mime_type ?? mime_content_type($path) ?: 'application/octet-stream';

                    // Pastikan tidak ada output lain yang bocor
                    if (function_exists('ob_get_level')) {
                        while (ob_get_level() > 0) {
                            ob_end_clean();
                        }
                    }

                    $response = response()->file($path, [
                        'Content-Type' => $mimeType,
                    ]);

                    // Header cache 7 hari
                    $response->headers->set('Cache-Control', 'public, max-age=604800, immutable');

                    if ($debugMode) {
                        return response()->json(array_merge($debugData, ['mime' => $mimeType]), 200);
                    }

                    return $response;
                } else {
                    $debugData['status'] = 'file_missing_on_disk';
                    // Jika file tidak ada di disk, coba redirect ke URL publik (CDN atau storage symlink)
                    $publicUrl = $media->getUrlAttribute();
                    if ($publicUrl) {
                        if ($debugMode) {
                            return response()->json(array_merge($debugData, ['redirect_to' => $publicUrl]), 200);
                        }

                        return redirect()->to($publicUrl, 302, [], false);
                    }
                }
            }

            // 3. Fallback ke noimage.jpg atau 404
            $debugData['status'] = 'not_found_using_fallback';
            $fallback = public_path('noimage.jpg');
            if (file_exists($fallback)) {
                if ($debugMode) {
                    return response()->json($debugData, 404);
                }
                // Bersihkan buffer sebelum kirim fallback
                if (function_exists('ob_get_level')) {
                    while (ob_get_level() > 0) {
                        ob_end_clean();
                    }
                }

                return response()->file($fallback, [
                    'Content-Type' => 'image/jpeg',
                ]);
            }

            // Jika tidak ada fallback, lempar 404
            if ($debugMode) {
                return response()->json($debugData, 404);
            }

            abort(404);
        } catch (\Throwable $e) {
            Log::error('Dynamic image render error: ' . $e->getMessage());
            if ($debugMode) {
                return response()->json(array_merge($debugData, [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]), 500);
            }

            abort(404);
        }
    }
} 
<?php

namespace App\Aplikasi\Hotel\Widgets;



use App\Aplikasi\Hotel\Models\Kamar;
use App\Aplikasi\Hotel\Models\Reservasi;


use App\Aplikasi\Hotel\Models\Transaksi;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        // Hitung tamu yang check-in hari ini
        $checkInHariIni = Reservasi::where('check_in', '>=', Carbon::today())
            ->where('check_in', '<', Carbon::tomorrow())
            ->count();

        // Hitung total kamar tersedia
        $totalKamar = Kamar::count();
        $kamarTerpakai = Reservasi::where('status_reservasi', '!=', 'SCO')
            ->where('check_in', '<=', Carbon::now())
            ->where('check_out', '>=', Carbon::now())
            ->count();
        $kamarTersedia = $totalKamar - $kamarTerpakai;

        // Hitung pendapatan hari ini
        $pendapatanHariIni = Transaksi::whereHas('reservasi', function ($query) {
            $query->where('created_at', '>=', Carbon::today())
                  ->where('created_at', '<', Carbon::tomorrow());
        })->sum('harga');

        // Hitung total reservasi
        $totalReservasi = Reservasi::count();
        $reservasiBulanIni = Reservasi::where('created_at', '>=', Carbon::now()->startOfMonth())
            ->count();
        $persentaseKenaikan = $totalReservasi > 0 
            ? round(($reservasiBulanIni / $totalReservasi) * 100) 
            : 0;

        return [
            Stat::make('Tamu Check-In Hari Ini', $checkInHariIni)
                ->description('Jumlah tamu yang check-in hari ini')
                ->descriptionIcon('heroicon-o-users')
                ->color('primary'),

            Stat::make('Kamar Tersedia', $kamarTersedia . ' dari ' . $totalKamar)
                ->description($kamarTersedia > 0 ? 'Kamar siap digunakan' : 'Semua kamar terpakai')
                ->descriptionIcon('heroicon-o-building-office-2')
                ->color($kamarTersedia > 0 ? 'success' : 'danger'),

            Stat::make('Pendapatan Hari Ini', 'Rp ' . number_format($pendapatanHariIni, 0, ',', '.'))
                ->description('Pendapatan dari transaksi hari ini')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('success'),

            Stat::make('Reservasi Bulan Ini', $reservasiBulanIni)
                ->description($persentaseKenaikan . '% dari total reservasi')
                ->descriptionIcon('heroicon-o-arrow-trending-up')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->color('warning'),
        ];
    }
}
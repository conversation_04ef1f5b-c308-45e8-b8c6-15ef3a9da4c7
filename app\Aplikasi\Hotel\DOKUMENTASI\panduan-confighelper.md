# Panduan ConfigHelper

## Daftar Isi
- [Pendahuluan](#pendahuluan)
- [<PERSON><PERSON><PERSON><PERSON>](#pemasangan)
- [<PERSON>](#cara-kerja)
- [Pengg<PERSON><PERSON>](#penggunaan-dasar)
- [Fungsi-Fungsi <PERSON>](#fungsi-fungsi-tersedia)
- [<PERSON><PERSON><PERSON>](#contoh-penggunaan)
- [Tips dan <PERSON>](#tips-dan-trik)
- [Panduan Teknis](#panduan-teknis)
- [Troubleshooting](#troubleshooting)

## Pendahuluan

ConfigHelper adalah kelas pembantu (helper class) yang dirancang untuk mempermudah akses ke konfigurasi sistem perhotelan pada aplikasi berbasis Laravel dan FilamentPHP. Helper ini memungkinkan Anda untuk mengambil nilai-nilai konfigurasi dengan cara yang lebih terstruktur dan konsisten.

## Pemasangan

### Prasyarat
- Laravel 10
- PHP 8.2
- FilamentPHP 3.2

### Langka<PERSON>-<PERSON><PERSON><PERSON>

1. **Buat file konfigurasi**

   Buat file `aplikasi.php` di direktori `app/Aplikasi/Hotel/config/`:

   ```php
   <?php
   
   return array(
       'jenis_identitas' => [ /* data disini */ ],
       'referensi' => [ /* data disini */ ],
       // konfigurasi lainnya
   );
   ```

2. **Buat kelas ConfigHelper**

   Buat file `ConfigHelper.php` di direktori `app/Aplikasi/Hotel/Helpers/`:

   ```php
   <?php
   
   namespace App\Aplikasi\Hotel\Helpers;
   
   use Illuminate\Support\Arr;
   
   class ConfigHelper
   {
       // Implementasi method-method helper
   }
   ```

3. **Daftarkan konfigurasi**

   Pada `HotelServiceProvider.php`, pastikan konfigurasi terdaftar dengan benar:

   ```php
   public function register()
   {
       // Gabungkan konfigurasi
       $this->mergeConfigFrom(
           __DIR__ . '/config/aplikasi.php', 'hotel'
       );
   }
   ```

4. **Daftarkan Service Provider**

   Tambahkan service provider di `config/app.php`:

   ```php
   'providers' => [
       // Service provider lainnya
       App\Aplikasi\Hotel\HotelServiceProvider::class,
   ],
   ```

## Cara Kerja

ConfigHelper bekerja dengan mengakses file konfigurasi yang sudah didaftarkan dengan nama `hotel`. Helper menggunakan fungsi `config()` bawaan Laravel untuk mengambil nilai-nilai dari file konfigurasi tersebut.

Setiap metode dalam ConfigHelper dirancang untuk mengambil bagian tertentu dari konfigurasi aplikasi hotel, seperti jenis identitas, fasilitas kamar, status reservasi, dan sebagainya.

## Penggunaan Dasar

Untuk menggunakan ConfigHelper, Anda cukup mengimpor kelasnya dan memanggil metode statis yang Anda perlukan:

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;

// Contoh pengambilan daftar fasilitas kamar
$fasilitasKamar = ConfigHelper::getFasilitasKamar();

// Contoh pengambilan status reservasi
$statusReservasi = ConfigHelper::getReservasiStatus();
```

## Fungsi-Fungsi Tersedia

### Manajemen Data Master

| Fungsi | Deskripsi | Parameter | Return |
|--------|-----------|-----------|--------|
| `getJenisIdentitas()` | Mendapatkan daftar jenis identitas tamu | - | `array` |
| `getReferensi()` | Mendapatkan daftar referensi reservasi | - | `array` |
| `getFasilitasKamar()` | Mendapatkan daftar fasilitas kamar | - | `array` |
| `getSpesifikasiKamar()` | Mendapatkan daftar spesifikasi kamar | - | `array` |
| `getJadwalShift()` | Mendapatkan daftar jadwal shift | - | `array` |
| `getPembayaranStatus()` | Mendapatkan daftar status pembayaran | - | `array` |
| `getMeja(string $lokasi = null)` | Mendapatkan daftar meja, opsional filter berdasarkan lokasi | `$lokasi` - Filter lokasi | `array` |
| `getJenisPesanan()` | Mendapatkan daftar jenis pesanan | - | `array` |
| `getStatusHousekeeping()` | Mendapatkan daftar status housekeeping | - | `array` |

### Manajemen Status Reservasi

| Fungsi | Deskripsi | Parameter | Return |
|--------|-----------|-----------|--------|
| `getReservasiStatus(string $kode = null)` | Mendapatkan daftar/nilai status reservasi | `$kode` - Kode status opsional | `array\|string` |
| `getReservasiStatusCode(string $nama)` | Mendapatkan kode dari nama status | `$nama` - Nama status | `string\|null` |

### Manajemen Konfigurasi Printer

| Fungsi | Deskripsi | Parameter | Return |
|--------|-----------|-----------|--------|
| `getPrinterKasir(string $key = null, $default = null)` | Mendapatkan konfigurasi printer kasir | `$key` - Kunci konfigurasi, `$default` - Nilai default | `array\|mixed` |

### Kalkulasi dan Validasi 

| Fungsi | Deskripsi | Parameter | Return |
|--------|-----------|-----------|--------|
| `getBiayaLayanan(string $jenis = null, float $default = 0)` | Mendapatkan nilai biaya layanan | `$jenis` - Jenis biaya, `$default` - Nilai default | `array\|float` |
| `hitungTotalDenganLayanan(float $subtotal, bool $includePajak = true, bool $includeServis = true, bool $includeDiskon = true)` | Menghitung total dengan biaya layanan | `$subtotal`, flag biaya | `array` detail perhitungan |
| `formatRupiah(float $amount, bool $withSymbol = true)` | Format angka ke format rupiah | `$amount` - Jumlah, `$withSymbol` - Tampilkan simbol Rp | `string` |
| `isFasilitasValid(string $fasilitas)` | Memeriksa validitas fasilitas | `$fasilitas` - Nama fasilitas | `bool` |
| `isSpesifikasiValid(string $spesifikasi)` | Memeriksa validitas spesifikasi | `$spesifikasi` - Nama spesifikasi | `bool` |

### Utilitas

| Fungsi | Deskripsi | Parameter | Return |
|--------|-----------|-----------|--------|
| `arrayToOptions(array $array)` | Mengubah array menjadi format opsi dropdown | `$array` - Array sumber | `array` |
| `get(string $key, $default = null)` | Mengambil nilai konfigurasi dengan dot notation | `$key` - Path konfigurasi, `$default` - Nilai default | `mixed` |

## Contoh Penggunaan

### 1. Mengambil Daftar Jenis Identitas untuk Form

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Filament\Forms\Components\Select;

// Di dalam definisi form
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Select::make('jenis_identitas')
                ->label('Jenis Identitas')
                ->options(ConfigHelper::getJenisIdentitas())
                ->required(),
            // komponen form lainnya
        ]);
}
```

### 2. Menghitung Total dengan Layanan

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;

// Menghitung total pesanan dengan pajak dan servis, tanpa diskon
$subtotal = 120000;
$hasil = ConfigHelper::hitungTotalDenganLayanan($subtotal, true, true, false);

// Hasil dalam array:
// [
//     'subtotal' => 120000,
//     'pajak' => 18000, // 15% dari 120000
//     'pajak_persen' => 15,
//     'servis' => 1200, // 1% dari 120000
//     'servis_persen' => 1,
//     'diskon' => 0, 
//     'diskon_persen' => 1,
//     'total' => 139200, // 120000 + 18000 + 1200
// ]

// Menampilkan total dalam format rupiah
echo ConfigHelper::formatRupiah($hasil['total']); // "Rp 139.200"
```

### 3. Mendapatkan Status Reservasi untuk Tabel

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Filament\Tables\Columns\TextColumn;

// Di dalam definisi tabel
public static function table(Table $table): Table
{
    return $table
        ->columns([
            // kolom lainnya
            TextColumn::make('status_kode')
                ->label('Status')
                ->formatStateUsing(fn (string $state): string => ConfigHelper::getReservasiStatus($state))
                ->badge()
                ->color(fn (string $state): string => match ($state) {
                    'SCI' => 'success',
                    'SCO' => 'danger',
                    'BK' => 'warning',
                    'PD' => 'gray',
                    default => 'gray',
                }),
        ]);
}
```

### 4. Validasi Input Fasilitas

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Illuminate\Validation\Rule;

// Di dalam aturan validasi
public function rules()
{
    return [
        'fasilitas' => [
            'required',
            'string',
            function ($attribute, $value, $fail) {
                if (!ConfigHelper::isFasilitasValid($value)) {
                    $fail('Fasilitas tidak valid.');
                }
            },
        ],
        // aturan lainnya
    ];
}

// Alternatif dengan Rule::in
public function rules()
{
    return [
        'fasilitas' => [
            'required',
            'string',
            Rule::in(ConfigHelper::getFasilitasKamar()),
        ],
        // aturan lainnya
    ];
}
```

### 5. Menggunakan ConfigHelper di Blade Template

```blade
@php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
@endphp

<div class="pricing">
    <p>Subtotal: {{ ConfigHelper::formatRupiah($subtotal) }}</p>
    
    @php
    $perhitungan = ConfigHelper::hitungTotalDenganLayanan($subtotal);
    @endphp
    
    <p>Pajak ({{ $perhitungan['pajak_persen'] }}%): {{ ConfigHelper::formatRupiah($perhitungan['pajak']) }}</p>
    <p>Servis ({{ $perhitungan['servis_persen'] }}%): {{ ConfigHelper::formatRupiah($perhitungan['servis']) }}</p>
    <p>Diskon ({{ $perhitungan['diskon_persen'] }}%): {{ ConfigHelper::formatRupiah($perhitungan['diskon']) }}</p>
    <p><strong>Total: {{ ConfigHelper::formatRupiah($perhitungan['total']) }}</strong></p>
</div>
```

### 6. Menggunakan ConfigHelper di Filament Resource

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;

class KamarResource extends Resource
{
    // ...

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('spesifikasi')
                    ->label('Spesifikasi Kamar')
                    ->options(ConfigHelper::arrayToOptions(ConfigHelper::getSpesifikasiKamar()))
                    ->required(),
                
                CheckboxList::make('fasilitas')
                    ->label('Fasilitas Kamar')
                    ->options(ConfigHelper::arrayToOptions(ConfigHelper::getFasilitasKamar()))
                    ->columns(2),
                
                // komponen form lainnya
            ]);
    }
}
```

## Tips dan Trik

### 1. Pembuatan Filter Meja Berdasarkan Lokasi

Gunakan `getMeja()` dengan parameter lokasi untuk memfilter meja:

```php
// Mendapatkan hanya meja di dalam
$mejaDalam = ConfigHelper::getMeja('dalam');

// Mendapatkan hanya meja di luar
$mejaLuar = ConfigHelper::getMeja('luar');
```

### 2. Menggunakan Dot Notation untuk Akses Konfigurasi Bersarang

```php
// Dapatkan nilai printer type
$printerType = ConfigHelper::get('printer_kasir.printer_type');

// Sama dengan
$printerType = ConfigHelper::getPrinterKasir('printer_type');
```

### 3. Perhitungan Total dengan Kustomisasi Biaya

```php
// Hanya hitung dengan pajak, tanpa servis dan diskon
$totalDenganPajak = ConfigHelper::hitungTotalDenganLayanan($subtotal, true, false, false);

// Hitung dengan pajak dan diskon, tanpa servis
$totalPajakDiskon = ConfigHelper::hitungTotalDenganLayanan($subtotal, true, false, true);
```

### 4. Mengoptimalkan Penggunaan ArrayToOptions untuk Dropdown

```php
// Contoh mengkonversi array numerik ke array asosiatif
$jenisPesanan = ConfigHelper::getJenisPesanan();
$opsiPesanan = ConfigHelper::arrayToOptions($jenisPesanan);

// Sebelum: [0 => 'takeaway', 1 => 'makan di tempat', 2 => 'delivery']
// Sesudah: ['takeaway' => 'takeaway', 'makan di tempat' => 'makan di tempat', 'delivery' => 'delivery']
```

### 5. Kombinasikan dengan Wire Model di Livewire

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Livewire\Component;

class PembayaranForm extends Component
{
    public $subtotal = 0;
    public $includePajak = true;
    public $includeServis = true;
    public $includeDiskon = false;
    
    public function hitungTotal()
    {
        return ConfigHelper::hitungTotalDenganLayanan(
            $this->subtotal,
            $this->includePajak,
            $this->includeServis,
            $this->includeDiskon
        );
    }
    
    public function render()
    {
        $perhitungan = $this->hitungTotal();
        
        return view('livewire.pembayaran-form', [
            'perhitungan' => $perhitungan,
        ]);
    }
}
```

## Panduan Teknis

### Struktur File Konfigurasi

File konfigurasi `aplikasi.php` disimpan di:

```
app/
└── Aplikasi/
    └── Hotel/
        └── config/
            └── aplikasi.php
```

Dan terdaftar dengan kunci `hotel` melalui `HotelServiceProvider`.

### Memperbarui Nilai Konfigurasi

Untuk memperbarui nilai konfigurasi saat runtime, gunakan fungsi `config()` bawaan Laravel:

```php
// Memperbarui nilai pajak
config(['hotel.biaya_layanan.pajak' => 10]);

// Sekarang ConfigHelper akan menggunakan nilai yang baru
$pajakPersen = ConfigHelper::getBiayaLayanan('pajak'); // akan mengembalikan 10
```

### Mengextend ConfigHelper dengan Method Tambahan

Anda dapat menambahkan metode baru ke ConfigHelper sesuai kebutuhan:

```php
<?php

namespace App\Aplikasi\Hotel\Helpers;

class ConfigHelper
{
    // Method yang sudah ada
    
    /**
     * Mendapatkan daftar jenis pembayaran
     *
     * @return array
     */
    public static function getJenisPembayaran(): array
    {
        return [
            'cash' => 'Tunai',
            'debit' => 'Kartu Debit',
            'credit' => 'Kartu Kredit',
            'transfer' => 'Transfer Bank',
            'qris' => 'QRIS',
        ];
    }
    
    /**
     * Mendapatkan daftar bank yang tersedia
     *
     * @return array
     */
    public static function getDaftarBank(): array
    {
        return [
            'bca' => 'Bank Central Asia',
            'bni' => 'Bank Negara Indonesia',
            'bri' => 'Bank Rakyat Indonesia',
            'mandiri' => 'Bank Mandiri',
            // daftar bank lainnya
        ];
    }
}
```

### Integrasi dengan Filament Icons

Saat menggunakan ConfigHelper dengan Filament, Anda bisa memanfaatkan icon dari blade-ui-kit/blade-icons:

```php
use App\Aplikasi\Hotel\Helpers\ConfigHelper;
use Filament\Forms\Components\Select;

Select::make('jenis_pembayaran')
    ->label('Jenis Pembayaran')
    ->options([
        'cash' => 'Tunai',
        'debit' => 'Kartu Debit',
        'credit' => 'Kartu Kredit',
        'transfer' => 'Transfer Bank',
        'qris' => 'QRIS',
    ])
    ->icon(function ($state) {
        return match ($state) {
            'cash' => 'heroicon-o-banknotes',
            'debit', 'credit' => 'heroicon-o-credit-card',
            'transfer' => 'heroicon-o-building-library',
            'qris' => 'heroicon-o-qr-code',
            default => 'heroicon-o-question-mark-circle',
        };
    });
```

## Troubleshooting

### Konfigurasi Tidak Ditemukan

Jika Anda mendapatkan nilai `null` atau array kosong dari ConfigHelper, periksa:

1. Pastikan `HotelServiceProvider` terdaftar di `config/app.php`
2. Pastikan jalur file konfigurasi benar di `HotelServiceProvider`
3. Coba hapus cache konfigurasi dengan perintah:

```bash
php artisan config:clear
```

### Nilai Default Tidak Berfungsi

Jika nilai default tidak berfungsi, pastikan Anda memberikan nilai default yang benar pada pemanggilan metode:

```php
// Contoh dengan nilai default
$printerName = ConfigHelper::getPrinterKasir('printer_unknown', 'default_printer');
// Jika 'printer_unknown' tidak ada, akan mengembalikan 'default_printer'
```

### Metode formatRupiah() Tidak Bekerja Dengan Benar

Pastikan parameter yang diberikan adalah numerik:

```php
// Salah
$harga = ConfigHelper::formatRupiah('100,000'); // Error: Expecting float

// Benar
$harga = ConfigHelper::formatRupiah(100000); // "Rp 100.000"
```

### Error Saat Mengambil Status Reservasi

Jika Anda mendapatkan error saat menggunakan `getReservasiStatus`, pastikan parameter kode yang diberikan valid:

```php
// Cara aman menggunakan getReservasiStatus dengan kode yang mungkin tidak valid
$status = ConfigHelper::getReservasiStatus($kode) ?? 'Status Tidak Dikenal';
```

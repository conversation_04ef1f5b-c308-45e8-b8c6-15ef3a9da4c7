<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\MetodePembayaran;
/**
 * @mixin IdeHelperPenjualan
 */
class Penjualan extends Model
{

  public function getTable()
  {
       return config('tabel.t_penjualan.nama_tabel', 'produk');
  }

  public function getFillable()
  {
       return config('tabel.t_penjualan.kolom', []);
  }


  // public function reservasi()
  // {
  //   return $this->belongsTo(Reservasi::class);
  // }

  // public function metodepembayaran()
  // {
  //   return $this->belongsTo(MetodePembayaran::class, 'id');
  // }

public function refund()
  {
    return $this->hasOne(Refund::class, 'penjualan_id', 'id');
  }

  public function transaksi()
  {
    return $this->hasMany(Transaksi::class, 'penjualan_id', 'id');
  }

  public function pembayaran()
  {
    return $this->hasMany(Pembayaran::class);
  }

  public function user()
  {
    return $this->belongsTo(User::class, 'karyawan_id');

  }


  /**
   * Menghitung total keseluruhan transaksi
   * 
   * Perhitungan mencakup:
   * 1. Total harga item (jumlah * harga dari setiap transaksi)
   * 2. Ditambah pajak
   * 3. Ditambah biaya servis
   * 4. Dikurangi diskon
   * 
   * @return float Total transaksi setelah perhitungan
   */
  public function grandTotal(): float
  {
    // Hitung total harga dari semua item transaksi
    $totalItem = $this->transaksi->sum(function ($item) {
      return $item->jumlah * $item->harga;
    });

    // Tambahkan pajak dan biaya servis
    $totalSebelumDiskon = $totalItem +
      ($this->pajak ?? 0) +
      ($this->servis ?? 0);

    // Kurangi diskon
    $grandTotal = $totalSebelumDiskon - ($this->diskon ?? 0);

    // Pastikan total tidak negatif
    return max(0, $grandTotal);
  }

  /**
   * Accessor untuk grand total yang dapat digunakan di Filament
   * 
   * @return float
   */
  public function getGrandTotalAttribute(): float
  {
    return $this->grandTotal();
  }

}

# Panduan Integrasi config/aplikasi.php dengan Filament PHP

## Pendahuluan

File konfigurasi `config/aplikasi.php` adalah tempat terpusat untuk menyimpan pengaturan aplikasi hotel yang dapat diakses di seluruh sistem. Panduan ini akan menjelaskan cara mengintegrasikan nilai-nilai dari file konfigurasi tersebut ke dalam berbagai komponen Filament seperti Resources, Form, Table, dan komponen lainnya.

## Daftar Isi

1. [Memahami Struktur config/aplikasi.php](#1-memahami-struktur-configaplikasiphp)
2. [Cara Mengakses <PERSON>](#2-cara-mengakses-nilai-konfigurasi)
3. [Integrasi dengan Form Fields](#3-integrasi-dengan-form-fields)
4. [Integrasi dengan Table Columns](#4-integrasi-dengan-table-columns)
5. [Integrasi dengan Filters](#5-integrasi-dengan-filters)
6. [Integrasi dengan Relation Managers](#6-integrasi-dengan-relation-managers)
7. [Pemrosesan Data dengan <PERSON>figurasi](#7-pemrosesan-data-dengan-nilai-konfigurasi)
8. [Tips dan Trik](#8-tips-dan-trik)
9. [Contoh Kasus Penggunaan](#9-contoh-kasus-penggunaan)

## 1. Memahami Struktur config/aplikasi.php

File `config/aplikasi.php` pada aplikasi hotel kita berisi konfigurasi seperti berikut:

```php
<?php

return [
    'jenis_identitas' => ["KTP", "SIM", "PASPORT", "LAINNYA"],
    
    'referensi' => ["traveloka", "TIKET.COM", "PRIBADI"],
    
    'fasilitas_kamar' => [
        "Tv Led", "minibar", "internet", "Teko Pemanas air", 
        "Bath hub", "tanpa sarapan", "Water shower panas & dingin", 
        "Kulkas", "View rinjani", "View air terjun", "View hutans"
    ],
    
    'reservasi_status' => [
        "SCI" => "Check in",
        "SCO" => "Check out",
        "BK" => "Boking",
        "PD" => "Pending"
    ],
    
    'spesifikasi_kamar' => ["Twin", "double", "single", "King", "Queen"],
    
    'biaya_layanan' => [
        "pajak" => "15",
        "servis" => "1",
        "diskon" => "1"
    ],
    
    'jadwal_shift' => ["pagi", "siang", "malam"],
    
    'pembayaran_status' => ["lunas"],
    
    'printer_kasir' => [
        "printer_type" => "windows",
        "printer_name" => "struk"
    ],
    
    'meja' => [
        "meja1" => "dalam",
        "meja2" => "dalam",
        "meja3" => "dalam",
        "meja4" => "dalam",
        "meja5" => "dalam",
        "meja6" => "luar",
        "meja7" => "luar",
        "meja8" => "luar",
        "meja9" => "luar",
        "meja10" => "luar",
    ],
    
    'jenis_pesanan' => [
        'takeaway',
        'makan di tempat',
        'delivery'
    ],
];
```

File ini berisi pengaturan dalam bentuk array dengan berbagai jenis data:
- **Array Sederhana**: Seperti `jenis_identitas`, `referensi`, `jadwal_shift`
- **Array Asosiatif (Key-Value)**: Seperti `reservasi_status`, `biaya_layanan`, `meja`

## 2. Cara Mengakses Nilai Konfigurasi

Untuk mengakses nilai-nilai dalam file konfigurasi, Laravel menyediakan fungsi `config()`:

```php
// Mengambil seluruh bagian konfigurasi
$seluruhConfig = config('hotel');

// Mengambil satu bagian konfigurasi
$jenisIdentitas = config('hotel.jenis_identitas');

// Mengambil nilai spesifik dalam array asosiatif
$biayaPajak = config('hotel.biaya_layanan.pajak');

// Memberikan nilai default jika konfigurasi tidak ada
$diskon = config('hotel.biaya_layanan.diskon', '0');
```

Di Filament PHP, kita bisa menggunakan fungsi `config()` ini di berbagai tempat seperti:
- Dalam closure untuk options pada Select, Radio, atau Checkbox
- Dalam formatter untuk menampilkan nilai di table column
- Dalam action handler
- Dalam callback validasi

## 3. Integrasi dengan Form Fields

### Select Field

```php
use Filament\Forms\Components\Select;

// Select dengan nilai dari konfigurasi (array sederhana)
Select::make('jenis_identitas')
    ->label('Jenis Identitas')
    ->options(function() {
        // Mengubah array sederhana menjadi bentuk key => value
        return array_combine(
            config('hotel.jenis_identitas'),
            config('hotel.jenis_identitas')
        );
    })
    ->required(),

// Select dengan nilai dari konfigurasi (array asosiatif)
Select::make('status_reservasi')
    ->label('Status Reservasi')
    ->options(config('hotel.reservasi_status'))
    ->required(),
```

### Checkbox List

```php
use Filament\Forms\Components\CheckboxList;

// Checkbox list untuk memilih multiple fasilitas
CheckboxList::make('fasilitas')
    ->label('Fasilitas Kamar')
    ->options(function() {
        return array_combine(
            config('hotel.fasilitas_kamar'),
            config('hotel.fasilitas_kamar')
        );
    })
    ->columns(2)
    ->helperText('Pilih fasilitas yang tersedia di kamar'),
```

### Radio Button

```php
use Filament\Forms\Components\Radio;

// Radio button untuk jenis pesanan
Radio::make('jenis_pesanan')
    ->label('Jenis Pesanan')
    ->options(function() {
        return array_combine(
            config('hotel.jenis_pesanan'),
            config('hotel.jenis_pesanan')
        );
    })
    ->inline()
    ->required(),
```

### TextInput dengan Validasi dari Konfigurasi

```php
use Filament\Forms\Components\TextInput;

// TextInput dengan validasi berdasarkan nilai konfigurasi
TextInput::make('persentase_pajak')
    ->label('Persentase Pajak (%)')
    ->numeric()
    ->default(config('hotel.biaya_layanan.pajak'))
    ->rules([
        fn() => function ($attribute, $value, $fail) {
            $defaultPajak = (float)config('hotel.biaya_layanan.pajak');
            if ($value < $defaultPajak * 0.5 || $value > $defaultPajak * 1.5) {
                $fail("Nilai pajak harus dalam rentang 50-150% dari nilai default ({$defaultPajak}%)");
            }
        },
    ]),
```

### Toggle dengan Visibilitas Berdasarkan Konfigurasi

```php
use Filament\Forms\Components\Toggle;

// Toggle yang hanya muncul jika fitur tertentu diaktifkan
Toggle::make('gunakan_printer')
    ->label('Cetak Struk Otomatis')
    ->visible(fn() => !empty(config('hotel.printer_kasir.printer_name')))
    ->helperText(function() {
        $printer = config('hotel.printer_kasir.printer_name');
        return "Struk akan dicetak ke printer: {$printer}";
    }),
```

## 4. Integrasi dengan Table Columns

### Text Column dengan Formatter

```php
use Filament\Tables\Columns\TextColumn;

// Text column untuk status dengan formatter menggunakan konfigurasi
TextColumn::make('status')
    ->label('Status Reservasi')
    ->formatStateUsing(function ($state) {
        return config('hotel.reservasi_status')[$state] ?? $state;
    })
    ->searchable(),
```

### Badge Column dengan Warna

```php
use Filament\Tables\Columns\BadgeColumn;

// Badge column dengan warna berdasarkan status
BadgeColumn::make('status')
    ->label('Status')
    ->formatStateUsing(function ($state) {
        return config('hotel.reservasi_status')[$state] ?? $state;
    })
    ->colors([
        'warning' => fn ($state) => $state === 'BK',
        'danger' => fn ($state) => $state === 'PD',
        'success' => fn ($state) => $state === 'SCI',
        'primary' => fn ($state) => $state === 'SCO',
    ]),
```

### Tags Column untuk Array Values

```php
use Filament\Tables\Columns\TagsColumn;

// Tags column untuk menampilkan fasilitas
TagsColumn::make('fasilitas')
    ->label('Fasilitas Kamar')
    ->separator(',')
    ->limitList(3) // Tampilkan maksimal 3 tag
    ->expandableLimitedList(), // Dapat diklik untuk melihat semua
```

### Icon Column untuk Boolean Status

```php
use Filament\Tables\Columns\IconColumn;

// Icon column untuk menampilkan status boolean
IconColumn::make('lokasi_meja')
    ->label('Lokasi')
    ->options([
        'heroicon-o-home' => fn ($state) => $state === 'dalam',
        'heroicon-o-sun' => fn ($state) => $state === 'luar',
    ])
    ->colors([
        'primary' => fn ($state) => $state === 'dalam',
        'success' => fn ($state) => $state === 'luar',
    ]),
```

## 5. Integrasi dengan Filters

### Select Filter dengan Nilai Konfigurasi

```php
use Filament\Tables\Filters\SelectFilter;

// Select filter untuk status reservasi
SelectFilter::make('status')
    ->label('Status Reservasi')
    ->options(config('hotel.reservasi_status')),
```

### Multi-select Filter

```php
use Filament\Tables\Filters\SelectFilter;

// Multi-select filter untuk fasilitas kamar
SelectFilter::make('fasilitas')
    ->label('Fasilitas Kamar')
    ->multiple()
    ->options(function() {
        return array_combine(
            config('hotel.fasilitas_kamar'),
            config('hotel.fasilitas_kamar')
        );
    })
    ->query(function (Builder $query, array $data): Builder {
        return $query->when(
            $data['values'],
            fn (Builder $query, $values): Builder => $query->whereJsonContains('fasilitas', $values),
        );
    }),
```

### Filter Kustom Berdasarkan Konfigurasi

```php
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;

// Filter kustom berdasarkan lokasi meja
Filter::make('lokasi_meja')
    ->form([
        Select::make('lokasi')
            ->label('Lokasi Meja')
            ->options([
                'dalam' => 'Dalam Ruangan',
                'luar' => 'Luar Ruangan',
            ]),
    ])
    ->query(function (Builder $query, array $data): Builder {
        return $query->when(
            $data['lokasi'],
            fn (Builder $query, $lokasi): Builder => 
                $query->whereHas('meja', function ($query) use ($lokasi) {
                    $query->whereIn('nama_meja', array_keys(
                        array_filter(config('hotel.meja'), fn($value) => $value === $lokasi)
                    ));
                })
        );
    })
    ->indicateUsing(function (array $data): ?string {
        if (empty($data['lokasi'])) {
            return null;
        }
        
        return 'Lokasi: ' . ($data['lokasi'] === 'dalam' ? 'Dalam Ruangan' : 'Luar Ruangan');
    }),
```

## 6. Integrasi dengan Relation Managers

### Relation Manager dengan Select Field dari Konfigurasi

```php
<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PembayaranRelationManager extends RelationManager
{
    protected static string $relationship = 'pembayaran';
    protected static ?string $recordTitleAttribute = 'kode_pembayaran';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('kode_pembayaran')
                    ->label('Kode Pembayaran')
                    ->required()
                    ->maxLength(20),
                    
                Forms\Components\TextInput::make('jumlah')
                    ->label('Jumlah Pembayaran')
                    ->numeric()
                    ->prefix('Rp')
                    ->required(),
                    
                Forms\Components\Select::make('status')
                    ->label('Status Pembayaran')
                    ->options(function() {
                        return array_combine(
                            config('hotel.pembayaran_status'),
                            config('hotel.pembayaran_status')
                        );
                    })
                    ->required(),
                    
                Forms\Components\DatePicker::make('tanggal_pembayaran')
                    ->label('Tanggal Pembayaran')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_pembayaran')
                    ->label('Kode Pembayaran')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('jumlah')
                    ->label('Jumlah')
                    ->money('IDR'),
                    
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'success' => fn ($state) => in_array($state, config('hotel.pembayaran_status')),
                        'danger' => fn ($state) => !in_array($state, config('hotel.pembayaran_status')),
                    ]),
                    
                Tables\Columns\TextColumn::make('tanggal_pembayaran')
                    ->label('Tanggal')
                    ->date('d/m/Y'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data) {
                        // Tambahkan opsi kalkulasi pembayaran otomatis
                        if (isset($data['hitung_otomatis']) && $data['hitung_otomatis']) {
                            $reservasi = $this->getOwnerRecord();
                            $pajak = (float)config('hotel.biaya_layanan.pajak');
                            $data['jumlah'] = $reservasi->total_harga * (1 + $pajak/100);
                        }
                        
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
```

## 7. Pemrosesan Data dengan Nilai Konfigurasi

### Hook untuk Modifikasi Data Sebelum Simpan

```php
// Di dalam resource Reservasi

protected function mutateFormDataBeforeCreate(array $data): array
{
    // Hitung total berdasarkan biaya layanan
    $hargaKamar = Kamar::find($data['kamar_id'])->harga_per_malam;
    $checkIn = Carbon::parse($data['tanggal_check_in']);
    $checkOut = Carbon::parse($data['tanggal_check_out']);
    $jumlahMalam = $checkOut->diffInDays($checkIn);
    
    $subtotal = $hargaKamar * $jumlahMalam;
    
    // Gunakan konfigurasi untuk menghitung biaya tambahan
    $pajak = $subtotal * ((float)config('hotel.biaya_layanan.pajak') / 100);
    $servis = $subtotal * ((float)config('hotel.biaya_layanan.servis') / 100);
    $diskon = $subtotal * ((float)config('hotel.biaya_layanan.diskon') / 100);
    
    $data['total_harga'] = $subtotal + $pajak + $servis - $diskon;
    
    return $data;
}
```

### Validasi Berdasarkan Konfigurasi

```php
// Di dalam form resource

TextInput::make('jenis_identitas')
    ->label('Jenis Identitas')
    ->required()
    ->rules([
        'string',
        function ($attribute, $value, $fail) {
            $validTypes = config('hotel.jenis_identitas');
            if (!in_array($value, $validTypes)) {
                $fail("Jenis identitas harus salah satu dari: " . implode(', ', $validTypes));
            }
        },
    ]),
```

## 8. Tips dan Trik

### Membuat Fungsi Helper untuk Konfigurasi

Untuk mempermudah penggunaan berulang, buat file helper (`app/Helpers/ConfigHelper.php`):

```php
<?php

namespace App\Helpers;

class ConfigHelper
{
    /**
     * Mengubah array sederhana menjadi array asosiatif untuk options
     */
    public static function arrayToOptions(string $configKey): array
    {
        $values = config($configKey, []);
        
        if (empty($values)) {
            return [];
        }
        
        // Jika sudah berupa array asosiatif
        if (array_keys($values) !== range(0, count($values) - 1)) {
            return $values;
        }
        
        // Jika berupa array sederhana
        return array_combine($values, $values);
    }
    
    /**
     * Mendapatkan nama status dari kode
     */
    public static function getStatusName(string $statusCode): string
    {
        return config("hotel.reservasi_status.{$statusCode}", $statusCode);
    }
    
    /**
     * Mendapatkan meja berdasarkan lokasi
     */
    public static function getMejaByLokasi(string $lokasi): array
    {
        $allMeja = config('hotel.meja', []);
        
        return array_filter($allMeja, function($value) use ($lokasi) {
            return $value === $lokasi;
        });
    }
    
    /**
     * Menghitung total dengan biaya layanan
     */
    public static function hitungTotalDenganLayanan(float $subtotal): array
    {
        $pajak = $subtotal * ((float)config('hotel.biaya_layanan.pajak', 0) / 100);
        $servis = $subtotal * ((float)config('hotel.biaya_layanan.servis', 0) / 100);
        $diskon = $subtotal * ((float)config('hotel.biaya_layanan.diskon', 0) / 100);
        
        $total = $subtotal + $pajak + $servis - $diskon;
        
        return [
            'subtotal' => $subtotal,
            'pajak' => $pajak,
            'servis' => $servis,
            'diskon' => $diskon,
            'total' => $total,
        ];
    }
}
```

Daftarkan helper ini di `composer.json`:

```json
{
    "autoload": {
        "psr-4": {
            "App\\": "app/",
            "Database\\Factories\\": "database/factories/",
            "Database\\Seeders\\": "database/seeders/"
        },
        "files": [
            "app/Helpers/ConfigHelper.php"
        ]
    }
}
```

Jalankan `composer dump-autoload` untuk mendaftarkan helper.

Cara menggunakan:

```php
// Di resource atau form

use App\Helpers\ConfigHelper;

Select::make('jenis_identitas')
    ->label('Jenis Identitas')
    ->options(ConfigHelper::arrayToOptions('hotel.jenis_identitas'))
    ->required(),
```

### Penggunaan Konfigurasi dalam Livewire Components

```php
<?php

namespace App\Aplikasi\Hotel\Livewire;

use Livewire\Component;
use App\Models\Reservasi;
use App\Models\Kamar;
use App\Helpers\ConfigHelper;
use Carbon\Carbon;

class KalkulatorHarga extends Component
{
    public $kamarId;
    public $checkIn;
    public $checkOut;
    public $jumlahTamu;
    
    public $subtotal = 0;
    public $pajak = 0;
    public $servis = 0;
    public $diskon = 0;
    public $total = 0;
    
    public function mount()
    {
        $this->checkIn = Carbon::today()->format('Y-m-d');
        $this->checkOut = Carbon::tomorrow()->format('Y-m-d');
        $this->jumlahTamu = 1;
    }
    
    public function updated($propertyName)
    {
        if (in_array($propertyName, ['kamarId', 'checkIn', 'checkOut', 'jumlahTamu'])) {
            $this->hitungHarga();
        }
    }
    
    public function hitungHarga()
    {
        if (empty($this->kamarId) || empty($this->checkIn) || empty($this->checkOut)) {
            return;
        }
        
        $kamar = Kamar::find($this->kamarId);
        
        if (!$kamar) {
            return;
        }
        
        $checkIn = Carbon::parse($this->checkIn);
        $checkOut = Carbon::parse($this->checkOut);
        $jumlahMalam = $checkOut->diffInDays($checkIn);
        
        if ($jumlahMalam <= 0) {
            $this->subtotal = 0;
            $this->pajak = 0;
            $this->servis = 0;
            $this->diskon = 0;
            $this->total = 0;
            return;
        }
        
        $this->subtotal = $kamar->harga_per_malam * $jumlahMalam;
        
        $hasil = ConfigHelper::hitungTotalDenganLayanan($this->subtotal);
        
        $this->pajak = $hasil['pajak'];
        $this->servis = $hasil['servis'];
        $this->diskon = $hasil['diskon'];
        $this->total = $hasil['total'];
    }
    
    public function render()
    {
        return view('hotel::livewire.kalkulator-harga', [
            'kamarList' => Kamar::where('status', 'tersedia')->pluck('nomor_kamar', 'id'),
            'pajak_persen' => config('hotel.biaya_layanan.pajak'),
            'servis_persen' => config('hotel.biaya_layanan.servis'),
            'diskon_persen' => config('hotel.biaya_layanan.diskon'),
        ]);
    }
}
```

## 9. Contoh Kasus Penggunaan

### Contoh 1: Reservasi Kamar Hotel

```php
// app/Aplikasi/Hotel/Resources/ReservasiResource.php

<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;
use App\Models\Reservasi;
use App\Helpers\ConfigHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ReservasiResource extends Resource
{
    protected static ?string $model = Reservasi::class;
    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    protected static ?string $navigationLabel = 'Reservasi';
    protected static ?string $navigationGroup = 'Reservasi';
    protected static ?string $slug = 'reservasi';
    
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Reservasi')
                    ->schema([
                        Forms\Components\TextInput::make('kode_reservasi')
                            ->label('Kode Reservasi')
                            ->default(fn() => 'RES-' . time())
                            ->disabled()
                            ->dehydrated()
                            ->required(),
                            
                        Forms\Components\Select::make('tamu_id')
                            ->label('Tamu')
                            ->relationship('tamu', 'nama')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('nama')
                                    ->label('Nama Lengkap')
                                    ->required(),
                                    
                                Forms\Components\TextInput::make('no_identitas')
                                    ->label('Nomor Identitas')
                                    ->required(),
                                    
                                Forms\Components\Select::make('jenis_identitas')
                                    ->label('Jenis Identitas')
                                    ->options(fn() => array_combine(
                                        config('hotel.jenis_identitas'),
                                        config('hotel.jenis_identitas')
                                    ))
                                    ->required(),
                                    
                                Forms\Components\TextInput::make('no_telepon')
                                    ->label('Nomor Telepon')
                                    ->tel()
                                    ->required(),
                                    
                                Forms\Components\Select::make('referensi')
                                    ->label('Referensi')
                                    ->options(fn() => array_combine(
                                        config('hotel.referensi'),
                                        config('hotel.referensi')
                                    )),
                            ])
                            ->required(),
                            
                        Forms\Components\Select::make('status')
                            ->label('Status Reservasi')
                            ->options(config('hotel.reservasi_status'))
                            ->default('BK')
                            ->required(),
                    ]),
                    
                Forms\Components\Section::make('Detail Kamar')
                    ->schema([
                        Forms\Components\Select::make('kamar_id')
                            ->label('Kamar')
                            ->relationship('kamar', 'nomor_kamar')
                            ->getOptionLabelFromRecordUsing(fn ($record) => 
                                "{$record->nomor_kamar} - {$record->tipe_kamar} ({$record->spesifikasi})"
                            )
                            ->searchable()
                            ->preload()
                            ->required(),
                            
                        Forms\Components\DatePicker::make('tanggal_check_in')
                            ->label('Tanggal Check In')
                            ->default(now())
                            ->required(),
                            
                        Forms\Components\DatePicker::make('tanggal_check_out')
                            ->label('Tanggal Check Out')
                            ->default(now()->addDay())
                            ->after('tanggal_check_in')
                            ->required(),
                            
                        Forms\Components\TextInput::make('jumlah_tamu')
                            ->label('Jumlah Tamu')
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->required(),
                    ])
                    ->columns(2),
                    
                Forms\Components\Section::make('Biaya')
                    ->schema([
                        Forms\Components\Placeholder::make('harga_info')
                            ->label('Informasi Biaya')
                            ->content(function (array $data) {
                                if (empty($data['kamar_id']) || empty($data['tanggal_check_in']) || empty($data['tanggal_check_out'])) {
                                    return 'Silahkan pilih kamar dan tanggal untuk melihat perhitungan biaya.';
                                }
                                
                                $kamar = \App\Models\Kamar::find($data['kamar_id']);
                                
                                if (!$kamar) {
                                    return 'Kamar tidak ditemukan.';
                                }
                                
                                $checkIn = Carbon::parse($data['tanggal_check_in']);
                                $checkOut = Carbon::parse($data['tanggal_check_out']);
                                $jumlahMalam = $checkOut->diffInDays($checkIn);
                                
                                if ($jumlahMalam <= 0) {
                                    return 'Tanggal check-out harus setelah tanggal check-in.';
                                }
                                
                                $subtotal = $kamar->harga_per_malam * $jumlahMalam;
                                
                                $hasil = ConfigHelper::hitungTotalDenganLayanan($subtotal);
                                
                                $pajak_persen = config('hotel.biaya_layanan.pajak');
                                $servis_persen = config('hotel.biaya_layanan.servis');
                                $diskon_persen = config('hotel.biaya_layanan.diskon');
                                
                                return "
                                    <div class='space-y-2'>
                                        <div class='flex justify-between'>
                                            <span>Harga kamar per malam:</span>
                                            <span>Rp " . number_format($kamar->harga_per_malam, 0, ',', '.') . "</span>
                                        </div>
                                        <div class='flex justify-between'>
                                            <span>Jumlah malam:</span>
                                            <span>{$jumlahMalam} malam</span>
                                        </div>
                                        <div class='flex justify-between'>
                                            <span>Subtotal:</span>
                                            <span>Rp " . number_format($subtotal, 0, ',', '.') . "</span>
                                        </div>
                                        <div class='flex justify-between'>
                                            <span>Pajak ({$pajak_persen}%):</span>
                                            <span>Rp " . number_format($hasil['pajak'], 0, ',', '.') . "</span>
                                        </div>
                                        <div class='flex justify-between'>
                                            <span>Biaya layanan ({$servis_persen}%):</span>
                                            <span>Rp " . number_format($hasil['servis'], 0, ',', '.') . "</span>
                                        </div>
                                        <div class='flex justify-between'>
                                            <span>Diskon ({$diskon_persen}%):</span>
                                            <span>Rp " . number_format($hasil['diskon'], 0, ',', '.') . "</span>
                                        </div>
                                        <div class='flex justify-between font-bold'>
                                            <span>Total:</span>
                                            <span>Rp " . number_format($hasil['total'], 0, ',', '.') . "</span>
                                        </div>
                                    </div>
                                ";
                            }),
                        
                        Forms\Components\Hidden::make('total_harga')
                            ->afterStateHydrated(function (Forms\Components\Hidden $component, $state, ?Reservasi $record) {
                                if ($record) {
                                    return;
                                }
                                
                                $livewire = $component->getContainer()->getLivewire();
                                $data = $livewire->data;
                                
                                if (empty($data['kamar_id']) || empty($data['tanggal_check_in']) || empty($data['tanggal_check_out'])) {
                                    return;
                                }
                                
                                $kamar = \App\Models\Kamar::find($data['kamar_id']);
                                
                                if (!$kamar) {
                                    return;
                                }
                                
                                $checkIn = Carbon::parse($data['tanggal_check_in']);
                                $checkOut = Carbon::parse($data['tanggal_check_out']);
                                $jumlahMalam = $checkOut->diffInDays($checkIn);
                                
                                if ($jumlahMalam <= 0) {
                                    return;
                                }
                                
                                $subtotal = $kamar->harga_per_malam * $jumlahMalam;
                                $hasil = ConfigHelper::hitungTotalDenganLayanan($subtotal);
                                
                                $component->state($hasil['total']);
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kode_reservasi')
                    ->label('Kode Reservasi')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('tamu.nama')
                    ->label('Nama Tamu')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('kamar.nomor_kamar')
                    ->label('Nomor Kamar'),
                    
                Tables\Columns\TextColumn::make('tanggal_check_in')
                    ->label('Check In')
                    ->date('d/m/Y'),
                    
                Tables\Columns\TextColumn::make('tanggal_check_out')
                    ->label('Check Out')
                    ->date('d/m/Y'),
                    
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => config('hotel.reservasi_status')[$state] ?? $state)
                    ->colors([
                        'warning' => fn ($state) => $state === 'BK',
                        'danger' => fn ($state) => $state === 'PD',
                        'success' => fn ($state) => $state === 'SCI',
                        'primary' => fn ($state) => $state === 'SCO',
                    ]),
                    
                Tables\Columns\TextColumn::make('total_harga')
                    ->label('Total')
                    ->money('IDR'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options(config('hotel.reservasi_status')),
                    
                Tables\Filters\Filter::make('tanggal_check_in')
                    ->form([
                        Forms\Components\DatePicker::make('check_in_from')
                            ->label('Check In Dari'),
                        Forms\Components\DatePicker::make('check_in_until')
                            ->label('Check In Sampai'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['check_in_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal_check_in', '>=', $date),
                            )
                            ->when(
                                $data['check_in_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal_check_in', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                
                Tables\Actions\Action::make('cetakInvoice')
                    ->label('Cetak Invoice')
                    ->icon('heroicon-o-printer')
                    ->url(fn (Reservasi $record) => route('reservasi.print-invoice', $record))
                    ->openUrlInNewTab(),
                    
                Tables\Actions\Action::make('ubahStatus')
                    ->label('Ubah Status')
                    ->icon('heroicon-o-arrow-path')
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Select::make('status')
                            ->label('Status Baru')
                            ->options(config('hotel.reservasi_status'))
                            ->required(),
                    ])
                    ->action(function (Reservasi $record, array $data): void {
                        $record->update(['status' => $data['status']]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    
    public static function getRelations(): array
    {
        return [
            ReservasiResource\RelationManagers\PembayaranRelationManager::class,
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReservasis::route('/'),
            'create' => Pages\CreateReservasi::route('/create'),
            'edit' => Pages\EditReservasi::route('/{record}/edit'),
        ];
    }    
}
```

### Contoh 2: Konfigurasi Sistem Hotel

```php
// app/Aplikasi/Hotel/Pages/KonfigurasiSistem.php

<?php

namespace App\Aplikasi\Hotel\Pages;

use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Grid;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;

class KonfigurasiSistem extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-8-tooth';
    protected static string $view = 'hotel::pages.konfigurasi-sistem';
    protected static ?string $title = 'Konfigurasi Sistem';
    protected static ?string $navigationLabel = 'Konfigurasi Sistem';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?int $navigationSort = 100;

    public ?array $data = [];
    protected $configPath;

    public function mount(): void
    {
        $this->configPath = config_path('hotel.php');
        
        // Coba ambil konfigurasi dari cache atau file
        $config = config('hotel', []);
        
        // Jika konfigurasi kosong, coba ambil dari file aplikasi.php langsung
        if (empty($config)) {
            $appConfigPath = app_path('Aplikasi/Hotel/config/aplikasi.php');
            if (File::exists($appConfigPath)) {
                $config = include $appConfigPath;
            }
        }
        
        $this->data = $config;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Konfigurasi Umum')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TagsInput::make('jenis_identitas')
                                    ->label('Jenis Identitas')
                                    ->placeholder('Tambahkan jenis identitas baru')
                                    ->helperText('Daftar jenis identitas yang dapat digunakan tamu'),

                                TagsInput::make('referensi')
                                    ->label('Referensi')
                                    ->placeholder('Tambahkan referensi baru')
                                    ->helperText('Daftar sumber referensi reservasi'),
                            ]),
                    ]),

                Section::make('Fasilitas & Spesifikasi')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TagsInput::make('fasilitas_kamar')
                                    ->label('Fasilitas Kamar')
                                    ->placeholder('Tambahkan fasilitas baru')
                                    ->helperText('Daftar fasilitas yang tersedia di kamar'),

                                TagsInput::make('spesifikasi_kamar')
                                    ->label('Spesifikasi Kamar')
                                    ->placeholder('Tambahkan spesifikasi baru')
                                    ->helperText('Daftar spesifikasi kamar yang tersedia'),
                            ]),
                    ]),

                Section::make('Status & Jadwal')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                KeyValue::make('reservasi_status')
                                    ->label('Status Reservasi')
                                    ->addButtonLabel('Tambah Status')
                                    ->keyLabel('Kode')
                                    ->valueLabel('Status')
                                    ->helperText('Kode dan deskripsi status reservasi'),

                                TagsInput::make('jadwal_shift')
                                    ->label('Jadwal Shift')
                                    ->placeholder('Tambahkan shift baru')
                                    ->helperText('Daftar jadwal shift kerja'),

                                TagsInput::make('pembayaran_status')
                                    ->label('Status Pembayaran')
                                    ->placeholder('Tambahkan status baru')
                                    ->helperText('Daftar status pembayaran'),
                            ]),
                    ]),

                Section::make('Biaya & Printer')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                KeyValue::make('biaya_layanan')
                                    ->label('Biaya Layanan (%)')
                                    ->addButtonLabel('Tambah Biaya')
                                    ->keyLabel('Jenis')
                                    ->valueLabel('Persentase')
                                    ->helperText('Pengaturan biaya layanan dalam persen'),

                                KeyValue::make('printer_kasir')
                                    ->label('Konfigurasi Printer Kasir')
                                    ->addButtonLabel('Tambah Konfigurasi')
                                    ->keyLabel('Kunci')
                                    ->valueLabel('Nilai')
                                    ->helperText('Pengaturan printer untuk struk'),
                            ]),
                    ]),
                
                Section::make('Restoran & Pesanan')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                KeyValue::make('meja')
                                    ->label('Daftar Meja')
                                    ->addButtonLabel('Tambah Meja Baru')
                                    ->keyLabel('Nama Meja')
                                    ->valueLabel('Lokasi')
                                    ->keyPlaceholder('Contoh: meja1')
                                    ->valuePlaceholder('dalam atau luar')
                                    ->helperText('Nama meja sebagai kunci dan lokasi (dalam/luar) sebagai nilai'),

                                TagsInput::make('jenis_pesanan')
                                    ->label('Jenis Pesanan')
                                    ->placeholder('Tambahkan jenis pesanan baru')
                                    ->helperText('Daftar jenis pesanan yang tersedia'),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('simpan')
                ->label('Simpan Konfigurasi')
                ->icon('heroicon-o-document-check')
                ->submit('simpan'),
        ];
    }

    public function simpan(): void
    {
        $this->validate();

        try {
            $config = var_export($this->data, true);
            $configContent = "<?php\n\nreturn {$config};\n";
            
            // Pastikan path config utama valid
            if (empty($this->configPath)) {
                $this->configPath = config_path('hotel.php');
            }
            
            // Buat direktori jika belum ada (untuk file aplikasi.php)
            $appConfigDir = app_path('Aplikasi/Hotel/config');
            if (!File::exists($appConfigDir)) {
                File::makeDirectory($appConfigDir, 0755, true);
            }
            
            // Perbarui file konfigurasi aplikasi.php
            $configFilePath = app_path('Aplikasi/Hotel/config/aplikasi.php');
            File::put($configFilePath, $configContent);
            
            // Pastikan direktori config utama ada
            $configDir = dirname($this->configPath);
            if (!File::exists($configDir)) {
                File::makeDirectory($configDir, 0755, true);
            }
            
            // Perbarui file konfigurasi utama hotel.php
            File::put($this->configPath, $configContent);
            
            // Bersihkan cache konfigurasi
            \Artisan::call('config:clear');
            
            Notification::make()
                ->title('Konfigurasi berhasil disimpan')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menyimpan konfigurasi')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
```

### Contoh 3: View untuk Cetak Invoice

```blade
{{-- app/Aplikasi/Hotel/views/reservasi/print-invoice.blade.php --}}
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Reservasi #{{ $reservasi->kode_reservasi }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        .invoice-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .invoice-details {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        .total {
            text-align: right;
            font-weight: bold;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-header">
        <h1>INVOICE</h1>
        <h2>{{ config('app.name') }}</h2>
    </div>
    
    <div class="invoice-details">
        <p><strong>No. Invoice:</strong> INV-{{ $reservasi->kode_reservasi }}</p>
        <p><strong>Tanggal:</strong> {{ $reservasi->created_at->format('d/m/Y') }}</p>
        <p><strong>Tamu:</strong> {{ $reservasi->tamu->nama }}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Deskripsi</th>
                <th>Tanggal Check In</th>
                <th>Tanggal Check Out</th>
                <th>Durasi</th>
                <th>Harga Per Malam</th>
                <th>Subtotal</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>{{ $reservasi->kamar->tipe_kamar }} - Kamar {{ $reservasi->kamar->nomor_kamar }}</td>
                <td>{{ \Carbon\Carbon::parse($reservasi->tanggal_check_in)->format('d/m/Y') }}</td>
                <td>{{ \Carbon\Carbon::parse($reservasi->tanggal_check_out)->format('d/m/Y') }}</td>
                <td>
                    {{ \Carbon\Carbon::parse($reservasi->tanggal_check_in)->diffInDays(\Carbon\Carbon::parse($reservasi->tanggal_check_out)) }}
                    malam
                </td>
                <td>Rp {{ number_format($reservasi->kamar->harga_per_malam, 0, ',', '.') }}</td>
                <td>Rp {{ number_format($reservasi->total_harga, 0, ',', '.') }}</td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="5" class="total">Pajak ({{ config('hotel.biaya_layanan.pajak') }}%)</td>
                <td>Rp {{ number_format($reservasi->total_harga * config('hotel.biaya_layanan.pajak') / 100, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="5" class="total">Biaya Layanan ({{ config('hotel.biaya_layanan.servis') }}%)</td>
                <td>Rp {{ number_format($reservasi->total_harga * config('hotel.biaya_layanan.servis') / 100, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="5" class="total">TOTAL</td>
                <td>Rp {{ number_format(
                    $reservasi->total_harga + 
                    ($reservasi->total_harga * config('hotel.biaya_layanan.pajak') / 100) + 
                    ($reservasi->total_harga * config('hotel.biaya_layanan.servis') / 100), 
                    0, ',', '.') }}</td>
            </tr>
        </tfoot>
    </table>
    
    <div class="no-print">
        <button onclick="window.print()">Cetak Invoice</button>
        <button onclick="window.close()">Tutup</button>
    </div>
</body>
</html>
```

## Kesimpulan

File konfigurasi `config/aplikasi.php` sangat berguna untuk menyimpan data yang sering berubah dan perlu diakses di berbagai bagian aplikasi. Dengan mengintegrasikannya ke dalam komponen Filament, Anda dapat membuat sistem manajemen hotel yang fleksibel dan mudah dikonfigurasi.

Saat menggunakan konfigurasi di Filament, ingatlah:

1. Gunakan `config()` helper untuk mengakses nilai konfigurasi
2. Buat helper functions untuk operasi yang sering digunakan
3. Manfaatkan konfigurasi dalam form, table, dan relation managers
4. Update nilai konfigurasi melalui halaman khusus

Dengan mengikuti panduan ini, Anda dapat membuat aplikasi hotel yang mudah disesuaikan tanpa perlu mengubah kode inti aplikasi.

---

*Terakhir diperbarui: Maret 2025*

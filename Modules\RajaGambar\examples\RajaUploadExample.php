<?php

namespace App\Filament\Resources\ProductResource\Pages;

use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\CreateRecord;
use Modules\RajaGambar\Forms\Components\RajaUpload;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Dasar')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Produk')
                            ->required()
                            ->maxLength(255),

                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),

                        Select::make('category_id')
                            ->label('Kategori')
                            ->relationship('category', 'name')
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Gambar Produk')
                    ->schema([
                        // Basic upload dengan auto resize dan optimize
                        RajaUpload::make('featured_image')
                            ->label('Gambar Utama')
                            ->helperText('Gambar akan otomatis diresize ke 800x600 dan dioptimasi')
                            ->autoResize(800, 600, 'crop')
                            ->autoOptimize(true)
                            ->outputDirectory('products/featured')
                            ->required(),

                        // Multiple upload dengan watermark
                        RajaUpload::make('gallery_images')
                            ->label('Galeri Produk')
                            ->helperText('Upload maksimal 8 gambar untuk galeri produk')
                            ->multiple()
                            ->maxFiles(8)
                            ->autoResize(600, 400, 'fit')
                            ->addWatermark(public_path('images/watermark.png'), 'bottom-right', 40, 15)
                            ->autoOptimize(true)
                            ->outputDirectory('products/gallery'),

                        // Upload dengan efek kustom
                        RajaUpload::make('thumbnail')
                            ->label('Thumbnail')
                            ->helperText('Thumbnail akan dibuat dengan border dan efek brightness')
                            ->autoResize(300, 300, 'crop')
                            ->applyEffects([
                                'brightness' => 10,
                                'contrast' => 5,
                                'border' => [
                                    'width' => 2,
                                    'type' => 'overlay',
                                    'color' => '#ffffff'
                                ]
                            ])
                            ->autoOptimize(true)
                            ->outputDirectory('products/thumbnails'),
                    ])
                    ->columns(1),

                Section::make('Banner & Promosi')
                    ->schema([
                        // Banner dengan resize khusus
                        RajaUpload::make('banner_image')
                            ->label('Banner Promosi')
                            ->helperText('Banner akan diresize ke 1200x400 dengan watermark logo')
                            ->autoResize(1200, 400, 'crop')
                            ->addWatermark(public_path('images/logo.png'), 'top-right', 60, 20)
                            ->applyEffects([
                                'brightness' => 5,
                                'contrast' => 8
                            ])
                            ->autoOptimize(true)
                            ->outputDirectory('banners'),

                        // Icon produk dengan efek khusus
                        RajaUpload::make('icon')
                            ->label('Icon Produk')
                            ->helperText('Icon akan dibuat square dengan background putih')
                            ->autoResize(128, 128, 'fit')
                            ->applyEffects([
                                'background' => '#ffffff',
                                'border' => [
                                    'width' => 1,
                                    'type' => 'overlay',
                                    'color' => '#e5e5e5'
                                ]
                            ])
                            ->autoOptimize(true)
                            ->outputDirectory('products/icons'),
                    ])
                    ->columns(2),

                Section::make('Gambar Khusus')
                    ->schema([
                        // Upload dengan banyak efek
                        RajaUpload::make('special_image')
                            ->label('Gambar Spesial')
                            ->helperText('Gambar dengan efek lengkap: colorize, gamma, dan orientasi')
                            ->autoResize(500, 500, 'crop')
                            ->applyEffects([
                                'gamma' => 1.2,
                                'colorize' => [
                                    'red' => 10,
                                    'green' => -5,
                                    'blue' => 15
                                ],
                                'orientation' => 0, // No rotation
                                'border' => [
                                    'width' => 5,
                                    'type' => 'overlay',
                                    'color' => '#333333'
                                ]
                            ])
                            ->addWatermark(public_path('images/special-watermark.png'), 'center', 30)
                            ->autoOptimize(true)
                            ->outputDirectory('products/special'),

                        // Upload untuk social media
                        RajaUpload::make('social_image')
                            ->label('Gambar Social Media')
                            ->helperText('Optimized untuk sharing di social media (1200x630)')
                            ->autoResize(1200, 630, 'crop')
                            ->applyEffects([
                                'brightness' => 15,
                                'contrast' => 10,
                                'background' => '#f8f9fa'
                            ])
                            ->addWatermark(public_path('images/social-watermark.png'), 'bottom-center', 50, 30)
                            ->autoOptimize(true)
                            ->outputDirectory('products/social'),
                    ])
                    ->columns(2),
            ]);
    }
}

// Contoh penggunaan dalam Resource lain
class HotelRoomResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Foto Kamar')
                    ->schema([
                        // Foto utama kamar
                        RajaUpload::make('main_photo')
                            ->label('Foto Utama Kamar')
                            ->autoResize(1024, 768, 'crop')
                            ->addWatermark(public_path('images/hotel-watermark.png'), 'bottom-right', 45)
                            ->autoOptimize(true)
                            ->outputDirectory('rooms/main')
                            ->required(),

                        // Galeri kamar
                        RajaUpload::make('room_gallery')
                            ->label('Galeri Kamar')
                            ->multiple()
                            ->maxFiles(12)
                            ->autoResize(800, 600, 'fit')
                            ->applyEffects([
                                'brightness' => 8,
                                'contrast' => 5
                            ])
                            ->addWatermark(public_path('images/hotel-watermark.png'), 'bottom-right', 35)
                            ->autoOptimize(true)
                            ->outputDirectory('rooms/gallery'),

                        // Floor plan
                        RajaUpload::make('floor_plan')
                            ->label('Denah Kamar')
                            ->autoResize(600, 400, 'fit')
                            ->applyEffects([
                                'background' => '#ffffff',
                                'border' => [
                                    'width' => 2,
                                    'type' => 'overlay',
                                    'color' => '#cccccc'
                                ]
                            ])
                            ->autoOptimize(true)
                            ->outputDirectory('rooms/floorplans'),
                    ])
            ]);
    }
}

// Contoh untuk Event/Wedding
class EventResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Dokumentasi Event')
                    ->schema([
                        // Cover event
                        RajaUpload::make('event_cover')
                            ->label('Cover Event')
                            ->autoResize(1200, 800, 'crop')
                            ->applyEffects([
                                'brightness' => 12,
                                'contrast' => 8,
                                'gamma' => 1.1
                            ])
                            ->addWatermark(public_path('images/event-watermark.png'), 'top-left', 55, 25)
                            ->autoOptimize(true)
                            ->outputDirectory('events/covers')
                            ->required(),

                        // Portfolio event
                        RajaUpload::make('portfolio_images')
                            ->label('Portfolio Event')
                            ->multiple()
                            ->maxFiles(20)
                            ->autoResize(900, 600, 'fit')
                            ->applyEffects([
                                'brightness' => 5,
                                'contrast' => 3,
                                'colorize' => [
                                    'red' => 5,
                                    'green' => 0,
                                    'blue' => -3
                                ]
                            ])
                            ->addWatermark(public_path('images/portfolio-watermark.png'), 'bottom-center', 40)
                            ->autoOptimize(true)
                            ->outputDirectory('events/portfolio'),
                    ])
            ]);
    }
}

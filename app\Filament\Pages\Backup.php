<?php

namespace App\Filament\Pages;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Shu<PERSON>roRoy\FilamentSpatieLaravelBackup\Pages\Backups as BaseBackups;

class Backup extends BaseBackups
{
    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';
    protected static ?string $navigationGroup = 'System';
    protected static ?string $navigationLabel = 'Backup';
    // protected static ?string $navigationParentItem = 'System';
    protected static ?string $slug = 'system/backup';
    protected static bool $shouldRegisterNavigation = true;

    // Properti untuk form backup per tabel
    public $data = [
        'table' => null,
        'jenis' => null,
        'nama' => null,
    ];
    public $tables = [];
    public $jenisOptions = [];

    public static function getNavigationGroup(): ?string
    {
        return 'System';
    }

    public function mount()
    {
        // Inisialisasi data
        $this->loadTables();
    }

    public function loadTables()
    {
        // Dapatkan semua tabel dari database
        $tables = DB::select('SHOW TABLES');
        $dbName = config('database.connections.mysql.database');
        $tableKey = 'Tables_in_' . $dbName;

        $this->tables = collect($tables)->pluck($tableKey)->toArray();
    }

    public function loadJenisOptions()
    {
        $this->jenisOptions = [];

        if ($this->data['table'] && Schema::hasColumn($this->data['table'], 'jenis')) {
            $this->jenisOptions = DB::table($this->data['table'])
                ->select('jenis')
                ->distinct()
                ->whereNotNull('jenis')
                ->pluck('jenis', 'jenis')
                ->toArray();
        }
    }

    public function updatedData()
    {
        if (isset($this->data['table']) && $this->data['table'] !== null) {
            $this->loadJenisOptions();
            $this->data['jenis'] = null;
        }
    }

    public function backupTable()
    {
        try {
            // Validasi data
            if (empty($this->data['table'])) {
                $this->dispatch('log-message', 'Error: Tabel harus dipilih', 'error');
                Notification::make()
                    ->title('Gagal membuat backup tabel')
                    ->body('Tabel harus dipilih')
                    ->danger()
                    ->send();
                return;
            }

            // Kirim log ke terminal
            $this->dispatch('log-message', 'Memulai backup tabel: ' . $this->data['table']);
            if (!empty($this->data['jenis'])) {
                $this->dispatch('log-message', 'Filter jenis: ' . $this->data['jenis']);
            }
            if (!empty($this->data['nama'])) {
                $this->dispatch('log-message', 'Nama backup: ' . $this->data['nama']);
            }

            $output = '';
            $exitCode = Artisan::call('backup:table', [
                'table' => $this->data['table'],
                '--jenis' => $this->data['jenis'],
                '--nama' => $this->data['nama'],
            ], $output);

            // Kirim output ke terminal
            $outputLines = explode("\n", $output);
            foreach ($outputLines as $line) {
                if (trim($line)) {
                    $this->dispatch('log-message', trim($line));
                }
            }

            if ($exitCode === 0) {
                $this->dispatch('log-message', 'Backup tabel berhasil dibuat', 'success');

                Notification::make()
                    ->title('Backup tabel berhasil dibuat')
                    ->success()
                    ->send();
            } else {
                $this->dispatch('log-message', 'Proses backup tabel gagal dengan kode: ' . $exitCode, 'error');

                throw new \Exception('Proses backup tabel gagal dengan kode: ' . $exitCode . '. Output: ' . $output);
            }
        } catch (\Exception $e) {
            $this->dispatch('log-message', 'Error: ' . $e->getMessage(), 'error');

            Notification::make()
                ->title('Gagal membuat backup tabel')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function getHeading(): string
    {
        return 'Application Backups';
    }



    protected static string $view = 'pages.backup';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        Select::make('table')
                            ->label('Pilih Tabel')
                            ->options(function () {
                                return array_combine($this->tables, $this->tables);
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function () {
                                $this->loadJenisOptions();
                            }),

                        Select::make('jenis')
                            ->label('Filter Jenis (Opsional)')
                            ->options(function () {
                                return $this->jenisOptions;
                            })
                            ->searchable()
                            ->preload()
                            ->visible(function () {
                                return !empty($this->jenisOptions);
                            }),

                        TextInput::make('nama')
                            ->label('Nama Backup (Opsional)')
                            ->placeholder('Nama untuk file backup')
                    ])
                    ->columns(3)
            ])
            ->statePath('data');
    }

    // Route dan handler download dinonaktifkan sementara
    /*
    public static function getRoutes(): \Closure
    {
        return function () {
            Route::get('download', static::downloadBackupRoute())
                ->name(static::getSlug() . '.download');
        };
    }

    protected static function downloadBackupRoute(): \Closure
    {
        return function () {
            $disk = request('disk');
            $path = request('path');

            if (!$disk || !$path) {
                abort(404, 'File tidak ditemukan');
            }

            if (!Storage::disk($disk)->exists($path)) {
                abort(404, 'File tidak ditemukan');
            }

            $filePath = Storage::disk($disk)->path($path);
            return response()->download($filePath);
        };
    }
    */
}
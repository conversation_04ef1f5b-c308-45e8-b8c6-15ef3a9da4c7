<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Pages;

use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Models\MetodePembayaran;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Resources\ReservasiResource;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Resources\Pages\Page;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\HtmlString;

class InvoiceHotel extends Page
{
   protected static string $resource = ReservasiResource::class;
   public $metodePembayaran;

   protected static string $view = 'hotel::pages.invoice-hotel';

   public $record;

   //  ReservasiResource::getUrl('invoice', ['record' => $this->record])
   public function mount(int|string $record): void
   {
      $this->record = Reservasi::with(['tamu', 'kamar', 'transaksi', 'pembayaran', 'kamar.tipe'])
         ->find($record);

      // Jika record tidak ditemukan, tampilkan 404
      if (! $this->record) {
         abort(404, "Reservasi tidak ditemukan");
      }

      $this->metodePembayaran = MetodePembayaran::where('print', true)->get();
   }

   public function cetakInvoice()
   {
      // Logika untuk mencetak invoice
      // Bisa menggunakan paket seperti barryvdh/laravel-dompdf atau laravel-snappy
      $this->dispatch('cetak-invoice');
   }



   protected function getHeaderActions(): array
   {
      return [
         Action::make('cetak')
            ->label('Cetak Invoice')
            ->icon('heroicon-o-printer')
            ->action(fn () => $this->cetakInvoice()),
        

         Action::make('kembali')
            ->label('Kembali')
            ->icon('heroicon-o-arrow-left')
            ->url(fn () => ReservasiResource::getUrl('view', ['record' => $this->record]))
         ,
      ];
   }

  

   public function infolist(Infolist $infolist): Infolist
   {
      return $infolist
         ->schema([
            Section::make()
               ->schema([
                  Split::make([
                     Group::make([
                        TextEntry::make('no_invoice')
                           ->label('No. Invoice')
                           ->weight(FontWeight::Bold)
                           ->size(TextEntry\TextEntrySize::Large),

                        TextEntry::make('created_at')
                           ->label('Tanggal Invoice')
                           ->date('d F Y'),

                        TextEntry::make('status_reservasi')
                           ->label('Status')
                           ->formatStateUsing(fn ($record) => $record->statusReservasi())
                           ->badge()
                           ->color(fn (string $state): string => match ($state) {
                              'SCI' => 'success',
                              'SCO' => 'danger',
                              'BK' => 'primary',
                              default => 'gray',
                           }),
                     ])->grow(),

                     // Logo hotel (jika ada)
                     // ImageEntry::make('logo')
                     //    ->state(fn() => asset('images/logo.png'))
                     //    ->height(100)
                     //    ->hiddenLabel(),
                  ])->from('md'),
               ]),

            Grid::make(2)
               ->schema([
                  Section::make('Informasi Tamu')
                     ->icon('heroicon-o-user')
                     ->schema([
                        TextEntry::make('tamu.nama')
                           ->label('Nama Tamu'),
                        TextEntry::make('tamu.telpon')
                           ->label('No. Telepon'),
                        TextEntry::make('tamu.email')
                           ->label('Email')
                           ->visible(fn ($record) => ! empty($record->tamu->email)),
                     ]),

                  Section::make('Informasi Reservasi')
                     ->icon('heroicon-o-calendar')
                     ->schema([
                        TextEntry::make('check_in')
                           ->label('Check In')
                           ->dateTime('d F Y H:i'),
                        TextEntry::make('check_out')
                           ->label('Check Out')
                           ->dateTime('d F Y H:i'),
                        TextEntry::make('durasi')
                           ->state(function ($record) {
                              $checkIn = Carbon::parse($record->check_in);
                              $checkOut = Carbon::parse($record->check_out);
                              return $checkIn->diffInDays($checkOut).' malam';
                           })
                           ->label('Durasi'),
                        TextEntry::make('kamar.nama')
                           ->label('Kamar')
                           ->prefix(fn ($record) => $record->kamar->tipe->nama.': '),
                     ]),
               ]),

            Section::make('Detail Transaksi')
               ->icon('heroicon-o-currency-dollar')
               ->schema([
                  RepeatableEntry::make('transaksi')
                     ->label('Item Transaksi')
                     ->schema([
                        TextEntry::make('nama_item')
                           ->label('Item'),
                        TextEntry::make('jumlah')
                           ->label('Jumlah'),
                        TextEntry::make('harga')
                           ->label('Harga')
                           ->money('IDR'),
                        TextEntry::make('subtotal')
                           ->state(fn ($record) => $record->jumlah * $record->harga)
                           ->label('Subtotal')
                           ->money('IDR'),
                     ])
                     ->columns(4),

                  Grid::make(3)
                     ->schema([
                        TextEntry::make('totalHargaBersih')
                           ->label('Subtotal')
                           ->state(fn ($record) => $record->totalHargaBersih())
                           ->money('IDR'),

                        TextEntry::make('diskon')
                           ->label('Diskon')
                           ->suffix('%')
                           ->visible(fn ($record) => $record->diskon > 0),

                        TextEntry::make('pajak')
                           ->label('Pajak')
                           ->suffix('%')
                           ->visible(fn ($record) => $record->pajak > 0),

                        TextEntry::make('servis')
                           ->label('Biaya Layanan')
                           ->suffix('%')
                           ->visible(fn ($record) => $record->servis > 0),
                     ]),

                  TextEntry::make('totalHarga')
                     ->label('Total')
                     ->state(fn ($record) => $record->totalHarga())
                     ->size(TextEntry\TextEntrySize::Large)
                     ->weight(FontWeight::Bold)
                     ->money('IDR'),
               ]),

            Section::make('Informasi Pembayaran')
               ->icon('heroicon-o-banknotes')
               ->schema([
                  RepeatableEntry::make('pembayaran')
                     ->label('Riwayat Pembayaran')
                     ->schema([
                        TextEntry::make('metodePembayaran.nama')
                           ->label('Metode Pembayaran'),
                        TextEntry::make('jumlah')
                           ->label('Jumlah')
                           ->money('IDR'),
                        TextEntry::make('created_at')
                           ->label('Tanggal')
                           ->dateTime('d F Y H:i'),
                        TextEntry::make('status')
                           ->label('Status')
                           ->badge()
                           ->color(fn (string $state): string => match ($state) {
                              'berhasil' => 'success',
                              'pending' => 'warning',
                              'gagal' => 'danger',
                              default => 'gray',
                           }),
                     ])
                     ->columns(4),

                  Grid::make(2)
                     ->schema([
                        TextEntry::make('hitungTotalPembayaran')
                           ->label('Total Dibayar')
                           ->state(fn ($record) => $record->hitungTotalPembayaran())
                           ->money('IDR'),

                        TextEntry::make('hitungSisaTagihan')
                           ->label('Sisa Tagihan')
                           ->state(fn ($record) => $record->hitungSisaTagihan())
                           ->money('IDR')
                           ->color(fn ($state) => $state > 0 ? 'danger' : 'success'),
                     ]),
               ]),

            Section::make('Keterangan')
               ->icon('heroicon-o-information-circle')
               ->schema([
                  TextEntry::make('ket')
                     ->label('Catatan')
                     ->default('Tidak ada catatan'),
               ])
               ->hidden(fn ($record) => empty($record->ket)),
         ]);
   }
}
<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;


use Modules\RajaCms\Filament\Resources\CmsResource;
use Filament\Resources\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use AbdelhamidErrahmouni\FilamentMonacoEditor\MonacoEditor;

use Filament\Forms\Concerns\InteractsWithForms;

class TemaEditor extends Page
{
    use InteractsWithForms;

    protected static string $resource = CmsResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-code-bracket';
    protected static ?string $navigationLabel = 'Editor Tema';
    protected static ?string $title = 'Editor Tema';
    protected static ?int $navigationSort = 4;
    // protected static ?string $navigationGroup = 'Konten';
    protected static string $view = 'filament.pages.tema-editor';
    protected static bool $shouldRegisterNavigation = false;

    public ?string $selectedFile = null;
    public ?string $fileContent = '';
    public ?string $temaAktif = null;
    public array $bladeFiles = [];
    public ?string $page_layout = '';
    public ?string $fileExtension = null;
    public ?string $editorType = null; // 'monaco', 'grapesjs'
    public array $supportedExtensions = [
        'php' => 'monaco',
        'js' => 'monaco',
        'blade.php' => 'grapesjs',
        'html' => 'grapesjs',
        'css' => 'monaco',
        'json' => 'monaco',
        'txt' => 'monaco',
        'md' => 'monaco'
    ];

    public function mount(): void
    {
        $this->temaAktif = Config::get('tema.aktif', 'default');
        $this->loadBladeFiles();

        // Cek apakah ini adalah permintaan AJAX untuk menyimpan file
        if (request()->ajax() && request()->isMethod('post') && request()->input('action') === 'save-file') {
            $this->handleAjaxSave();
            return;
        }

        // Cek apakah ada parameter namafile
        $namaFile = request()->query('namafile');

        if ($namaFile) {
            $temaPath = public_path('tema/' . $this->temaAktif);

            // Cek parameter type dan path untuk file non-blade
            $fileType = request()->query('type');
            $customPath = request()->query('path');

            if ($customPath) {
                // Jika ada path custom, cek apakah absolute atau relative
                if (str_starts_with($customPath, '/') || str_contains($customPath, ':')) {
                    // Path absolute
                    $filePath = $customPath;
                } else {
                    // Path relative, gabungkan dengan tema path
                    $filePath = $temaPath . '/' . $customPath;
                }
            } elseif ($fileType === 'php') {
                // Untuk file PHP biasa
                $filePath = $temaPath . '/' . $namaFile . '.php';
            } elseif ($fileType) {
                // Untuk file dengan ekstensi lain
                $filePath = $temaPath . '/' . $namaFile . '.' . $fileType;
            } else {
                // Default untuk file blade
                $filePath = $temaPath . '/' . $namaFile . '.blade.php';
            }

            // Log untuk debugging
            Log::info('TemaEditor - Mencari file', [
                'namaFile' => $namaFile,
                'fileType' => $fileType,
                'customPath' => $customPath,
                'temaPath' => $temaPath,
                'finalFilePath' => $filePath,
                'fileExists' => File::exists($filePath)
            ]);

            // Cek apakah file ada
            if (File::exists($filePath)) {
                $this->selectedFile = $filePath;
                $this->detectFileExtension($filePath);
                $this->determineEditorType();
                $this->loadFileContent($filePath);
                $this->page_layout = $this->fileContent;
            } else {
                Notification::make()
                    ->title('File tidak ditemukan')
                    ->body('Path: ' . $filePath . ' tidak ditemukan')
                    ->danger()
                    ->send();
            }
        }

        $this->form->fill([
            'page_layout' => $this->page_layout,
        ]);
    }

    /**
     * Menangani permintaan AJAX untuk menyimpan file
     */
    protected function handleAjaxSave()
    {
        // Ambil konten dari permintaan
        $content = request()->input('content');

        // Cek apakah ada parameter namafile
        $namaFile = request()->query('namafile');

        if (!$namaFile) {
            return response()->json([
                'success' => false,
                'message' => 'Nama file tidak ditemukan'
            ]);
        }

        $temaPath = public_path('tema/' . $this->temaAktif);

        // Cek parameter type dan path untuk file non-blade
        $fileType = request()->query('type');
        $customPath = request()->query('path');

        if ($customPath) {
            // Jika ada path custom, cek apakah absolute atau relative
            if (str_starts_with($customPath, '/') || str_contains($customPath, ':')) {
                // Path absolute
                $filePath = $customPath;
            } else {
                // Path relative, gabungkan dengan tema path
                $filePath = $temaPath . '/' . $customPath;
            }
        } elseif ($fileType === 'php') {
            // Untuk file PHP biasa
            $filePath = $temaPath . '/' . $namaFile . '.php';
        } elseif ($fileType) {
            // Untuk file dengan ekstensi lain
            $filePath = $temaPath . '/' . $namaFile . '.' . $fileType;
        } else {
            // Default untuk file blade
            $filePath = $temaPath . '/' . $namaFile . '.blade.php';
        }

        // Cek apakah file ada
        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'File ' . $namaFile . ' tidak ditemukan di tema ' . $this->temaAktif
            ]);
        }

        // Pastikan konten tidak kosong
        if (empty($content)) {
            return response()->json([
                'success' => false,
                'message' => 'Konten file kosong'
            ]);
        }

        try {
            // Simpan konten ke file
            File::put($filePath, $content);

            // Log untuk debugging
            Log::info('File berhasil disimpan via AJAX', [
                'filePath' => $filePath,
                'contentLength' => strlen($content)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File berhasil disimpan: ' . basename($filePath)
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Error menyimpan file via AJAX', [
                'filePath' => $filePath,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan file: ' . $e->getMessage()
            ]);
        }
    }

    protected function loadBladeFiles(): void
    {
        $this->bladeFiles = [];
        $temaPath = public_path('tema/' . $this->temaAktif);

        if (!File::exists($temaPath)) {
            return;
        }

        $allFiles = $this->getAllFiles($temaPath);

        foreach ($allFiles as $file) {
            if (str_ends_with($file, '.blade.php')) {
                $relativePath = str_replace($temaPath . '/', '', $file);
                $this->bladeFiles[] = [
                    'path' => $file,
                    'name' => $relativePath,
                    'relativePath' => $relativePath,
                ];
            }
        }
    }

    protected function getAllFiles($dir): array
    {
        $files = [];

        if (!is_dir($dir)) {
            return $files;
        }

        $items = scandir($dir);

        foreach ($items as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            $path = $dir . DIRECTORY_SEPARATOR . $item;

            if (is_dir($path)) {
                $files = array_merge($files, $this->getAllFiles($path));
            } else {
                $files[] = $path;
            }
        }

        return $files;
    }

    /**
     * Mendeteksi ekstensi file
     */
    protected function detectFileExtension(string $filePath): void
    {
        $fileName = basename($filePath);

        // Cek untuk file .blade.php terlebih dahulu
        if (str_ends_with(strtolower($fileName), '.blade.php')) {
            $this->fileExtension = 'blade.php';
        } else {
            // Ambil ekstensi biasa
            $this->fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        }
    }

    /**
     * Menentukan tipe editor berdasarkan ekstensi file
     */
    protected function determineEditorType(): void
    {
        if ($this->fileExtension && isset($this->supportedExtensions[$this->fileExtension])) {
            $this->editorType = $this->supportedExtensions[$this->fileExtension];
        } else {
            // Default ke monaco untuk file yang tidak dikenal
            $this->editorType = 'monaco';
        }
    }

    /**
     * Mendapatkan bahasa Monaco berdasarkan ekstensi file
     */
    protected function getMonacoLanguage(): string
    {
        $languageMap = [
            'php' => 'php',
            'js' => 'javascript',
            'css' => 'css',
            'json' => 'json',
            'html' => 'html',
            'txt' => 'plaintext',
            'md' => 'markdown',
            'blade.php' => 'html' // Fallback jika blade menggunakan monaco
        ];

        return $languageMap[$this->fileExtension] ?? 'plaintext';
    }

    /**
     * Form schema untuk editor
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Editor')
                    ->schema([
                        $this->getEditorField()
                    ])
                    ->visible(fn() => $this->selectedFile !== null)
            ]);
    }

    /**
     * Mendapatkan field editor yang sesuai
     */
    protected function getEditorField()
    {
        if ($this->editorType === 'monaco') {
            return MonacoEditor::make('page_layout')
                ->label('Konten File')
                ->language($this->getMonacoLanguage())
                ->fontSize('14px')
                ->lineNumbersMinChars(3)
                ->automaticLayout(true)
                ->showPlaceholder(true)
                ->placeholderText('Masukkan kode di sini...')
                ->showLoader(true)
                ->enablePreview(false)
                ->showFullScreenToggle(true)
                ->columnSpanFull();
        } else {
            // Fallback ke textarea untuk GrapesJS (akan dihandle di view)
            return Textarea::make('page_layout')
                ->label('Konten File')
                ->rows(20)
                ->columnSpanFull()
                ->extraAttributes(['style' => 'display: none;']); // Hidden karena akan digunakan GrapesJS
        }
    }

    protected function loadFileContent(string $filePath): void
    {
        if (File::exists($filePath)) {
            try {
                $this->fileContent = File::get($filePath);

                // Log untuk debugging
                \Illuminate\Support\Facades\Log::info('File content loaded', [
                    'filePath' => $filePath,
                    'contentLength' => strlen($this->fileContent),
                    'contentPreview' => substr($this->fileContent, 0, 100)
                ]);
            } catch (\Exception $e) {
                // Log error
                \Illuminate\Support\Facades\Log::error('Error loading file content', [
                    'filePath' => $filePath,
                    'error' => $e->getMessage()
                ]);

                Notification::make()
                    ->title('Error membaca file')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        } else {
            // Log file not found
            \Illuminate\Support\Facades\Log::warning('File not found', [
                'filePath' => $filePath
            ]);

            Notification::make()
                ->title('File tidak ditemukan')
                ->body('Path: ' . $filePath)
                ->danger()
                ->send();
        }
    }

    /**
     * Fungsi untuk menyimpan konten file tema
     */
    public function save()
    {
        // Pastikan file yang dipilih ada
        if (!$this->selectedFile || !File::exists($this->selectedFile)) {
            Notification::make()
                ->title('File tidak ditemukan')
                ->body('File yang dipilih tidak ditemukan atau tidak valid')
                ->danger()
                ->send();
            return;
        }

        // Validasi form jika menggunakan Monaco Editor
        if ($this->editorType === 'monaco') {
            $this->form->getState();
        }

        // Ambil nilai dari textarea page_layout
        $content = $this->page_layout;

        // Log konten untuk debugging
        Log::debug('Konten yang akan disimpan:', [
            'filePath' => $this->selectedFile,
            'editorType' => $this->editorType,
            'panjang' => strlen($content),
            'preview' => substr($content, 0, 100)
        ]);

        // Pastikan konten tidak kosong
        if (!empty($content)) {
            try {
                // Simpan konten ke file
                File::put($this->selectedFile, $content);

                // Tampilkan notifikasi sukses
                Notification::make()
                    ->title('File berhasil disimpan')
                    ->body('File: ' . basename($this->selectedFile) . ' (' . $this->editorType . ')')
                    ->success()
                    ->send();
            } catch (\Exception $e) {
                // Log error
                Log::error('Error menyimpan file', [
                    'filePath' => $this->selectedFile,
                    'editorType' => $this->editorType,
                    'error' => $e->getMessage()
                ]);

                // Tampilkan notifikasi error
                Notification::make()
                    ->title('Gagal menyimpan file')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        } else {
            // Tampilkan notifikasi error jika konten kosong
            Notification::make()
                ->title('Gagal menyimpan file')
                ->body('Konten file kosong')
                ->danger()
                ->send();
        }
    }

    /**
     * Fungsi untuk me-refresh daftar file
     */
    public function refreshFiles()
    {
        $this->loadBladeFiles();

        // Reload file yang sedang dibuka jika ada
        if ($this->selectedFile && File::exists($this->selectedFile)) {
            $this->loadFileContent($this->selectedFile);
            $this->page_layout = $this->fileContent;
        }

        Notification::make()
            ->title('Daftar file diperbarui')
            ->success()
            ->send();
    }

    /**
     * Mengubah tipe editor
     */
    public function switchEditor(string $editorType)
    {
        if (in_array($editorType, ['monaco', 'grapesjs'])) {
            $this->editorType = $editorType;

            // Refresh form untuk menggunakan editor yang baru
            $this->form->fill([
                'page_layout' => $this->page_layout,
            ]);

            // Dispatch event ke frontend
            $this->dispatch('editor-switched', editorType: $editorType);

            Notification::make()
                ->title('Editor berhasil diubah')
                ->body('Sekarang menggunakan ' . ($editorType === 'monaco' ? 'Monaco Editor' : 'GrapesJS'))
                ->success()
                ->send();
        }
    }

    /**
     * Tampilkan notifikasi sukses
     */
    public function showSuccessNotification(string $message)
    {
        Notification::make()
            ->title('Berhasil')
            ->body($message)
            ->success()
            ->send();
    }

    /**
     * Tampilkan notifikasi error
     */
    public function showErrorNotification(string $message)
    {
        Notification::make()
            ->title('Error')
            ->body($message)
            ->danger()
            ->send();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save')
                ->label('Simpan')
                ->color('success')
                ->action(function () {
                    // Jika ini adalah permintaan AJAX, gunakan metode handleAjaxSave
                    if (request()->ajax()) {
                        return $this->handleAjaxSave();
                    }

                    // Jika bukan permintaan AJAX, gunakan metode save biasa
                    return $this->save();
                })
                ->disabled(fn() => $this->selectedFile === null)
                ->extraAttributes([
                    'id' => 'header-save-button',
                    'onclick' => "document.dispatchEvent(new CustomEvent('tema-editor-save'))"
                ]),

            Action::make('switch_monaco')
                ->label('Monaco Editor')
                ->color('info')
                ->icon('heroicon-o-code-bracket')
                ->action(fn() => $this->switchEditor('monaco'))
                ->disabled(fn() => $this->selectedFile === null || $this->editorType === 'monaco')
                ->visible(fn() => $this->selectedFile !== null),

            Action::make('switch_grapesjs')
                ->label('GrapesJS')
                ->color('warning')
                ->icon('heroicon-o-paint-brush')
                ->action(fn() => $this->switchEditor('grapesjs'))
                ->disabled(fn() => $this->selectedFile === null || $this->editorType === 'grapesjs')
                ->visible(fn() => $this->selectedFile !== null),

            Action::make('refresh')
                ->label('Refresh')
                ->color('gray')
                ->action(fn() => $this->refreshFiles()),
        ];
    }
}

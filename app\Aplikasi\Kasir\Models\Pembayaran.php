<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperPembayaran
 */
class Pembayaran extends Model
{
  use HasFactory;
  protected $table = 'pembayaran';
  protected $fillable = [
    'nama',
    'penjualan_id',
    'reservasi_id',
    'metode_pembayaran_id',
    'jumlah',
    'bukti',
    'status',
    'pengirim',
    'tujuan',
    'ket',
    'jenis'
  ];

  protected $casts = [


    'pengirim' => 'array',

  ];

  protected static function booted()
  {


    static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
      $builder->where('jenis', 'KASIR');
    });


    static::creating(function ($model) {
      $model->jenis = 'KASIR';
    });

    static::updating(function ($model) {
      $model->jenis = 'KASIR';
    });
  }


  public function penjualan(): BelongsTo
  {
    return $this->belongsTo(Penjualan::class);
  }

  public function metodePembayaran(): BelongsTo
  {
    return $this->belongsTo(MetodePembayaran::class);
  }


  public function getPengirimJsonAttribute()
  {

    // cara pakai $payment->pengirim_json 

    // Jika pengirim null
    if (! $this->pengirim) {
      return '-';
    }

    // Jika sudah berbentuk array
    if (is_array($this->pengirim)) {
      $data = $this->pengirim;
    }
    // Jika berbentuk string JSON
    else {
      $data = json_decode($this->pengirim, true);
    }

    // Jika berhasil parsing
    if (is_array($data) && isset($data['nama_pengirim'])) {
      return $data['nama_pengirim'];
    }

    return '-';
  }


}

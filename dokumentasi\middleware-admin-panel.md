# EnsureHasAdminPanelPermission Middleware

Middleware ini berfungsi untuk memastikan bahwa hanya user yang memiliki izin yang tepat yang dapat mengakses panel admin FilamentPHP.

## Lokasi File
- **Middleware**: `app/Http/Middleware/EnsureHasAdminPanelPermission.php`
- **Seeder**: `database/seeders/AdminPanelPermissionSeeder.php`

## Cara Kerja

### 1. Flow Keamanan
```
Request → Auth Middleware → EnsureHasAdminPanelPermission → FilamentPHP Panel
```

### 2. Logika Permission
1. **User tidak login** → Lanjutkan ke Auth middleware (akan redirect ke login)
2. **User memiliki role `super_admin`** → ✅ Akses diberikan
3. **User memiliki permission `access_admin_panel`** → ✅ Akses diberikan  
4. **User tidak memiliki izin** → ❌ 403 Forbidden

### 3. Error Handling
- <PERSON><PERSON> terjadi error saat cek permission → Log warning dan lanjutkan
- Fallback untuk config yang tidak tersedia
- Robust error handling untuk mencegah aplikasi crash

## Konfigurasi

### Permission yang Diperlukan
- **Permission**: `access_admin_panel`
- **Roles**: `super_admin`, `panel_user`

### Filament Shield Config
```php
// config/filament-shield.php
'super_admin' => [
    'enabled' => true,
    'name' => 'super_admin',
],

'panel_user' => [
    'enabled' => true,
    'name' => 'panel_user',
],
```

## Setup & Installation

### 1. Jalankan Seeder
```bash
php artisan db:seed --class=AdminPanelPermissionSeeder
```

### 2. Assign Role ke User
```php
// Assign super admin role
$user = User::find(1);
$user->assignRole('super_admin');

// Atau assign permission langsung
$user->givePermissionTo('access_admin_panel');
```

### 3. Registrasi Middleware
Middleware ini biasanya sudah terdaftar di FilamentPHP panel configuration.

## Penggunaan

### Dalam Panel Provider
```php
// app/Providers/Filament/AdminPanelProvider.php
public function panel(Panel $panel): Panel
{
    return $panel
        ->middleware([
            // ... middleware lain
            \App\Http\Middleware\EnsureHasAdminPanelPermission::class,
        ]);
}
```

### Manual Check dalam Controller
```php
// Cek permission secara manual
if (!auth()->user()->hasPermissionTo('access_admin_panel')) {
    abort(403, 'Tidak memiliki izin akses panel admin');
}
```

## Troubleshooting

### Error: "Class not found"
```bash
composer dump-autoload
php artisan config:clear
```

### Error: "Permission not found"
```bash
php artisan db:seed --class=AdminPanelPermissionSeeder
```

### Error: "Role not found"
Pastikan role sudah dibuat:
```php
use Spatie\Permission\Models\Role;

Role::create(['name' => 'super_admin']);
Role::create(['name' => 'panel_user']);
```

### User tidak bisa akses panel
1. Cek apakah user memiliki role atau permission:
```php
$user = User::find(1);
dd($user->roles, $user->permissions);
```

2. Assign role atau permission:
```php
$user->assignRole('super_admin');
// atau
$user->givePermissionTo('access_admin_panel');
```

### Clear Permission Cache
```bash
php artisan permission:cache-reset
```

## Security Best Practices

### 1. Principle of Least Privilege
- Hanya berikan permission yang benar-benar diperlukan
- Gunakan role-based access control (RBAC)

### 2. Regular Audit
- Review user permissions secara berkala
- Hapus permission yang tidak diperlukan

### 3. Logging
- Monitor akses yang ditolak
- Log aktivitas admin panel

### 4. Multi-layer Security
```php
// Kombinasi middleware untuk keamanan berlapis
->middleware([
    'auth',
    'verified',
    \App\Http\Middleware\EnsureHasAdminPanelPermission::class,
    'throttle:60,1', // Rate limiting
])
```

## Testing

### Unit Test Example
```php
public function test_middleware_allows_super_admin()
{
    $user = User::factory()->create();
    $user->assignRole('super_admin');
    
    $request = Request::create('/admin');
    $request->setUserResolver(fn() => $user);
    
    $middleware = new EnsureHasAdminPanelPermission();
    $response = $middleware->handle($request, fn($req) => new Response('OK'));
    
    $this->assertEquals('OK', $response->getContent());
}

public function test_middleware_blocks_unauthorized_user()
{
    $user = User::factory()->create();
    // User tanpa role atau permission
    
    $request = Request::create('/admin');
    $request->setUserResolver(fn() => $user);
    
    $middleware = new EnsureHasAdminPanelPermission();
    
    $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
    $middleware->handle($request, fn($req) => new Response('OK'));
}
```

## Status
✅ **Middleware berfungsi dengan baik**
- Permission `access_admin_panel` sudah dibuat
- Role `super_admin` dan `panel_user` sudah dikonfigurasi
- Error handling sudah diimplementasi
- Fallback configuration sudah tersedia

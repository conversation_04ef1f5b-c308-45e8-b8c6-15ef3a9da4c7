<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use App\Filament\Widgets\IkonWidget;
use Filament\Actions;

use Filament\Resources\Components\Tab;



use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class ListHalaman extends ListRecords
{



    protected static string $resource = CmsResource::class;
    protected static ?string $title = 'List konten';
    protected static ?string $navigationLabel = 'List konten';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $slug = 'konten';
    protected static ?int $navigationSort = 1;


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Tambah konten'),

        ];
    }


    public  function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            ->paginated([10, 20, 50, 100])
            ->defaultPaginationPageOption(20)
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('jenis')
                    ->label('Jenis')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'ARTIKEL' => 'success',
                        'HALAMAN' => 'info',
                        'SLIDESHOW' => 'warning',
                        'WIDGET' => 'danger',
                        default => 'gray',
                    })
                    ->icon(fn(string $state): string => match ($state) {
                        'ARTIKEL' => 'heroicon-o-document-text',
                        'HALAMAN' => 'heroicon-o-document',
                        'SLIDESHOW' => 'heroicon-o-photo',
                        'WIDGET' => 'heroicon-o-squares-2x2',
                        default => 'heroicon-o-document',
                    }),

                TextColumn::make('judul')
                    ->label('Judul')
                    ->description(fn($record) => new HtmlString("<span class='text-xs text-gray-500 text-center'> Slug : " . Str::limit($record->slug, 20) . "</span>"))
                    ->sortable()
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= 50) {
                            return null;
                        }

                        return $state;
                    }),

                TextColumn::make('kategori.nama')
                    ->label('Kategori')
                    ->sortable()

                    ->searchable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'tampil' => 'success',
                        'draft' => 'gray',
                        'home' => 'primary',
                        default => 'secondary',
                    }),

                ImageColumn::make('gambar')
                    ->label('Gambar')
                    ->circular()
                    ->stacked()
                    ->limit(3)
                    ->defaultImageUrl(url('/noimage.jpg'))
                    ->extraImgAttributes(['class' => 'object-cover'])
                    ->checkFileExistence(false)
                    ->state(function ($record) {
                        // Jika gambar adalah array, ambil elemen pertama
                        if (is_array($record->gambar)) {
                            return $record->gambar[0] ?? null;
                        }

                        // Jika gambar adalah string JSON, decode dan ambil elemen pertama
                        if (is_string($record->gambar) && (str_starts_with($record->gambar, '[') || str_starts_with($record->gambar, '{'))) {
                            $decoded = json_decode($record->gambar, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                if (isset($decoded[0])) {
                                    return $decoded[0];
                                } elseif (!empty($decoded)) {
                                    // Jika array asosiatif, ambil nilai pertama
                                    return reset($decoded);
                                }
                            }
                        }

                        // Jika bukan array atau JSON, kembalikan apa adanya
                        return $record->gambar;
                    }),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),


            ])
            ->defaultSort('id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(false)
                    ->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil'),
                Tables\Actions\ActionGroup::make([




                    Tables\Actions\Action::make('preview')
                        ->label('Pratinjau')
                        ->icon('heroicon-o-arrow-top-right-on-square')
                        ->color('success')
                        ->url(fn($record) => url('/' . $record->slug))
                        ->openUrlInNewTab(),
                ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis')
                    ->label('Jenis')
                    ->options([
                        'ARTIKEL' => 'Artikel',
                        'HALAMAN' => 'Halaman',
                        'SLIDESHOW' => 'Slideshow',
                        'WIDGET' => 'Widget',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'tampil' => 'Tampil',
                        'draft' => 'Draft',
                        'home' => 'Halaman Utama',
                    ]),
            ]);
    }



    public function getTabs(): array
    {
        return [
            'Semua' => Tab::make(),
            'Artikel' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('jenis', 'ARTIKEL')),
            'Halaman' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('jenis', 'HALAMAN')),
            'Acara' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('jenis', 'ACARA')),
            'Galeri' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('jenis', 'GALERI')),
        ];
    }
    public function getDefaultActiveTab(): string | int | null
    {
        return 'active';
    }

    public function getHeaderWidgets(): array
    {
        return [
    
        ];
    }
}

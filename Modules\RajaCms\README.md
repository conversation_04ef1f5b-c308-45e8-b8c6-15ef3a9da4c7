# RajaCms Module

Modul <PERSON> (Content Management System) untuk aplikasi hotel.

## Fitur

- Manajemen konten website
- Artikel dan blog
- Halaman statis
- Acara dan event
- Tema website
- Marketplace

## Controller

### CmsController
Controller utama untuk menangani halaman website:
- `beranda()` - <PERSON>aman utama website
- `daftarArtikel()` - Daftar artikel/blog
- `detailArtikel($slug)` - Detail artikel
- `halaman($slug)` - Halaman statis
- `acara()` - Daftar acara
- `daftarAcara()` - Daftar acara (alias)
- `detailAcara($slug)` - Detail acara
- `detailEvent($slug)` - Detail event (alias)
- `marketplace()` - Halaman marketplace

### TemaController
Controller untuk manajemen tema:
- `daftar()` - Menampilkan daftar tema yang tersedia
- `dapatkanDaftarTema()` - Method private untuk mendapatkan daftar tema

## Model

- `Cms` - Model untuk konten CMS

## Route

Semua route untuk CMS telah dipindahkan ke modul ini dan menggunakan namespace `Modules\RajaCms\Http\Controllers\`.

## Instalasi

Modul ini sudah terintegrasi dengan aplikasi utama dan akan auto-discover saat aplikasi berjalan.

## Support

you can join our discord server to get support [TomatoPHP](https://discord.gg/Xqmt35Uh)

## Docs

you can check docs of this package on [Docs](https://docs.tomatophp.com/plugins/tomato-themes)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.

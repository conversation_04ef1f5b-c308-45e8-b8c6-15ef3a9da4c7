<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Panel;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
 
use Illuminate\Notifications\Notifiable;
use Jeffgreco13\FilamentBreezy\Traits\TwoFactorAuthenticatable;
use Laravel\Sanctum\HasApiTokens; 
use Spatie\Permission\Traits\HasRoles;


 

/**
 * @mixin IdeHelperUser
 */
class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable ,  TwoFactorAuthenticatable , HasRoles  ; // HasRoles disabled - spatie/laravel-permission removed

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'pin',
        'username',
        'shift_id',
        // Member fields - minimal
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Menentukan apakah user boleh mengakses sebuah Panel Filament.
     *
     * Aturan:
     * 1. Panel dengan ID 'admin' hanya boleh diakses oleh user
     *    yang memiliki role 'admin' ATAU permission 'access_admin_panel'.
     * 2. Panel lainnya (mis. panel kasir/hotel) masih dibebaskan,
     *    ubah sesuai kebutuhan jika ingin aturan berbeda.
     */
    public function canAccessPanel(Panel $panel): bool
    {
        // Dapatkan ID panel
        $panelId = $panel->getId();

        // Jika panel admin, cek role/permission
        if ($panelId === 'admin') {
            // Hanya izinkan jika user memiliki permission khusus
            return $this->hasPermissionTo('access_admin_panel');
        }

        // Default: izinkan akses ke panel lain
        return true;
    }

    /**
     * Relasi ke model Toko
     * Setiap user hanya bisa memiliki 1 toko (business rule)
     */
    public function toko(): HasOne
    {
        return $this->hasOne(Toko::class, 'user_id');
    }

    /**
     * Accessor untuk mendapatkan toko_id berdasarkan user yang sedang login
     * Menangani kasus ketika user belum memiliki toko (return null)
     */
    public function getTokoIdAttribute(): ?int
    {
        // Gunakan relasi toko untuk mendapatkan ID
        // Jika relasi belum di-load, load secara lazy
        if (!$this->relationLoaded('toko')) {
            $toko = Toko::where('user_id', $this->id)->first();
            return $toko ? $toko->id : null;
        }

        // Jika relasi sudah di-load, gunakan relasi
        return $this->toko ? $this->toko->id : null;
    }

    // ========== MEMBER METHODS ==========

    protected static function boot()
    {
        parent::boot();

        static::created(function ($user) {
            // Assign default role 'member' setelah user dibuat jika belum punya role
            if (!$user->roles()->exists()) {
                $user->assignRole('member');
            }
        });
    }

    /**
     * Update last login
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Check if user is a member (has member role)
     */
    public function isMember(): bool
    {
        return $this->hasRole('member');
    }
}

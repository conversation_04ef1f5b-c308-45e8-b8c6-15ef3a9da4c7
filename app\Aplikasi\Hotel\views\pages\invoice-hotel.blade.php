<x-filament-panels::page>
  <div>
    @if ($this->record)
      <div id="cetakArea" class="rounded-lg bg-white p-6 shadow print:p-0 print:shadow-none">
        <div class="mb-6 flex items-center justify-between border-b pb-4">
          <div>
            <h1 class="text-2xl font-bold">INVOICE</h1>
            <p class="text-gray-500">No: {{ $this->record->no_invoice }}</p>
          </div>
          <div class="text-right">
            <h2 class="text-xl font-semibold">Hotel {{ config('app.name') }}</h2>
            <p class="text-gray-500">Dicetak: {{ now()->format('d F Y') }}</p>
          </div>
        </div>

        <div class="mb-6 grid grid-cols-2 gap-6 md:grid-cols-2">
          <!-- Detail Tamu -->
          <div>
            <h3 class="mb-2 text-lg font-semibold">Tamu</h3>
            <p><span class="font-medium">Nama:</span> {{ $this->record->tamu->nama }}</p>
            <p><span class="font-medium">Telepon:</span> {{ $this->record->tamu->telpon }}</p>
            @if ($this->record->tamu->email)
              <p><span class="font-medium">Email:</span> {{ $this->record->tamu->email }}</p>
            @endif
          </div>

          <!-- Detail Reservasi -->
          <div>
            <h3 class="mb-2 text-lg font-semibold">Reservasi</h3>
            <p><span class="font-medium">Check In / Out:</span> </p>
            <p><span class="font-light text-sm">  {{ \Carbon\Carbon::parse($this->record->check_in)->format('d F Y H:i') }} /    {{ \Carbon\Carbon::parse($this->record->check_out)->format('d F Y H:i') }}</span> </p>
          
         
            <p><span class="font-normal text-sm">Kamar  : 
              {{ $this->record->kamar->nama }} ( {{ $this->record->kamar->tipe->nama }} ) -   {{ \Carbon\Carbon::parse($this->record->check_in)->diffInDays(\Carbon\Carbon::parse($this->record->check_out)) }}
              malam </span></p>
          </div>
        </div>

        <!-- Daftar Transaksi -->
        <div class="mb-6">
          <h3 class="mb-2 text-lg font-semibold">Detail Transaksi</h3>
          <div class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead>
                <tr class="bg-gray-100">
                  <th class="px-3 py-2 text-left">Item</th>
                  <th class="px-3 py-2 text-center">Jumlah</th>
                  <th class="px-3 py-2 text-right">Harga</th>
                  <th class="px-3 py-2 text-right">Subtotal</th>
                </tr>
              </thead>
              <tbody>
                @foreach ($this->record->transaksi as $transaksi)
                  <tr class="border-b">
                    <td class="px-3 py-2">{{ $transaksi->nama_item }}</td>
                    <td class="px-3 py-2 text-center">{{ $transaksi->jumlah }}</td>
                    <td class="px-3 py-2 text-right">Rp {{ number_format($transaksi->harga, 0, ',', '.') }}</td>
                    <td class="px-3 py-2 text-right">Rp
                      {{ number_format($transaksi->jumlah * $transaksi->harga, 0, ',', '.') }}</td>
                  </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>

        <!-- Informasi Pembayaran -->
        <div class="mb-6 grid grid-cols-2 gap-6 md:grid-cols-2">
          <!-- Riwayat Pembayaran -->
          <div>
            <h3 class="mb-2 text-lg font-semibold">Riwayat Pembayaran</h3>
            <div class="overflow-x-auto">
              <table class="w-full text-sm">
                <thead>
                  <tr class="bg-gray-100">
                    <th class="px-3 py-2 text-left">Metode</th>
                    <th class="px-3 py-2 text-right">Jumlah</th>
                    <th class="px-3 py-2 text-right">Tanggal</th>
               
                  </tr>
                </thead>
                <tbody>
                  @foreach ($this->record->pembayaran as $pembayaran)
                    <tr class="border-b">
                      <td class="px-3 py-2">{{ $pembayaran->metodePembayaran->nama ?? 'Tidak diketahui' }}</td>
                      <td class="px-3 py-2 text-right">Rp {{ number_format($pembayaran->jumlah, 0, ',', '.') }}</td>
                      <td class="px-3 py-2 text-right">
                        {{ \Carbon\Carbon::parse($pembayaran->created_at)->format('d/m/Y H:i') }}</td>
                 
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          </div>

          <!-- Ringkasan Tagihan -->
          <div>
            <h3 class="mb-2 text-lg font-semibold">Ringkasan Tagihan</h3>
            <table class="w-full text-sm">
              <tr>
                <td class="py-1">Subtotal</td>
                <td class="py-1 text-right">Rp {{ number_format($this->record->totalHargaBersih(), 0, ',', '.') }}</td>
              </tr>
              @if ($this->record->diskon > 0)
                <tr>
                  <td class="py-1">Diskon ({{ $this->record->diskon }}%)</td>
                  <td class="py-1 text-right">- Rp
                    {{ number_format(($this->record->totalHargaBersih() * $this->record->diskon) / 100, 0, ',', '.') }}
                  </td>
                </tr>
              @endif
              @if ($this->record->pajak > 0)
                <tr>
                  <td class="py-1">Pajak ({{ $this->record->pajak }}%)</td>
                  <td class="py-1 text-right">Rp
                    {{ number_format(($this->record->totalHargaBersih() * $this->record->pajak) / 100, 0, ',', '.') }}
                  </td>
                </tr>
              @endif
              @if ($this->record->servis > 0)
                <tr>
                  <td class="py-1">Biaya Layanan ({{ $this->record->servis }}%)</td>
                  <td class="py-1 text-right">Rp
                    {{ number_format(($this->record->totalHargaBersih() * $this->record->servis) / 100, 0, ',', '.') }}
                  </td>
                </tr>
              @endif
              <tr class="border-t text-base font-semibold">
                <td class="py-2">TOTAL</td>
                <td class="py-2 text-right">Rp {{ number_format($this->record->totalHarga(), 0, ',', '.') }}</td>
              </tr>
              <tr>
                <td class="py-1">Total Dibayar</td>
                <td class="py-1 text-right">Rp {{ number_format($this->record->hitungTotalPembayaran(), 0, ',', '.') }}
                </td>
              </tr>
              <tr class="font-semibold">
                <td class="py-1">Sisa Tagihan</td>
                <td class="@if ($this->record->hitungSisaTagihan() > 0) text-red-600 @else text-green-600 @endif py-1 text-right">
                  Rp {{ number_format($this->record->hitungSisaTagihan(), 0, ',', '.') }}
                </td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Catatan & Tanda Tangan -->
        <div class="mt-8 grid grid-cols-2 gap-6 md:grid-cols-2 print:mt-16">
          <div>
            @if ($this->record->hitungSisaTagihan() > 0)
              <h3 class="mb-2 text-lg font-semibold">Cara pembayaran</h3>
          <table class="w-full text-xs">
                 
                <tbody>
                    @foreach($this->metodePembayaran as $metode)
                    <tr class="border-b">
                        <td class="py-2 px-3 font-medium w-fit">{{ $metode->nama }}</td>
                        <td class="py-2 px-3">
                            @if($metode->info_tujuan)
                                {{ strip_tags(str_replace(['<p>', '</p>', '<strong>', '</strong>', '<br>', '<br/>', '<br />'], ['', '', '', '', ', ', ', ', ', '], $metode->info_tujuan)) }}
                            @else
                                @if($metode->nama == 'Kode Qris')
                                    kosong
                                @endif
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
          </div>


          <div class="text-right">
           
               <!-- Tanda Tangan dengan layout yang lebih baik -->
    <div class="grid grid-cols-2 gap-8 mt-10">
        <div class="text-center">
            <p class=" ">Petugas Hotel</p>
               <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
            <p class="font-semibold border-t  text-center border-gray-400">{{ $this->record->user->name ?? 'penyair' }}</p>
        </div>
        
        <div class="text-center">
            <div class=" ">Tamu</div>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
           <p> &nbsp; </p>
            <div class="font-semibold border-t  border-gray-400">{{ $this->record->tamu->nama ?? '-' }}</div>
        </div>
    </div>


          </div>
        </div>


        

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500 print:mt-16">
          <p>Terima kasih telah menginap di hotel kami.</p>
      
        </div>
      </div>
    @else
      <div class="py-12 text-center">
        <p class="text-xl text-gray-500">Data reservasi tidak ditemukan.</p>
      </div>
    @endif
  </div>

  <!-- Script untuk print halaman -->
  <script>
    document.addEventListener('cetak-invoice', function() {
      window.print();
    });
  </script>

  <style>
    @media print {
      body * {
        visibility: hidden;
      }

      #cetakArea,
      #cetakArea * {
        visibility: visible;
      }

      #cetakArea {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
      }

      @page {
        size: A4;
        /* size: A4; */
        margin: 0.5cm;
      }

      .print\\:hidden {
        display: none !important;
      }
    }
  </style>
</x-filament-panels::page>

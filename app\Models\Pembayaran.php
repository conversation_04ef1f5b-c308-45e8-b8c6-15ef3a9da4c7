<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\MetodePembayaranUtama;
/**
 * @mixin IdeHelperPembayaran
 */
class Pembayaran extends Model
{
    use HasFactory;
    public function getTable()
    {
         return config('tabel.t_pembayaran.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_pembayaran.kolom', []);
    }
	    protected $casts = [
      

       // 'pengirim' => 'array',
       // 'tujuan' => 'array',
    ];

 

    public function penjualan() : BelongsTo
    {
        return $this->belongsTo(Penjualan::class);
    }

    public function metodePembayaran() : BelongsTo
    {
        return $this->belongsTo(MetodePembayaranUtama::class);
    }
}

<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class AplikasiRajaDesain extends Page
{
   protected static ?string $title = 'Aplikasi ';
   protected static ?string $slug = 'system/aplikasi';
   protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
   protected static string $view = 'pages.aplikasi-raja-desain';
   protected static ?string $navigationGroup = 'System';
   protected static ?string $navigationLabel = 'Aplikasi';

   protected static bool $shouldRegisterNavigation = true;
   public array $aplikasi = [];
   public array $providers = [];

   public function mount(): void
   {
      $this->bacaSemuaAplikasi();
   }

 
   protected function bacaSemuaAplikasi(): void
   {
      $aplikasiPath = app_path('Aplikasi');
      $this->aplikasi = [];
      $this->providers = [];
      $nomor = 1;

      // Pastikan direktori Aplikasi ada
      if (! File::exists($aplikasiPath)) {
         return;
      }

      // Baca semua direktori di app/Aplikasi
      $direktori = File::directories($aplikasiPath);

      foreach ($direktori as $dir) {
         $namaFolder = basename($dir);
         $configPath = $dir.'/config.php';

         // Cek jika file config.php ada
         if (File::exists($configPath)) {
            $config = include $configPath;

            // Cek jika config memiliki id dan nama
            if (isset($config['id']) && isset($config['nama'])) {
               // Simpan data aplikasi
               $this->aplikasi[] = [
                  'no' => $nomor++,
                  'id' => $config['id'],
                  'nama' => $config['nama'],
                  'deskripsi' => $this->formatDeskripsi($config['deskripsi'] ?? '-'),
                  'versi' => $config['versi'] ?? '1.0.0',
                  'lokasi' => "app/Aplikasi/{$namaFolder}",
                  'status' => $config['status'] ?? 'nonaktif',
                  'path' => $dir,
                  'providers' => $config['auto_register_services'] ?? [],
                  'gambar' => $this->dapatkanGambar($dir, $config)
               ];

               // Jika aplikasi aktif, tambahkan providers
               if (($config['status'] ?? 'nonaktif') === 'aktif' && ! empty($config['auto_register_services'])) {
                  foreach ($config['auto_register_services'] as $provider) {
                     $this->providers[] = [
                        'nama_aplikasi' => $config['nama'],
                        'provider' => $provider
                     ];
                  }
               }
            }
         }
      }




   }

   protected function formatDeskripsi(string $deskripsi): string
   {
      // Jika deskripsi kosong atau hanya tanda "-"
      if (empty($deskripsi) || $deskripsi === '-') {
         return 'Tidak tersedia informasi deskripsi untuk aplikasi ini.';
      }

      // Jika deskripsi terlalu pendek
      if (strlen($deskripsi) < 10) {
         return $deskripsi.' (Deskripsi singkat)';
      }

      return $deskripsi;
   }

   protected function dapatkanGambar(string $path, array $config): string
   {
      // Cek gambar dari konfigurasi
      if (isset($config['gambar']) && ! empty($config['gambar'])) {
         $gambarPath = $path.'/'.$config['gambar'];

         if (File::exists($gambarPath)) {
            // Gambar ada di folder aplikasi
            // Kita tidak bisa langsung mengakses file di dalam 'app' dari browser
            // Jadi kita perlu menyimpan gambar di folder public

            // Cek apakah gambar sudah ada di folder public
            $publicPath = public_path('aplikasi/'.basename($path).'/'.$config['gambar']);
            $publicDir = dirname($publicPath);

            // Pastikan direktori ada
            if (! File::exists($publicDir)) {
               File::makeDirectory($publicDir, 0755, true);
            }

            // Salin gambar jika belum ada di folder public
            if (! File::exists($publicPath)) {
               File::copy($gambarPath, $publicPath);
            }

            return asset('aplikasi/'.basename($path).'/'.$config['gambar']);
         }
      }

      // Gunakan gambar default
      return asset('noimage.jpg');
   }


   public function toggleStatus(int $index): void
   {
      // Dapatkan aplikasi yang akan diubah
      $aplikasi = $this->aplikasi[$index];

      // Toggle status
      $statusBaru = $aplikasi['status'] === 'aktif' ? 'nonaktif' : 'aktif';

      // Update file config
      $configPath = $aplikasi['path'].'/config.php';

      if (File::exists($configPath)) {
         $configContent = File::get($configPath);

         // Mencari dan mengganti status
         $pattern = "/'status'\s*=>\s*'(aktif|nonaktif)'/";
         $replacement = "'status' => '".$statusBaru."'";

         if (preg_match($pattern, $configContent)) {
            // Jika 'status' sudah ada, update nilainya
            $updatedContent = preg_replace($pattern, $replacement, $configContent);
         } else {
            // Jika 'status' belum ada, tambahkan di bagian akhir array
            $updatedContent = preg_replace('/return\s+\[\s*/', "return [\n    'status' => '".$statusBaru."',\n    ", $configContent);
         }

         // Simpan perubahan ke file config
         File::put($configPath, $updatedContent);

         // Update data aplikasi lokal
         $this->aplikasi[$index]['status'] = $statusBaru;

         // Perbarui daftar providers
         $this->bacaSemuaAplikasi();

         // Tampilkan notifikasi sukses
         Notification::make()
            ->title("Status aplikasi {$aplikasi['nama']} berhasil diubah menjadi $statusBaru")
            ->success()
            ->send();
      }
   }

   /**
    * Muat ulang halaman
    */
   public function refresh(): void
   {
      $this->bacaSemuaAplikasi();

      Notification::make()
         ->title('Daftar aplikasi berhasil disegarkan')
         ->success()
         ->send();
   }
}
<?php

namespace App\Aplikasi\Hotel\Resources\LaporanResource\Pages;

use App\Aplikasi\Hotel\Actions\CetakLaporanAction;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Resources\LaporanResource;
use App\Models\Tamu;
use App\Models\User;

use Filament\Forms\Components\DatePicker;

use Filament\Forms\Components\Select;

use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\ColumnGroup;

use Filament\Tables\Columns\Summarizers\Summarizer;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

use Illuminate\Support\HtmlString;
use Carbon\Carbon;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Aplikasi\Hotel\Models\Transaksi;


class ListLaporans extends ListRecords
{
    protected static ?string $model = Reservasi::class;
    protected static string $resource = LaporanResource::class;
    protected static ?string $title = 'LAPORAN RESERVASI';
    protected static bool $canCreate = false;

    protected static ?string $navigationLabel = 'laporan';



    protected function getHeaderActions(): array
    {
        return [
            CetakLaporanAction::make() ->slideOver(),


        ];
    }

    public function table(Table $table): Table
    {
        TextColumn::configureUsing(function (TextColumn $column): void {
            $column
                ->toggleable();
        });

        return $table
            ->striped()

            ->query(Reservasi::query()->orderBy('id', 'desc'))
            ->description(fn () => new HtmlString('<h3>Laporan Reservasi</h3>'))
            ->searchable(false)

            ->columns([
                TextColumn::make('id')->sortable()->searchable(),
                TextColumn::make('tamu.nama')->limit(15)->searchable(),

                TextColumn::make('durasi')
                    ->description(fn ($record) => new HtmlString("<span class='text-xs text-gray-500 text-center'>".$record->check_in->format('d')."-".$record->check_out->format('d/m/y')."</span>"))
                    ->getStateUsing(fn ($record) => Carbon::parse($record->check_in)->diffInDays($record->check_out))
                    ->formatStateUsing(fn ($state) => $state." Malam")
                    ->summarize(
                        Summarizer::make()
                            // ->label('Total Durasi')
                            ->using(function ($query): string {
                                // Dapatkan semua ID reservasi dari query saat ini
                                $reservasiIds = $query->pluck('id');
                                if ($reservasiIds->isEmpty()) {
                                    return '0 Malam';
                                }

                                // Hitung total durasi dari semua reservasi dalam query
                                $totalDurasi = Reservasi::whereIn('id', $reservasiIds)
                                    ->get()
                                    ->sum(function ($reservasi) {
                                    return Carbon::parse($reservasi->check_in)->diffInDays($reservasi->check_out);
                                });

                                return $totalDurasi.' Malam';
                            })
                    ),

                ColumnGroup::make('TOTAL')
                    ->columns([

                        TextColumn::make('total_pajak')
                            ->label('Pajak')
                            ->getStateUsing(fn ($record) => $record->hitungNilaiPajak())
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('Total Pajak')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total pajak dari semua reservasi dalam query
                                        $totalPajak = Reservasi::whereIn('id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($reservasi) {
                                            return $reservasi->hitungNilaiPajak();
                                        });

                                        return 'Rp. '.number_format($totalPajak, 0, ',', '.');
                                    })
                            ),
                        TextColumn::make('total_diskon')
                            ->label('Diskon')
                            ->getStateUsing(fn ($record) => $record->hitungNilaiDiskon())
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('Total Diskon')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total diskon dari semua reservasi dalam query
                                        $totalDiskon = Reservasi::whereIn('id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($reservasi) {
                                            return $reservasi->hitungNilaiDiskon();
                                        });

                                        return 'Rp. '.number_format($totalDiskon, 0, ',', '.');
                                    })
                            ),
                        TextColumn::make('total_servis')
                            ->label('Servis')
                            ->getStateUsing(fn ($record) => $record->hitungNilaiServis())
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('Total Servis')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total servis dari semua reservasi dalam query
                                        $totalServis = Reservasi::whereIn('id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($reservasi) {
                                            return $reservasi->hitungNilaiServis();
                                        });

                                        return 'Rp. '.number_format($totalServis, 0, ',', '.');
                                    })
                            ),

                        TextColumn::make('total_modal')
                            ->label('Modal')
                            ->getStateUsing(function ($record) {
                                // Menghitung total modal dari semua transaksi dalam reservasi
                                return $record->transaksi->sum(function ($transaksi) {
                                    return $transaksi->hitungTotalModal();
                                });
                            })
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('Total Modal')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }
                                        $totalModal = Transaksi::whereIn('reservasi_id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($transaksi) {
                                                return $transaksi->hitungTotalModal();
                                            });

                                        return 'Rp. '.number_format($totalModal, 0, ',', '.');
                                    })
                            ),

                        TextColumn::make('total_bersih')
                            ->label('Harga bersih')
                            ->getStateUsing(function ($record) {
                                // Menghitung total tagihan dari semua transaksi dalam reservasi
                                return $record->transaksi->sum(function ($transaksi) {
                                    return $transaksi->hitungTotalHarga();
                                });
                            })
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('harga jual bersih')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');

                                        // Jika tidak ada reservasi, kembalikan 0
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Ambil semua transaksi untuk reservasi yang dipilih
                                        $totalTagihan = Transaksi::whereIn('reservasi_id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($transaksi) {
                                            return $transaksi->hitungTotalHarga();
                                        });

                                        return 'Rp. '.number_format($totalTagihan, 0, ',', '.');
                                    })
                            ),




                        TextColumn::make('keuntungan')
                            ->label('Keuntungan')
                            ->getStateUsing(function ($record) {
                                $totalTagihan = $record->transaksi->sum(function ($transaksi) {
                                    return $transaksi->hitungTotalHarga();
                                });

                                $totalModal = $record->transaksi->sum(function ($transaksi) {
                                    return $transaksi->hitungTotalModal();
                                });

                                return $totalTagihan - $totalModal;
                            })
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label(' Keuntungan')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }
                                        $transaksis = Transaksi::whereIn('reservasi_id', $reservasiIds)->get();
                                        $totalTagihan = $transaksis->sum(function ($transaksi) {
                                            return $transaksi->hitungTotalHarga();
                                        });

                                        $totalModal = $transaksis->sum(function ($transaksi) {
                                            return $transaksi->hitungTotalModal();
                                        });

                                        $keuntungan = $totalTagihan - $totalModal;

                                        return 'Rp. '.number_format($keuntungan, 0, ',', '.');
                                    }),
                            ),

                    ])
                    ->alignment(Alignment::Center)
                    ->wrapHeader(),
                ColumnGroup::make('PEMBAYARAN')
                    ->columns([


                        TextColumn::make('total_akhir')
                            ->label('Total tagihan')
                            ->getStateUsing(fn ($record) => $record->hitungTotalHargaAkhir())
                            ->numeric(0, ',', '.')
                            ->prefix('Rp. ')
                            ->summarize(
                                Summarizer::make()
                                    // ->label('Grand total')
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');
                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total servis dari semua reservasi dalam query
                                        $totalAkhir = Reservasi::whereIn('id', $reservasiIds)
                                            ->get()
                                            ->sum(function ($reservasi) {
                                            return $reservasi->hitungTotalHargaAkhir();
                                        });

                                        return 'Rp. '.number_format($totalAkhir, 0, ',', '.');
                                    })
                            ),
                        TextColumn::make('pembayaran')->label('Total pembayaran')
                            ->getStateUsing(fn ($record) => $record->hitungTotalPembayaran())
                            ->formatStateUsing(fn ($state) => 'Rp. '.number_format($state, 0, ',', '.'))
                            ->summarize(
                                Summarizer::make()
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');

                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total pembayaran dari semua reservasi dalam query
                                        $totalPembayaran = 0;

                                        foreach ($reservasiIds as $reservasiId) {
                                            $reservasi = Reservasi::find($reservasiId);
                                            if ($reservasi) {
                                                $totalPembayaran += $reservasi->hitungTotalPembayaran();
                                            }
                                        }

                                        return 'Rp. '.number_format($totalPembayaran, 0, ',', '.');
                                    })
                            ),

                        TextColumn::make('sisa_tagihan')
                            ->label('Sisa tagihan')
                            ->getStateUsing(fn ($record) => $record->hitungSisaPembayaran())
                            ->formatStateUsing(fn ($state) => $state < 1
                                ? new HtmlString("<span class='badge bg-success'>Lunas</span>")
                                : "Rp. ".number_format($state, 0, ',', '.'))
                            ->summarize(
                                Summarizer::make()
                                    ->using(function ($query): string {
                                        // Dapatkan semua ID reservasi dari query saat ini
                                        $reservasiIds = $query->pluck('id');

                                        if ($reservasiIds->isEmpty()) {
                                            return 'Rp. 0';
                                        }

                                        // Hitung total sisa tagihan dari semua reservasi dalam query
                                        // yang memiliki sisa pembayaran > 1 (belum lunas)
                                        $totalSisaTagihan = 0;

                                        foreach ($reservasiIds as $reservasiId) {
                                            $reservasi = Reservasi::find($reservasiId);
                                            if ($reservasi) {
                                                $sisaPembayaran = $reservasi->hitungSisaPembayaran();
                                                // Hanya tambahkan ke total jika sisa pembayaran > 1
                                                if ($sisaPembayaran > 1) {
                                                    $totalSisaTagihan += $sisaPembayaran;
                                                }
                                            }
                                        }

                                        return 'Rp. '.number_format($totalSisaTagihan, 0, ',', '.');
                                    })
                            ),
                    ]),


                TextColumn::make('status_reservasi')
                    ->formatStateUsing(fn ($record) => $record->statusReservasi())
                    ->badge()
                    ->colors([
                        'success' => fn ($state) => $state == "SCI",
                        'danger' => fn ($state) => $state == "SCO",
                        'primary' => fn ($state) => $state == "BK",
                    ]),


                TextColumn::make('karyawan_id')
                    ->getStateUsing(fn ($record) => $record->user->name)
                    ->label('Pegawai'),

                TextColumn::make('created_at')
                    ->label('Tanggal')
                    ->dateTime('d/m/Y'),


            ])
            ->filters([

                Filter::make('tanggal')
                    ->label('Tanggal Reservasi')
                    ->form([
                        DatePicker::make('reservasi_dari'),
                        DatePicker::make('reservasi_tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['reservasi_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['reservasi_tanggal'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->columns(2)->columnSpan(2),


                Filter::make('filter_bulan')
                    ->form([
                        // Gunakan komponen Select di dalam form
                        Select::make('bulan')
                            ->label('Reservasi bulan')
                            ->options([
                                '1' => 'Januari',
                                '2' => 'Februari',
                                '3' => 'Maret',
                                '4' => 'April',
                                '5' => 'Mei',
                                '6' => 'Juni',
                                '7' => 'Juli',
                                '8' => 'Agustus',
                                '9' => 'September',
                                '10' => 'Oktober',
                                '11' => 'November',
                                '12' => 'Desember',
                            ])
                            ->placeholder('Semua Bulan')
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['bulan']) && ! empty($data['bulan'])) {
                            $query->whereMonth('created_at', $data['bulan']);
                        }

                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (isset($data['bulan']) && ! empty($data['bulan'])) {
                            $namaBulan = [
                                '1' => 'Januari', '2' => 'Februari', '3' => 'Maret',
                                '4' => 'April', '5' => 'Mei', '6' => 'Juni',
                                '7' => 'Juli', '8' => 'Agustus', '9' => 'September',
                                '10' => 'Oktober', '11' => 'November', '12' => 'Desember',
                            ];

                            return 'Bulan: '.($namaBulan[$data['bulan']] ?? $data['bulan']);
                        }

                        return null;
                    }),

                Filter::make('filter_tahun')
                    ->form([
                        Select::make('tahun')
                            ->label('Reservasi Tahun')
                            ->options(function () {
                                $tahunSekarang = date('Y'); // Menggunakan date() langsung daripada now()->year
                                $options = [];

                                for ($i = 0; $i < 10; $i++) {
                                    $tahun = (int) $tahunSekarang - $i; // Pastikan tipe data integer
                                    $options[$tahun] = (string) $tahun; // Simpan sebagai key integer, value string
                                }

                                return $options;
                            })
                            ->placeholder('Semua Tahun')
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['tahun']) && ! empty($data['tahun'])) {
                            $query->whereYear('created_at', $data['tahun']);
                        }

                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (isset($data['tahun']) && ! empty($data['tahun'])) {
                            return 'Tahun: '.$data['tahun'];
                        }

                        return null;
                    }),

                SelectFilter::make('status_reservasi')
                    ->options(Konfig::jsonKuRaw('reservasi_status')),

                Filter::make('check_in')
                    ->form([
                        DatePicker::make('checkin_dari'),
                        DatePicker::make('checkin_sampai'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['checkin_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('check_in', '>=', $date),
                            )
                            ->when(
                                $data['checkin_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('check_in', '<=', $date),
                            );
                    })->columns(2)->columnSpan(2),


                Filter::make('check_out')
                    ->form([
                        DatePicker::make('checkout_dari'),
                        DatePicker::make('checkout_sampai'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['checkout_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('check_out', '>=', $date),
                            )
                            ->when(
                                $data['checkout_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('check_out', '<=', $date),
                            );
                    })->columns(2)->columnSpan(2),


               // Filter untuk Status Pembayaran (Lunas/Belum Lunas)
                Filter::make('status_pembayaran')
                    ->label('Status Pembayaran')
                    ->form([
                        Select::make('status_pembayaran')
                            ->label('Status Pembayaran')
                            ->options([
                                'lunas' => 'Lunas',
                                'belum_lunas' => 'Belum Lunas',
                            ])
                            ->placeholder('Semua Status Pembayaran')
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // Hanya proses jika ada pilihan status pembayaran
                        if (isset($data['status_pembayaran']) && !empty($data['status_pembayaran'])) {
                            // Pendekatan yang lebih sederhana dan aman untuk menghindari error SQL
                            $reservasiIds = Reservasi::all()->filter(function ($reservasi) use ($data) {
                                $sisaTagihan = $reservasi->hitungSisaPembayaran();
                                
                                // Jika filter lunas, cari yang sisa tagihannya < 1
                                if ($data['status_pembayaran'] === 'lunas') {
                                    return $sisaTagihan < 1;
                                }
                                // Jika filter belum lunas, cari yang sisa tagihannya >= 1
                                elseif ($data['status_pembayaran'] === 'belum_lunas') {
                                    return $sisaTagihan >= 1;
                                }
                                
                                return false;
                            })->pluck('id')->toArray();
                            
                            // Filter reservasi berdasarkan ID yang sudah kita hitung
                            $query->whereIn('id', $reservasiIds);
                        }
                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (isset($data['status_pembayaran']) && !empty($data['status_pembayaran'])) {
                            $statusLabels = [
                                'lunas' => 'Lunas',
                                'belum_lunas' => 'Belum Lunas',
                            ];
                            return 'Status Pembayaran: ' . ($statusLabels[$data['status_pembayaran']] ?? $data['status_pembayaran']);
                        }
                        return null;
                    }),

                SelectFilter::make('tamu_id')->label('Tamu')
                    ->options(Tamu::all()->pluck('nama', 'id'))
                    ->searchable()
                    ->preload(),

                SelectFilter::make('karyawan_id')->label('Petugas')
                    ->options(User::all()->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),






            ], layout: FiltersLayout::AboveContentCollapsible)->filtersFormColumns(5)

        ;
    }
}
<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperRefund
 */
class Refund extends Model
{
    use HasFactory;
    protected $table = 'refund';
    protected $fillable =
        ['toko_id',
            'penjualan_id',
            'sub',
            'penjualan_invoice',
            'reservasi_id',
            'kategori',
            'jenis_pesanan',
            'nama',
            'nama_tamu',
            'tamu_id',
            'diskon',
            'pajak',
            'servis',
            'jumlah_pembayaran',
            'metode_pembayaran',
            'status_pembayaran',
            'ket_pembayaran',
            'foto_pembayaran',
            'ket_pesanan',
            'karyawan_id',
            'status'
        ];


    public function refundtransaksi()
    {
        return $this->hasMany(RefundTransaksi::class, 'refund_id', 'id');
    }

    public function penjualan()
    {
        return $this->belongsTo(Penjualan::class, 'penjualan_id', 'id');
    }
}

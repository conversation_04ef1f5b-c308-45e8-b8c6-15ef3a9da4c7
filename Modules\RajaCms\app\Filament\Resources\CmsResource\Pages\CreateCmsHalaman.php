<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Modules\RajaJson\Filament\Resources\Pages\AutoRajajsonCreateRecord;
use Filament\Actions;

class CreateCmsHalaman extends AutoRajajsonCreateRecord
{
    protected static string $resource = CmsResource::class;
    protected static ?string $title = 'Buat Konten';

    protected static ?string $breadcrumb = 'Buat konten';

    // Override method mount untuk debugging
    public function mount(): void
    {
        parent::mount();

        // Log untuk debugging
        // \Illuminate\Support\Facades\Log::debug('CreateCmsHalaman mount');
    }

    protected function getRedirectUrl(): string
    {
        return CmsResource::getUrl('index');
    }

    /**
     * Tambahkan tombol save di header (di atas form)
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('simpanKonten')
                ->label('Simpan Konten')
                ->color('success')
                ->icon('heroicon-o-check-circle')
                ->action(function () {
                    $this->create();
                })
                ->extraAttributes([
                    'wire:loading.attr' => 'disabled',
                    'wire:loading.class' => 'opacity-70 cursor-wait',
                    'class' => 'filament-button-tunggu'
                ])
                ->iconPosition('after'),
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction(),
            $this->getCancelFormAction(),
        ];
    }

    protected function getCreateFormAction(): \Filament\Actions\Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan')
            ->color('success')
            ->icon('heroicon-o-check-circle')
            ->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-70 cursor-wait',
                'class' => 'filament-button-tunggu'
            ])
            ->iconPosition('after');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Jika status adalah 'home', pastikan jenis adalah 'HALAMAN'
        if (isset($data['status']) && $data['status'] === 'home' && isset($data['jenis']) && $data['jenis'] !== 'HALAMAN') {
            // Reset status ke 'tampil' jika jenis bukan 'HALAMAN'
            $data['status'] = 'tampil';
        }

        return $data;
    }

    // Tambahkan method beforeCreate untuk validasi status 'home'
    protected function beforeCreate(): void
    {
        $data = $this->form->getState();

        // Jika status yang akan disimpan adalah 'home'
        if (isset($data['status']) && $data['status'] === 'home') {
            // Cek apakah sudah ada halaman dengan status 'home'
            $existingHome = \Modules\RajaCms\Models\Cms::where('status', 'home')->first();

            if ($existingHome) {
                // Ubah status halaman yang sebelumnya 'home' menjadi 'tampil'
                $existingHome->status = 'tampil';
                $existingHome->save();
            }
        }
    }
}

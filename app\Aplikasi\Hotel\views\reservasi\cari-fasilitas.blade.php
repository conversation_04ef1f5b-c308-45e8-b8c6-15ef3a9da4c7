<div>

<button class="tombol tombol-ikon" wire:click.prevent="$dispatch('open-modal', { id: 'carifasilitas' })"><x-heroicon-c-archive-box class="h-5 w-5 mr-2 text-slate-600" /> <span> Fasilitas tambahan</span></button>

  <x-filament::modal id="carifasilitas" width="xl" slide-over>
  

  
    <form wire:submit.prevent>
      <x-slot name="header">
        <div class="flex items-center">
     <x-heroicon-c-archive-box class="h-5 w-5 mr-2 text-slate-600" />
          <h2 class="text-lg font-medium text-slate-800">Fasilitas tambahan</h2>
        </div>
      </x-slot>

 
          <table class="min-w-full divide-y divide-slate-200">
            <thead class="bg-slate-50">
              <tr>
                {{-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider w-1/6">Barcode</th> --}}
                <th scope="col"
                  class="w-2/6 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-slate-500">
                  Nama Produk</th>
                <th scope="col"
                  class="w-1/6 px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-slate-500">
                  Harga</th>
                <th scope="col"
                  class="w-1/6 px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-slate-500">
                  Stok</th>
                <th scope="col"
                  class="w-1/6 px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-slate-500">
                  Aksi</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 bg-white">
              @forelse($produk as $item)
                <tr class="hover:bg-slate-50">
                  {{-- <td class="px-6 py-3 whitespace-nowrap text-sm font-mono text-slate-500">{{ $item->barcode ?? '-' }}</td> --}}
                  <td
                    class="whitespace-nowrap px-6 py-3 text-sm font-medium text-slate-900">
                    {{ $item->nama }}</td>
                  <td
                    class="whitespace-nowrap px-6 py-3 text-right text-sm text-slate-800">
                    Rp
                    {{ number_format($item->harga, 0, ',', '.') }}</td>
                  <td class="whitespace-nowrap px-6 py-3 text-center text-sm">
                    @if ($item->stok > 10)
                      <span                        class="inline-flex rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium leading-5 text-green-800">
                        {{ $item->stok ?? 0 }}
                      </span>
                    @elseif($item->stok > 0)
                      <span
                        class="inline-flex rounded-full bg-yellow-100 px-2 py-0.5 text-xs font-medium leading-5 text-yellow-800">
                        {{ $item->stok ?? 0 }}
                      </span>
                    @else
                      <span
                        class="inline-flex rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium leading-5 text-red-800">
                        {{ $item->stok ?? 0 }}
                      </span>
                    @endif
                  </td>
                  <td class="whitespace-nowrap px-6 py-3 text-center text-sm">
                    @if ($item->stok > 0)
                      <button
                        wire:click.prevent="tambahKeKeranjangFasilitas({{ $item->id }})" 
                        class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-3 py-1 text-xs font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                       <x-heroicon-o-plus class="h-4 w-4 mr-1" />
                        Tambah
                      </button>
                    @else
                      Stok Habis
                    @endif

                  </td>
                </tr>
              @empty
                <tr>
                  <td colspan="5"
                    class="px-6 py-10 text-center text-slate-500">
                    <svg xmlns="http://www.w3.org/2000/svg"
                      class="mx-auto h-12 w-12 text-slate-300" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round"
                        stroke-width="1.5" d="M20 12H4m8-4v8" />
                    </svg>
                    <p class="mt-2 text-sm">Tidak ada produk yang ditemukan</p>
                  </td>
                </tr>
              @endforelse
            </tbody>
          </table>
   

    </form>

  </x-filament::modal>

</div>

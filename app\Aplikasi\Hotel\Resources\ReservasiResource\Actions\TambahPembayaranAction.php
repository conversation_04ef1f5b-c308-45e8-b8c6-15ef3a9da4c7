<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\Actions;

use App\Aplikasi\Hotel\Models\Pembayaran;
use App\Aplikasi\Hotel\Models\Reservasi;
use App\Filament\Forms\Components\PembayaranForm;
use App\Models\MetodePembayaranUtama;
use Filament\Actions\Action;

use Filament\Forms\Components\Hidden;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class TambahPembayaranAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'tambahPembayaran';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Tambah Pembayaran')
            ->icon('heroicon-o-banknotes')
            ->color('success')
            ->modalHeading('Tambah Pembayaran')
            ->modalWidth('lg')
            ->form([
                // Tambahkan field nama langsung ke form
                Hidden::make('nama')
                    ->default("pembayaran kamar "),
                // Gunakan PembayaranForm dengan repeater=false
                PembayaranForm::make(['repeater' => false]),
            ])
            ->action(function (array $data, Reservasi $record): void {
                try {
                    // Debug the actual data structure from the form
                    Log::info('Payment form data received:', $data);
                    Log::debug('Raw payment form data:', ['data' => json_encode($data)]);

                    // Access the pembayaran data - when repeater is false, the data is directly in the root
                    $pembayaranData = $data;
                    Log::info('Payment data to be processed:', $pembayaranData);

                    // Validasi data yang diperlukan
                    if (empty($pembayaranData['metode_pembayaran_id'])) {
                        throw new \Exception('Metode pembayaran harus dipilih');
                    }

                    if (empty($pembayaranData['jumlah'])) {
                        throw new \Exception('Jumlah pembayaran harus diisi');
                    }

                    // Fix numeric value formatting
                    $jumlah = $pembayaranData['jumlah'] ?? 0;
                    if (is_string($jumlah)) {
                        $jumlah = (int) preg_replace('/[^\d]/', '', $jumlah);
                    }

                    // Ambil metode pembayaran untuk validasi
                    $metodePembayaran = MetodePembayaranUtama::find($pembayaranData['metode_pembayaran_id']);

                    // Jika tidak ditemukan, lempar exception
                    if (!$metodePembayaran) {
                        throw new \Exception('Metode pembayaran tidak ditemukan');
                    }

                    // Log untuk debugging
                    Log::info('Menggunakan MetodePembayaranUtama:', [
                        'id' => $metodePembayaran->id,
                        'nama' => $metodePembayaran->nama,
                    ]);

                    // Ambil jenis pembayaran
                    $jenisPembayaran = $metodePembayaran->jenis ?? '';

                    // Log jenis pembayaran
                    Log::info('Jenis pembayaran:', [
                        'jenis' => $jenisPembayaran,
                    ]);

                    // Pastikan pengirim adalah array
                    $pengirim = $pembayaranData['pengirim'] ?? [];

                    // Jika pengirim adalah string, coba parse sebagai JSON
                    if (is_string($pengirim) && !empty($pengirim)) {
                        $decoded = json_decode($pengirim, true);
                        if (is_array($decoded)) {
                            $pengirim = $decoded;
                        }
                    }

                    // Jika masih bukan array, gunakan array kosong
                    if (!is_array($pengirim)) {
                        $pengirim = [];
                    }

                    // Jika jenis pembayaran adalah transfer atau kartu, pastikan pengirim diisi
                    if (in_array($jenisPembayaran, ['transfer', 'kartu']) && empty($pengirim)) {
                        // Buat data pengirim minimal
                        $pengirim = ['metode' => $metodePembayaran->nama];

                        // Log data pengirim
                        Log::info('Data pengirim dibuat otomatis:', [
                            'pengirim' => $pengirim,
                        ]);
                    }

                    // Buat pembayaran baru dengan cara yang lebih aman
                    $pembayaran = new Pembayaran();
                    $pembayaran->reservasi_id = $record->id;
                    $pembayaran->nama = $data['nama'] ?? 'Pembayaran kamar';
                    $pembayaran->metode_pembayaran_id = $metodePembayaran->id;
                    $pembayaran->jumlah = $jumlah;
                    $pembayaran->pengirim = $pengirim; // Akan diproses oleh setPengirimAttribute
                    $pembayaran->tujuan = $pembayaranData['tujuan'] ?? '';
                    $pembayaran->bukti = $pembayaranData['bukti'] ?? null;
                    $pembayaran->ket = $pembayaranData['ket'] ?? '';
                    $pembayaran->save();

                    // Log data yang berhasil disimpan
                    Log::info('Pembayaran berhasil disimpan:', [
                        'id' => $pembayaran->id,
                        'metode_pembayaran_id' => $pembayaran->metode_pembayaran_id,
                        'jumlah' => $pembayaran->jumlah,
                        'pengirim' => $pembayaran->pengirim,
                    ]);

                    Notification::make()
                        ->title('Pembayaran berhasil ditambahkan')
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Log::error('Error saat menambahkan pembayaran: ' . $e->getMessage());

                    Notification::make()
                        ->title('Gagal menambahkan pembayaran')
                        ->body($e->getMessage())
                        ->danger()
                        ->send();

                    throw $e;
                }
            });
    }
}

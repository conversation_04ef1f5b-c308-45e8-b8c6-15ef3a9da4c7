<?php

namespace App\Aplikasi\Hotel\Models;

use App\Models\Penjualan;
use App\Models\Transaksi as ModelsTransaksi;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperTransaksi
 */
class Transaksi extends ModelsTransaksi
{
    use HasFactory;
 
    // protected $table = 'transaksi';
 
    // protected $fillable = [
    //     'id',
    //     'toko_id',
    //     'jenis',
    //     'reservasi_id',
    //     'penjualan_id',
    //     'produk_id',
    //     'nama_item',
    //     'harga_modal',
    //     'harga',
    //     'jumlah',
    //     'ket',
    //     'karyawan_id',
    // ];

   
    protected $casts = [
        'harga_modal' => 'float',
        'harga' => 'float',
        'jumlah' => 'integer',
    ];

    public function penjualan(): BelongsTo
    {
        return $this->belongsTo(Penjualan::class, 'penjualan_id');
    }

    public function reservasi(): BelongsTo
    {
        return $this->belongsTo(Reservasi::class, 'reservasi_id');
    }

    public function hitungTotalHarga(): float
    {
        return (float) ($this->jumlah * $this->harga);
    }

 
    public function hitungTotalModal(): float
    {
        return (float) ($this->jumlah * $this->harga_modal);
    }


    public function hitungKeuntungan(): float
    {
        return $this->hitungTotalHarga() - $this->hitungTotalModal();
    }


    public function hitungPersentaseKeuntungan(): float
    {
        if ($this->hitungTotalModal() == 0) {
            return 0;
        }

        return ($this->hitungKeuntungan() / $this->hitungTotalModal()) * 100;
    }

    public function totalHarga(): float
{
    return (float)($this->jumlah * $this->harga);
}
}
<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\extend\Kamar;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperHargaJual
 */
class HargaJual extends Model
{
    protected $table = 'harga_jual';
    protected $fillable = [
        'nama',
        'harga',
        'minimal',
        'produk_id',
        'maksimal',
        'tgl_mulai',
        'tgl_selesai',
        
    ];
 
    public function produk()
    {
        return $this->belongsTo(Produk::class);
    }


    public function kamar()
    {
        return $this->belongsTo(Kamar::class);
    }


}

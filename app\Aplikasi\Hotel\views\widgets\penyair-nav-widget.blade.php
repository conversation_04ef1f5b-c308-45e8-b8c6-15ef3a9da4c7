<div class="  p-1 bg-white border rounded-md border-neutral-200/80">
    <div class="flex flex-col">
        <ul class="flex items-center gap-1 px-2  ">
            @if($judul)
                <li class="px-3 py-1.5 font-semibold text-pink-500 dark:text-blue-300 text-sm">
                    {{ $judul }}
                </li>
            @endif
            
            @foreach($items as $label => $item)
                @if($this->apakahSubmenu($item))
                    <li x-data="{ terbuka: false }" class="relative ">
                        <button 
                            @click="terbuka = !terbuka" 
                            @click.away="terbuka = false"
                            class="flex items-center justify-between px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-200 rounded    focus:outline-none focus:ring-2 focus:ring-blue-400 transition duration-150  hover:bg-pink-500"
                        >
                            <span class=" hover:bg-pink-500">{{ $label }}</span>
                            <span class="ml-1.5">
                                <x-heroicon-s-chevron-down x-show="terbuka" class="w-4 h-4" />
                                <x-heroicon-s-chevron-right x-show="!terbuka" class="w-4 h-4" />
                            </span>
                        </button>

                        <div 
                            x-show="terbuka" 
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="transform opacity-0 scale-95"
                            x-transition:enter-end="transform opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-75"
                            x-transition:leave-start="transform opacity-100 scale-100"
                            x-transition:leave-end="transform opacity-0 scale-95"
                            class="absolute left-0 mt-1 z-10"
                        >
                            <ul class="  bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 w-max min-w-[10rem]">
                                @foreach($item as $subLabel => $subUrl)
                                    <li>
                                        <a 
                                            href="{{ $subUrl }}" 
                                            class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-200 dark:hover:bg-gray-700 whitespace-nowrap"
                                        >
                                            {{ $subLabel }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </li>
                @else
                    <li>
                        <a 
                            href="{{ $item }}" 
                            class="block px-3 py-1.5 text-sm font-medium text-gray-700 border-gray-200 rounded  dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition duration-150 hover:bg-pink-500 hover:text-white capitalize"
                        >
                            {{ $label }}
                        </a>
                    </li>
                @endif
            @endforeach
        </ul>
    </div>
</div>
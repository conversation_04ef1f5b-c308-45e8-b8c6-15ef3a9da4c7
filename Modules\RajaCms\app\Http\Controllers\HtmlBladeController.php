<?php

namespace Modules\RajaCms\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\View;

class HtmlBladeController extends Controller    
{
    public function renderHtml($filename)
    {
        $temaAktif = config('tema.aktif', 'default');
        $filePath = public_path("tema/{$temaAktif}/{$filename}.html");
        
        if (!File::exists($filePath)) {
            abort(404);
        }
        
        // Baca konten HTML
        $htmlContent = File::get($filePath);
        
        // Proses Blade directives dalam HTML
        $processedContent = $this->processBladeInHtml($htmlContent);
        
        return response($processedContent)->header('Content-Type', 'text/html');
    }
    
    private function processBladeInHtml($htmlContent)
    {
        // Siapkan data yang akan digunakan dalam Blade
        $data = [
            'infoweb' => \App\Models\Konfig::jcolObject('website'),
            'menuItems' => \Datlechin\FilamentMenuBuilder\Models\Menu::location('website_header')->first()?->menuItems()->get() ?? collect([]),
            'temaAsset' => asset('tema/' . config('tema.aktif', 'default')),
            'title' => 'Halaman HTML dengan Blade'
        ];
        
        // Buat temporary Blade view
        $tempViewName = 'temp_html_' . uniqid();
        View::addNamespace('temp', storage_path('temp'));
        
        // Simpan konten ke file temporary
        $tempPath = storage_path("temp/{$tempViewName}.blade.php");
        File::put($tempPath, $htmlContent);
        
        // Render dengan Blade
        $rendered = View::make("temp::{$tempViewName}", $data)->render();
        
        // Hapus file temporary
        File::delete($tempPath);
        
        return $rendered;
    }
}
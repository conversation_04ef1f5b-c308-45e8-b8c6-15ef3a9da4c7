<?php

namespace App\Aplikasi\Hotel\Forms\Components;

use App\Aplikasi\Hotel\Models\MetodePembayaran;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\Log;

class PengirimField extends Field
{
  // Path view untuk custom field
  protected string $view = 'hotel::forms.components.pengirim-field';

  // Properti untuk menyimpan jenis pembayaran aktif
  protected string|\Closure|null $jenisPembayaran = null;

  // Properti untuk mengatur teks yang ditampilkan saat data kosong
  protected string|\Closure|null $teksDataKosong = 'Data pengirim kosong, tambahkan sekarang';

  // Properti untuk callback afterStateUpdated
  protected \Closure|null $afterStateUpdatedCallback = null;

  // Method untuk mengatur jenis pembayaran
  public function jenisPembayaran(string|\Closure|null $jenisPembayaran): static
  {
    $this->jenisPembayaran = $jenisPembayaran;

    return $this;
  }

  // Method untuk mengatur teks data kosong
  public function teksDataKosong(string|\Closure|null $teksDataKosong): static
  {
    $this->teksDataKosong = $teksDataKosong;

    return $this;
  }

  // Method untuk menangani afterStateUpdated
  public function afterStateUpdated(\Closure|null $callback): static
  {
    $this->afterStateUpdatedCallback = $callback;

    return $this;
  }

  // Mendapatkan jenis pembayaran aktif
  public function getJenisPembayaran(): ?string
  {
    return $this->evaluate($this->jenisPembayaran);
  }

  // Mendapatkan teks data kosong
  public function getTeksDataKosong(): ?string
  {
    return $this->evaluate($this->teksDataKosong);
  }

  // Override getState untuk memastikan data selalu tersedia
  public function getState(): mixed
  {
    $state = parent::getState();

    // Pastikan state selalu array jika kosong
    if (empty($state)) {
      return [];
    }

    return $state;
  }

  // Setup field dan actions saat instance dibuat
  public function setUp(): void
  {
    parent::setUp();

    // Daftarkan action untuk field ini
    $this->registerActions([
      Action::make('tambahDataPengirim')
        ->iconButton('heroicon-m-pencil-square')
        ->label(function (Get $get): string {
          $statePath = $this->getStatePath();
          $state = $get($statePath);

          // Jika sudah ada data, ubah label tombol
          if (! empty($state) && is_array($state)) {
            return 'ubah';
          }

          return "+";
        })
        ->modalHeading('Data Pengirim')
        ->modalDescription('Silahkan lengkapi data pengirim sesuai dengan metode pembayaran')
        ->form(function (Get $get): array {
          // Ambil jenis pembayaran dari state atau dari properti
          $jenisPembayaran = $this->getJenisPembayaran() ?: $get('jenis_pembayaran');

          if (empty($jenisPembayaran)) {
            return [];
          }

          // Ambil skema form dari jenis pembayaran
          return $this->generateFormSchema($jenisPembayaran);
        })
        // Pre-fill form jika ada data yang sudah tersimpan
        ->fillForm(function (Get $get): array {
          // Dapatkan data yang sudah ada di state
          $statePath = $this->getStatePath();
          $existingData = $get($statePath) ?: [];

          // Pastikan data yang dikembalikan berupa array
          return is_array($existingData) ? $existingData : [];
        })
        // Handle saat form di-submit
        // Tambahkan di method action() di PengirimField
        ->action(function (array $data, Set $set, Get $get): void {
          // Ambil jenis pembayaran untuk filter data
          $jenisPembayaran = $this->getJenisPembayaran() ?: $get('jenis_pembayaran');

          // Filter data berdasarkan setting 'insert' dari config
          $dataToSave = $this->filterDataForInsert($data, $jenisPembayaran);

          // Debug: log data sebelum disimpan
          // \Illuminate\Support\Facades\Log::info('Data pengirim yang akan disimpan:', $dataToSave);
    
          // PERBAIKAN: Simpan data dengan cara alternatif
          $statePath = $this->getStatePath();
          $livewire = $this->getLivewire();

          // Simpan langsung ke data Livewire
          data_set($livewire, $statePath, $dataToSave);

          // Perbarui ringkasan pengirim secara MANUAL
          $info = '';
          foreach ($dataToSave as $key => $value) {
            if (! empty($value)) {
              $label = ucwords(str_replace('_', ' ', $key));
              $info .= "{$label}: {$value}\n";
            }
          }

          // Set langsung ke text_info_pengirim
          if (! empty($info)) {
            data_set($livewire, 'data.text_info_pengirim', $info);
          } else {
            data_set($livewire, 'data.text_info_pengirim', 'Data pengirim tidak lengkap');
          }

          // Trigger Livewire untuk render
          $livewire->dispatch('refresh');

          // Beri notifikasi
          // \Filament\Notifications\Notification::make()
          //  ->success()
          //  ->title('Data pengirim berhasil disimpan')
          //  ->send();
    
        })
      ,
    ]);

    // Tambahkan atribut live pada field untuk memastikan perubahan state dideteksi
    $this->live()
      ->afterStateUpdated(function ($state, Set $set, Get $get): void {
        // Log::info('PengirimField state updated', [
        //  'path' => $this->getStatePath(),
        //  'state' => $state
        // ]);
  
        // Panggil callback afterStateUpdated jika ada
        if ($this->afterStateUpdatedCallback) {
          $callback = $this->afterStateUpdatedCallback;
          $callback($get, $set);

          // Dispatch event untuk memastikan UI diperbarui
          $set('text_info_pengirim', 'Memperbarui informasi...');
        }
      });
  }

  // Mendapatkan action utama untuk ditampilkan di field
  public function getMainAction(): ?Action
  {
    return $this->getAction('tambahDataPengirim');
  }

  // Membuat schema form berdasarkan jenis pembayaran
  protected function generateFormSchema(string $jenisPembayaran): array
  {
    // Ambil konfigurasi fields dari config
    $fieldsConfig = config("hotel.info_pengirim.{$jenisPembayaran}", []);

    if (empty($fieldsConfig)) {
      return [];
    }

    $formSchema = [];

    foreach ($fieldsConfig as $fieldConfig) {
      $field = $this->createField($fieldConfig);

      if ($field) {
        $formSchema[] = $field;
      }
    }

    return $formSchema;
  }

  // Membuat field berdasarkan konfigurasi
  protected function createField(array $fieldConfig): ?Field
  {
    $nama = $fieldConfig['nama'] ?? null;
    $jenis = $fieldConfig['jenis'] ?? null;

    if (empty($nama) || empty($jenis)) {
      return null;
    }

    $field = match ($jenis) {
      'textinput' => $this->createTextInput($fieldConfig),
      'textarea' => $this->createTextarea($fieldConfig),
      'radio' => $this->createRadio($fieldConfig),
      'select' => $this->createSelect($fieldConfig),
      'view' => $this->createViewField($fieldConfig),
      default => null,
    };

    return $field;
  }

  // Membuat TextInput field
  protected function createTextInput(array $config): TextInput
  {
    $field = TextInput::make($config['nama'])
      ->label(ucwords(str_replace('_', ' ', $config['nama'])));

    if (isset($config['hint'])) {
      $field->hint($config['hint']);
    }

    if (isset($config['default_value'])) {
      $field->default($config['default_value']);
    }

    if (isset($config['disabled']) && $config['disabled']) {
      $field->disabled();
    }

    if (isset($config['prefix'])) {
      $field->prefix($config['prefix']);
    }

    if (isset($config['prefixicon'])) {
      $field->prefixIcon($config['prefixicon']);
    }

    if (isset($config['numeric']) && $config['numeric']) {
      $field->numeric();
    }

    if (isset($config['mask'])) {
      $field->mask($config['mask']);
    }

    return $field;
  }

  // Membuat Textarea field
  protected function createTextarea(array $config): Textarea
  {
    $field = Textarea::make($config['nama'])
      ->label(ucwords(str_replace('_', ' ', $config['nama'])));

    if (isset($config['hint'])) {
      $field->hint($config['hint']);
    }

    if (isset($config['default_value'])) {
      $field->default($config['default_value']);
    }

    if (isset($config['disabled']) && $config['disabled']) {
      $field->disabled();
    }

    return $field;
  }

  // Membuat Radio field
  protected function createRadio(array $config): Radio
  {
    $options = $this->parseOptions($config['options'] ?? '');

    $field = Radio::make($config['nama'])
      ->label(ucwords(str_replace('_', ' ', $config['nama'])))
      ->options($options);

    if (isset($config['default_value'])) {
      $field->default($config['default_value']);
    }

    return $field;
  }

  // Membuat Select field
  protected function createSelect(array $config): Select
  {
    $options = $this->parseOptions($config['options'] ?? '');

    $field = Select::make($config['nama'])
      ->label(ucwords(str_replace('_', ' ', $config['nama'])))
      ->options($options);

    if (isset($config['default_value'])) {
      $field->default($config['default_value']);
    }

    if (isset($config['suffixicon'])) {
      $field->suffixIcon($config['suffixicon']);
    }

    return $field;
  }

  // Membuat ViewField
  protected function createViewField(array $config): ViewField
  {
    $field = ViewField::make($config['nama'])
      ->label(ucwords(str_replace('_', ' ', $config['nama'])));

    if (isset($config['view'])) {
      $field->view($config['view']);
    }

    if (isset($config['default_value'])) {
      $field->default($config['default_value']);
    }

    return $field;
  }

  // Mem-parse opsi dari string menjadi array
  protected function parseOptions(string $optionsString): array
  {
    if (empty($optionsString)) {
      return [];
    }

    $options = explode(',', $optionsString);
    $options = array_map('trim', $options);

    return array_combine($options, $options);
  }

  // Memfilter data yang akan disimpan (hanya yang insert = true)
  protected function filterDataForInsert(array $data, ?string $jenisPembayaran = null): array
  {
    // Gunakan jenis dari parameter atau dari properti
    $jenisPembayaran = $jenisPembayaran ?: $this->getJenisPembayaran();

    // Jika tidak ada jenis pembayaran, kembalikan data apa adanya
    if (empty($jenisPembayaran)) {
      Log::warning('Jenis pembayaran kosong, mengembalikan data apa adanya', ['data' => $data]);
      return $data;
    }

    // Ambil konfigurasi field untuk jenis pembayaran ini
    $fieldsConfig = config("hotel.info_pengirim.{$jenisPembayaran}", []);

    if (empty($fieldsConfig)) {
      Log::warning("Konfigurasi untuk jenis pembayaran '{$jenisPembayaran}' tidak ditemukan");
      return $data; // Kembalikan data apa adanya jika tidak ada konfigurasi
    }

    // Siapkan array untuk menyimpan data yang akan disimpan
    $dataToSave = [];

    // Loop melalui setiap konfigurasi field
    foreach ($fieldsConfig as $fieldConfig) {
      $nama = $fieldConfig['nama'] ?? null;
      $insert = $fieldConfig['insert'] ?? true;

      // Hanya simpan field yang memiliki 'insert' = true dan nilai ada di data
      if ($nama && $insert && array_key_exists($nama, $data)) {
        $dataToSave[$nama] = $data[$nama];
      }
    }

    // Jika tidak ada data yang tersaring, gunakan data asli
    if (empty($dataToSave)) {
      Log::warning('Tidak ada data yang tersaring, menggunakan data asli', ['data' => $data]);
      return $data;
    }

    // Log untuk debugging
    Log::info("Filter data untuk {$jenisPembayaran}:", [
      'config' => $fieldsConfig,
      'input' => $data,
      'output' => $dataToSave
    ]);

    return $dataToSave;
  }
}
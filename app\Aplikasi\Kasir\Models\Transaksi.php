<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperTransaksi
 */
class Transaksi extends Model
{
    protected $table = 'transaksi';
protected $fillable = [
    'toko_id',
 
    'reservasi_id',
    'penjualan_id',
 
    'produk_id',
    'nama_item',
    'harga_modal',
    'harga',
    'jumlah',
    'ket'
];

   public function penjualan()
    {
        return $this->belongsTo(Penjualan::class, 'penjualan_id', 'id');
    }

 protected static function boot()
    {
        parent::boot();
      
        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'KASIR'); 
         });

        static::creating(function ($model)   {   $model->jenis = 'KASIR';   });
        static::updating(function ($model)    {   $model->jenis = 'KASIR';    });
    }


public function totalHarga(): float
{
    return (float)($this->jumlah * $this->harga);
}


    public function hitungTotalHarga(): float
    {
        return (float) ($this->jumlah * $this->harga);
    }

 
    public function hitungTotalModal(): float
    {
        return (float) ($this->jumlah * $this->harga_modal);
    }


    public function hitungKeuntungan(): float
    {
        return $this->hitungTotalHarga() - $this->hitungTotalModal();
    }


    public function hitungPersentaseKeuntungan(): float
    {
        if ($this->hitungTotalModal() == 0) {
            return 0;
        }

        return ($this->hitungKeuntungan() / $this->hitungTotalModal()) * 100;
    }

 


}

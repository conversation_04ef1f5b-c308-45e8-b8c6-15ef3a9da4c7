<div class="my-0 border-b border-slate-200 bg-slate-200 py-0" id="tutorial_header" style="  ">
    <div class="mx-auto max-w-full px-4 sm:px-6 lg:px-8">
        <div class="flex h-12 items-center justify-between">
            <!-- Breadcrumb -->
            <div class="flex">
                <ol class="flex items-center space-x-2 text-sm">
                    <li>
                        <button id="toggle-topbar"
                            class="text-md ml-2 flex items-center rounded px-2 py-1 font-bold text-slate-500">
                            <x-heroicon-o-clock class="mr-1 h-4 w-4" />
                            <span id="realtime-clock">{{ now()->format('H:i:s') }}</span>
                        </button>
                    </li>
                    <li>
                        <button id="toggle-fullscreen"
                            class="text-md flex items-center rounded px-2 py-1 font-bold text-slate-500">
                            <x-heroicon-o-arrows-pointing-out class="h-4 w-4" />
                        </button>
                    </li>
                    {{-- <li>
                        <button id="toggle-topbar" wire:click.prevent="bukaTutorial"
                            class="text-md flex items-center rounded px-2 py-1 font-bold text-slate-500">
                            <x-heroicon-o-x-circle class="h-4 w-4" />Tutorial
                        </button>
                    </li>  --}}


                </ol>
            </div>

            <!-- Quick Actions -->

            <div class="flex items-center space-x-2">
                <!-- Perbaikan checkbox mode expert -->
                <label class="flex items-left  mr-4">
                    CHECK IN : <span class="ml-2 text-sm text-gray-700">
                      Tanggal check in - Tanggal check out
                    </span>
            </label>
            <span class="text-slate-300">|</span>
                <label class="flex items-center cursor-pointer">
                    <input type="checkbox" wire:model.live="isExpertMode" id="tutorial_expert"
                        class="form-checkbox h-4 w-4 text-primary-600 rounded border-gray-300 focus:ring-primary-500">
                    <span class="ml-2 text-sm text-gray-700">
                        Mode Expert
                    </span>
                </label>

                {{-- tombol draft transaksi --}}
                {{-- @if ($tampilanKasir['tampilkan_draft'] == 'ya')
            <span class="text-slate-300">|</span>
            <button id="btn-transaksi-draft"
              class="relative flex items-center text-xs text-slate-600 hover:text-emerald-600"
              wire:click="$dispatch('open-modal', { id: 'modal-draft' })">
              <x-heroicon-o-bars-4 class="mr-1 h-4 w-4" />
              Draft (F6)
              <span x-cloak x-show="true"
                class="absolute -left-2 -top-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white">
                <span x-text="{{ app(\App\Services\DraftService::class)->jumlahDraft() }}"></span>
              </span>
            </button>
          @endif --}}

                {{-- tombol transaksi selesai  --}}
                {{-- @if ($tampilanKasir['tampilkan_selesai'] == 'ya')
            <span class="text-slate-300">|</span>
  
            <button id="btn-transaksi-selesai"
              class="ml-2 flex items-center text-xs text-slate-600 hover:text-emerald-600"
              wire:click="$dispatch('open-modal', { id: 'modal-transaksiselesai' })">
              <x-heroicon-o-document-duplicate class="mr-1 h-4 w-4" />
              Selesai (F7)
            </button>
          @endif --}}

            </div>
        </div>
    </div>
</div>



<style>

 
  
    /* CSS untuk menyembunyikan topbar */
    .fi-topbar {
      transform: translateY(-100%);
      /* Sembunyikan topbar ke atas */
      transition: transform 0.3s ease;
      /* Animasi transisi */
      position: fixed;
      width: 100%;
      z-index: 50;
    }
  
    /* Saat topbar aktif/tampil */
    .fi-topbar.show-topbar {
      transform: translateY(0);
      /* Tampilkan topbar */
    }
  
    /* Tambahkan margin atas pada content utama */
    .topbar-hidden .fi-main {
      margin-top: 0 !important;
      transition: margin-top 0.3s ease;
      /* Animasi transisi */
    }
  
    .show-topbar-space .fi-main {
      margin-top: 4rem !important;
      /* Sesuaikan dengan tinggi topbar */
    }
  
    /* Pastikan header tetap terlihat */
    .my-0.border-b.border-slate-200.bg-slate-200.py-0 {
      position: sticky;
      top: 0;
      z-index: 40;
    }
  </style>
  
<!-- Script untuk jam real-time dan keyboard shortcut -->
<script>
    function updateClock() {
        var now = new Date();
        var hours = now.getHours().toString().padStart(2, '0');
        var minutes = now.getMinutes().toString().padStart(2, '0');
        var seconds = now.getSeconds().toString().padStart(2, '0');
        document.getElementById('realtime-clock').textContent = hours + ':' + minutes + ':' + seconds;
        setTimeout(updateClock, 1000);
    }
</script>

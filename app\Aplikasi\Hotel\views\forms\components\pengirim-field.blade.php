@php
    // Mendapatkan state saat ini dari field
    $statePath = $getStatePath();
    $state = $getState();
    
    // Pastikan state diproses dengan benar
    if (is_string($state) && Illuminate\Support\Str::isJson($state)) {
        $state = json_decode($state, true);
    }
    
    // Cek apakah state benar-benar kosong
    $isStateEmpty = true;
    if (is_array($state)) {
        foreach ($state as $value) {
            if (!empty($value)) {
                $isStateEmpty = false;
                break;
            }
        }
    } elseif (!empty($state)) {
        $isStateEmpty = false;
    }
    
    $action = $getMainAction();
    
    // Buat ID unik untuk elemen
    $uniqueId = "pengirim-field-" . \Illuminate\Support\Str::random(6);
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div wire:key="{{ $statePath }}_{{ Illuminate\Support\Str::random(8) }}">
        @if ($isStateEmpty)
            <div 
                id="{{ $uniqueId }}"
                class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200"
                onclick="document.querySelector('#{{ $uniqueId }} button').click()"
            >
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ $getTeksDataKosong() }}
                    </div>
                    <div style="opacity: 0.9">
                        {{ $action }}
                    </div>
                </div>
            </div>
        @else
            <div 
                id="{{ $uniqueId }}"
                class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200"
                onclick="document.querySelector('#{{ $uniqueId }} button').click()"
            >
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            Data Pengirim
                        </div>
                        <div style="opacity: 0.9">
                            {{ $action->label('Ubah Data') }}
                        </div>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-2">
                        @if (is_array($state))
                            <div class="grid grid-cols-1 gap-2">
                                @foreach ($state as $key => $value)
                                    @if (!empty($value))
                                    <div class="flex items-start py-1">
                                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400 w-1/3">
                                            {{ ucwords(str_replace('_', ' ', $key)) }}:
                                        </span>
                                        <span class="text-sm text-gray-900 dark:text-gray-100 w-2/3">
                                            {{ $value }}
                                        </span>
                                    </div>
                                    @endif
                                @endforeach
                            </div>
                        @else
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $state }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-dynamic-component>
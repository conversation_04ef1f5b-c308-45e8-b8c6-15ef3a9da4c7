<?php

namespace Modules\RajaMenu\Plugins;

use Filament\Contracts\Plugin;
use Filament\Panel;

class RajaMenuPlugin implements Plugin
{
    protected bool $enabled = true;
    protected bool $autoRender = true; // Auto-render navigation
    protected string $mode = 'auto'; // 'auto' or 'manual'
    protected array $manualNavigation = [];
    protected string $panelId = 'admin';
    protected array $autoDiscoveryConfig = [];
    // Lokasi render hook (default panels::page.header.actions.before)
    protected string $lokasi = 'panels::page.header.actions.before';

    public static function make(): static
    {
        return app(static::class);
    }

    public function getId(): string
    {
        return 'rajamenu';
    }

    /**
     * Enable or disable RajaMenu
     */
    public function enabled(bool $enabled = true): static
    {
        $this->enabled = $enabled;
        return $this;
    }

    /**
     * Enable or disable auto-render navigation
     */
    public function autoRender(bool $autoRender = true): static
    {
        $this->autoRender = $autoRender;
        return $this;
    }

    /**
     * Menentukan lokasi render hook Filament.
     * Contoh: ->lokasi('panels::global-search.before')
     */
    public function lokasi(string $lokasi = 'panels::page.header.actions.before'): static
    {
        $this->lokasi = $lokasi;
        return $this;
    }

    /**
     * Disable auto-render (for manual render hook usage)
     */
    public function manualRender(): static
    {
        $this->autoRender = false;
        return $this;
    }

    /**
     * Set navigation mode
     */
    public function mode(string $mode): static
    {
        $this->mode = $mode;
        return $this;
    }

    /**
     * Use auto-discovery mode
     */
    public function auto(): static
    {
        $this->mode = 'auto';
        return $this;
    }

    /**
     * Use manual mode with navigation items
     */
    public function manual(array $navigationItems = []): static
    {
        $this->mode = 'manual';
        $this->manualNavigation = $navigationItems;
        return $this;
    }

    /**
     * Set panel ID
     */
    public function panel(string $panelId): static
    {
        $this->panelId = $panelId;
        return $this;
    }

    /**
     * Configure auto-discovery settings
     */
    public function autoDiscovery(array $config): static
    {
        $this->autoDiscoveryConfig = $config;
        return $this;
    }
 
    public function addGroup(string $groupName, array $groupConfig): static
    {
        $this->manualNavigation[$groupName] = $groupConfig;
        return $this;
    }

 

    
    public function register(Panel $panel): void
    {
        if (!$this->enabled) {
            return;
        }

        // Set panel ID from panel
        $this->panelId = $panel->getId();

        // Register navigation configuration for this panel
        $this->registerNavigationConfig();

        // Register views and assets
        $this->registerViews($panel);
    }

    /**
     * Boot the plugin
     */
    public function boot(Panel $panel): void
    {
        if (!$this->enabled) {
            return;
        }

        // Additional boot logic if needed
    }

    /**
     * Register navigation configuration
     */
    protected function registerNavigationConfig(): void
    {
        // Set runtime configuration for this panel
        config([
            "rajamenu.navigation_mode" => $this->mode,
            "rajamenu.manual_navigation.{$this->panelId}.groups" => $this->manualNavigation,
        ]);

        if (!empty($this->autoDiscoveryConfig)) {
            config([
                "rajamenu.auto_discovery" => array_merge(
                    config('rajamenu.auto_discovery', []),
                    $this->autoDiscoveryConfig
                )
            ]);
        }
    }

    /**
     * Register views and assets
     */
    protected function registerViews(Panel $panel): void
    {
        // Register render hook hanya jika autoRender diaktifkan
        if ($this->autoRender) {
            $panel->renderHook(
                $this->lokasi,
                function() {
                    try {
                        return view('rajamenu::navigation.auto-flyout')->render();
                    } catch (\Exception $e) {
                        return '<div style="background: red; color: white; padding: 10px; margin: 10px 0; border-radius: 6px;">RajaMenu Error: ' . $e->getMessage() . '</div>';
                    }
                }
            );
        }
    }
}

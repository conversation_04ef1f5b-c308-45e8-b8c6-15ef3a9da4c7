<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureHasAdminPanelPermission
{
    /**
     * Menolak request jika user tidak memiliki permission akses panel admin.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Middleware Authenticate sudah dijalankan lebih dulu, jadi user seharusnya tidak null.
        $user = $request->user();

        // Jika tetap tidak ada user, lanjutkan alur normal (akan ditangani middleware Auth).
        if (!$user) {
            return $next($request);
        }

        // Izinkan super-admin (role yang didefinisikan di config) tanpa perlu permission khusus
        if ($user->hasRole(config('filament-shield.super_admin.name'))) {
            return $next($request);
        }

        // Cek permission khusus.
        if (!$user->hasPermissionTo('access_admin_panel')) {
            // Abort dengan status 403 (Forbidden) jika tidak punya izin.
            abort(403, 'Anda tidak memiliki izin untuk mengakses Panel Admin.');
        }

        return $next($request);
    }
} 
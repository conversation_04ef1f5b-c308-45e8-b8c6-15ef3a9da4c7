<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class EnsureHasAdminPanelPermission
{
    /**
     * Menolak request jika user tidak memiliki permission akses panel admin.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Middleware Authenticate sudah dijalankan lebih dulu, jadi user seharusnya tidak null.
        $user = $request->user();

        // Jika tetap tidak ada user, lanjutkan alur normal (akan ditangani middleware Auth).
        if (!$user) {
            return $next($request);
        }

        // Izinkan super-admin (role yang didefinisikan di config) tanpa perlu permission khusus
        $superAdminRole = config('filament-shield.super_admin.name', 'super_admin');
        if ($superAdminRole && $user->hasRole($superAdminRole)) {
            return $next($request);
        }

        // Cek permission khusus dengan error handling
        try {
            if (!$user->hasPermissionTo('access_admin_panel')) {
                // Abort dengan status 403 (Forbidden) jika tidak punya izin.
                abort(403, 'Anda tidak memiliki izin untuk mengakses Panel Admin.');
            }
        } catch (\Exception $e) {
            // Jika terjadi error saat cek permission, log error dan izinkan akses
            // (untuk menghindari aplikasi crash)
            Log::warning('Error checking admin panel permission: ' . $e->getMessage());
            // Bisa juga di-abort jika ingin lebih strict:
            // abort(500, 'Terjadi kesalahan sistem saat memeriksa izin akses.');
        }

        return $next($request);
    }
} 
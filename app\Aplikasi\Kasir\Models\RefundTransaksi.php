<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperRefundTransaksi
 */
class RefundTransaksi extends Model
{
    use HasFactory;
    protected $table = 'refund_transaksi';
    protected $fillable = ['toko_id','reservasi_id','penjualan_id','refund_id','transaksi_invoice','produk_id','nama_item','harga_modal','harga','jumlah','ket'];
	
	    public function refund()
    {
        return $this->belongsTo(Refund::class, 'refund_id', 'id');
    }
}

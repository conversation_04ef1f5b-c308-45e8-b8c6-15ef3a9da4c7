# RajaPicker - Custom FilamentPHP Field

RajaPicker adalah custom field FilamentPHP yang menggabungkan fungsi image picker dan image uploader dalam satu komponen yang user-friendly.

## Fitur Utama

### 🖼️ **Dual Functionality**
- **Image Picker**: Memilih gambar dari media library yang sudah ada
- **Image Uploader**: Upload gambar baru langsung dari field

### 🎨 **User Interface**
- Preview gambar yang dipilih/diupload
- Modal picker dengan grid layout untuk memilih gambar
- Drag & drop support untuk upload
- Responsive design yang konsisten dengan FilamentPHP

### ⚙️ **Konfigurasi Fleksibel**
- Support single dan multiple selection
- Kustomisasi collection media
- Validasi file type dan ukuran
- Preview size yang dapat disesuaikan

## Instalasi & Setup

### 1. File yang Dibuat
```
modules/Rajapicker/app/Filament/Forms/Components/RajaPicker.php
modules/Rajapicker/resources/views/components/forms/raja-picker.blade.php
app/Http/Controllers/Api/MediaController.php
```

### 2. API Routes
Routes API telah ditambahkan di `routes/api.php`:
```php
Route::prefix('media')->group(function () {
    Route::get('/{id}', [MediaController::class, 'show']);
    Route::get('/by-ids', [MediaController::class, 'getByIds']);
    Route::get('/collection/{collection}', [MediaController::class, 'getByCollection']);
    Route::post('/upload', [MediaController::class, 'upload']);
});
```

## Cara Penggunaan

### Basic Usage
```php
use Modules\Rajapicker\Filament\Forms\Components\RajaPicker;

RajaPicker::make('gambar')
    ->label('Pilih Gambar')
```

### Advanced Configuration
```php
RajaPicker::make('featured_image')
    ->label('Gambar Utama')
    ->collection('cms')                    // Media collection
    ->previewSize(200)                     // Preview size in pixels
    ->maxFileSize(10)                      // Max file size in MB
    ->acceptedFileTypes([                  // Accepted file types
        'image/jpeg', 
        'image/png', 
        'image/webp'
    ])
    ->placeholder('Pilih atau upload gambar utama')
    ->showFileName(true)                   // Show file name
    ->showFileSize(true)                   // Show file size
    ->enablePicker(true)                   // Enable picker functionality
    ->enableUploader(true)                 // Enable uploader functionality
```

### Multiple Selection
```php
RajaPicker::make('galeri')
    ->label('Galeri Foto')
    ->collection('gallery')
    ->multiple()                           // Enable multiple selection
    ->previewSize(150)
    ->placeholder('Pilih atau upload foto untuk galeri')
```

### Conditional Visibility
```php
RajaPicker::make('banner')
    ->label('Banner')
    ->collection('banners')
    ->visible(fn($get) => $get('type') === 'homepage')
    ->columnSpanFull()
```

## Konfigurasi Method

### File & Validation
- `acceptedFileTypes(array $types)` - Set accepted MIME types
- `maxFileSize(int $size)` - Set max file size in MB
- `collection(string $collection)` - Set media collection

### Selection Mode
- `multiple(bool $multiple = true)` - Enable multiple selection
- `directory(string $directory)` - Set upload directory

### UI Customization
- `previewSize(int $size)` - Set preview image size in pixels
- `placeholder(string $placeholder)` - Set placeholder text
- `showFileName(bool $show = true)` - Show/hide file name
- `showFileSize(bool $show = true)` - Show/hide file size

### Feature Toggle
- `enablePicker(bool $enable = true)` - Enable/disable picker
- `enableUploader(bool $enable = true)` - Enable/disable uploader

## Contoh Implementasi

### 1. CMS Resource
```php
// Gambar utama artikel
RajaPicker::make('rajajson.featured_image')
    ->label('Gambar Utama')
    ->collection('cms')
    ->previewSize(200)
    ->placeholder('Pilih atau upload gambar utama untuk konten ini')
    ->visible(fn($get) => in_array($get('jenis'), ['ARTIKEL', 'HALAMAN', 'ACARA']))
    ->columnSpanFull(),

// Galeri foto
RajaPicker::make('rajajson.galeri')
    ->label('Galeri Foto')
    ->collection('gallery')
    ->multiple()
    ->previewSize(150)
    ->placeholder('Pilih atau upload foto untuk galeri')
    ->visible(fn($get) => $get('jenis') == 'GALERI')
    ->columnSpanFull(),
```

### 2. Product Resource
```php
RajaPicker::make('product_images')
    ->label('Foto Produk')
    ->collection('products')
    ->multiple()
    ->maxFileSize(5)
    ->previewSize(120)
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->placeholder('Upload foto produk (max 5MB)')
```

### 3. User Profile
```php
RajaPicker::make('avatar')
    ->label('Foto Profil')
    ->collection('avatars')
    ->previewSize(100)
    ->maxFileSize(2)
    ->placeholder('Upload foto profil')
    ->showFileSize(false)
```

## Integrasi dengan Model

RajaPicker terintegrasi dengan model Media yang sudah ada dan menggunakan Spatie Media Library. Data yang disimpan berupa:

- **Single selection**: ID media (integer)
- **Multiple selection**: Array of media IDs

### Mengakses Media di Model
```php
// Single media
$media = Media::find($record->featured_image);
$imageUrl = $media ? $media->url : null;

// Multiple media
$mediaIds = $record->galeri ?? [];
$mediaCollection = Media::whereIn('id', $mediaIds)->get();
```

## Teknologi yang Digunakan

- **Backend**: Laravel 11, FilamentPHP 3.2, Spatie Media Library
- **Frontend**: Alpine.js, Tailwind CSS
- **File Upload**: Native HTML5 File API dengan FormData
- **Modal**: Tailwind CSS dengan Alpine.js transitions

## Troubleshooting

### 1. API Routes tidak berfungsi
Pastikan routes API sudah ditambahkan dan controller sudah dibuat dengan benar.

### 2. Upload gagal
- Periksa permission direktori storage
- Pastikan max file size sesuai dengan konfigurasi server
- Cek log Laravel untuk error detail

### 3. Preview tidak muncul
- Pastikan model Media memiliki accessor `url` yang benar
- Periksa path file di storage

### 4. JavaScript error
- Pastikan Alpine.js sudah loaded
- Cek console browser untuk error detail
- Pastikan CSRF token tersedia

## Keunggulan RajaPicker

1. **Reusable**: Dapat digunakan di berbagai resource
2. **Flexible**: Konfigurasi yang sangat fleksibel
3. **User-friendly**: Interface yang intuitif dan modern
4. **Integrated**: Terintegrasi penuh dengan ecosystem FilamentPHP
5. **Performant**: Optimized untuk performa dengan lazy loading
6. **Responsive**: Design yang responsive di semua device

## Roadmap

- [ ] Support untuk video dan audio files
- [ ] Bulk upload dengan progress indicator
- [ ] Image cropping dan editing
- [ ] Drag & drop reordering untuk multiple selection
- [ ] Integration dengan cloud storage (S3, etc.)

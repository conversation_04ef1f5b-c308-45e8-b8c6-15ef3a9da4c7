<?php

namespace App\Aplikasi\Hotel\Models;


use App\Aplikasi\Hotel\Models\Reservasi;
use App\Models\Tamu as TamuUtama;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperTamu
 */
class Tamu extends TamuUtama
{
    use HasFactory;



    // public function penjualan(): HasMany
    // {
    //     return $this->hasMany(Penjualan::class, 'tamu_id');
    // }
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'HOTEL');
        });

        static::creating(function ($model) {
            $model->jenis = 'HOTEL';
        });

        static::updating(function ($model) {
            $model->jenis = 'HOTEL';
        });
    }


    public function reservasi(): HasMany
    {
        return $this->hasMany(Reservasi::class);
    }


    public function hitungKunjungan(): int
    {
        return $this->reservasi()->count();
    }


    public function hitungTotalBelanja(): float
    {
        return $this->penjualan()
            ->where('status', 'SELESAI')
            ->sum('jumlah_pembayaran');
    }


    public function getFotoIdentitasUrl(): ?string
    {
        if (empty($this->foto_identitas)) {
            return null;
        }

        // Jika foto sudah berupa URL lengkap
        if (filter_var($this->foto_identitas, FILTER_VALIDATE_URL)) {
            return $this->foto_identitas;
        }

        // Jika foto disimpan di storage
        return asset('storage/' . $this->foto_identitas);
    }


    public function getNamaFormat(): string
    {
        return ucwords(strtolower($this->nama));
    }


    public function hasInfoKontakLengkap(): bool
    {
        return !empty($this->email) && !empty($this->telpon);
    }

    public function hasIdentitasLengkap(): bool
    {
        return !empty($this->jenis_identitas) && !empty($this->no_identitas);
    }
}
# WebP Converter Service

Service untuk mengkonversi gambar ke format WebP dengan berbagai opsi konfigurasi.

## Fitur Utama

- Konversi gambar ke format WebP
- Resize gambar dengan maintain aspect ratio
- Konversi multiple gambar sekaligus
- Konversi gambar dari URL
- Quality control (0-100)
- Compression ratio calculation
- Error handling dan logging
- Validasi file dan tipe gambar

## Cara Penggunaan

### 1. Konversi Single Image

```php
use Modules\Rajapicker\Services\WebPConverterService;

$converter = new WebPConverterService();

// Konversi dengan default options
$result = $converter->convertToWebP('/path/to/image.jpg');

// Konversi dengan custom options
$result = $converter->convertToWebP('/path/to/image.jpg', '/path/to/output.webp', [
    'quality' => 85,
    'width' => 800,
    'height' => 600,
    'maintain_aspect_ratio' => true,
    'background_color' => '#FFFFFF',
    'compression_method' => 6
]);
```

### 2. Konversi Multiple Images

```php
$sourcePaths = [
    '/path/to/image1.jpg',
    '/path/to/image2.png',
    '/path/to/image3.gif'
];

$results = $converter->convertMultipleToWebP($sourcePaths, '/path/to/output/directory', [
    'quality' => 80,
    'width' => 1200
]);
```

### 3. Konversi dari URL

```php
$result = $converter->convertUrlToWebP(
    'https://example.com/image.jpg',
    '/path/to/local/output.webp',
    ['quality' => 90]
);
```

### 4. Cek Informasi Gambar

```php
$info = $converter->getImageInfo('/path/to/image.jpg');
// Returns: ['width', 'height', 'mime_type', 'file_size', 'file_size_bytes', 'aspect_ratio']
```

### 5. Validasi Gambar

```php
$isValid = $converter->isValidImage('/path/to/image.jpg');
$isWebPSupported = $converter->isWebPSupported();
```

## Opsi Konfigurasi

| Parameter | Type | Default | Deskripsi |
|-----------|------|---------|-----------|
| `quality` | int | 80 | Kualitas WebP (0-100) |
| `width` | int|null | null | Lebar gambar (pixel) |
| `height` | int|null | null | Tinggi gambar (pixel) |
| `maintain_aspect_ratio` | bool | true | Pertahankan aspect ratio saat resize |
| `background_color` | string | '#FFFFFF' | Warna background untuk gambar transparan |
| `preserve_metadata` | bool | false | Pertahankan metadata gambar |
| `compression_method` | int | 6 | Metode kompresi (0-6, semakin tinggi semakin lambat) |

## Response Format

### Success Response
```php
[
    'success' => true,
    'original_path' => '/path/to/original.jpg',
    'webp_path' => '/path/to/output.webp',
    'original_size' => '2.5 MB',
    'webp_size' => '500 KB',
    'compression_ratio' => 80.0, // Persentase pengurangan ukuran
    'quality' => 80,
    'dimensions' => [
        'width' => 1920,
        'height' => 1080
    ]
]
```

### Error Response
```php
[
    'success' => false,
    'error' => 'Error message',
    'original_path' => '/path/to/original.jpg',
    'destination_path' => '/path/to/output.webp'
]
```

## Integrasi dengan RajaPicker

Service ini dapat diintegrasikan dengan komponen RajaPicker untuk otomatis mengkonversi gambar ke WebP saat upload:

```php
// Di controller atau service upload
if ($rajaPicker->shouldConvertWebp()) {
    $converter = new WebPConverterService();
    $result = $converter->convertToWebP($uploadedFilePath, null, [
        'quality' => 80
    ]);
    
    if ($result['success']) {
        // Gunakan file WebP yang sudah dikonversi
        $webpPath = $result['webp_path'];
    }
}
```

## Requirements

- PHP 8.0+
- Intervention Image v3
- GD extension dengan WebP support
- cURL extension (untuk konversi dari URL)

## Error Handling

Service ini menangani berbagai error:
- File tidak ditemukan
- Tipe file tidak didukung
- WebP tidak didukung oleh server
- Gagal download dari URL
- Memory limit exceeded
- Permission denied

Semua error akan di-log dan return response dengan format yang konsisten. 
{"name": "nwidart/rajagambar", "description": "<PERSON><PERSON><PERSON> untuk pemrosesan gambar menggunakan Spatie Image", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"spatie/image": "^3.8"}, "extra": {"laravel": {"providers": ["Modules\\RajaGambar\\Providers\\SpatieServiceProvider"], "aliases": {}}}, "autoload": {"psr-4": {"Modules\\RajaGambar\\": "app/", "Modules\\RajaGambar\\Database\\Factories\\": "database/factories/", "Modules\\RajaGambar\\Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Modules\\RajaGambar\\Tests\\": "tests/"}}}
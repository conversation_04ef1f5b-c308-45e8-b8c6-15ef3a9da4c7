<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Forms;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;

class SeoForm
{
    public static function make(): Section
    {
        return Section::make('SEO')
            
            ->collapsible()
            ->collapsed()
            ->schema([
                TextInput::make('jcol.seo.keywords')
                    ->label('Keywords')
                    ->placeholder('Masukkan keywords, pisahkan dengan koma')
                    ->helperText('Maksimal 60 karakter , gunakan koma untuk memisahkan '),

                // Deskripsi
                Textarea::make('jcol.seo.description')
                    ->label('Deskripsi')
                    ->placeholder('Masukkan deskripsi')
                    ->maxLength(160)
                    ->helperText('Maksimal 160 karakter'),
            ]);
    }
}

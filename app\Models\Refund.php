<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperRefund
 */
class Refund extends Model
{
    use HasFactory;
    public function getTable()
    {
         return config('tabel.t_refund.nama_tabel', 'produk');
    }
 
    public function getFillable()
    {
         return config('tabel.t_refund.kolom', []);
    }

    public function refundtransaksi()
    {
        return $this->hasMany(RefundTransaksi::class, 'refund_id', 'id');
    }

    public function penjualan()
    {
        return $this->belongsTo(Penjualan::class, 'penjualan_id', 'id');
    }
}

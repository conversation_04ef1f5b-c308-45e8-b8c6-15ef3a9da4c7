<?php

namespace App\Aplikasi\Hotel\Resources;

use App\Filament\Resources\KonfigResource as BaseKonfigResource;
use App\Aplikasi\Hotel\Models\Konfig;
use App\Aplikasi\Hotel\Resources\KonfigResource\Pages;

class KonfigResource extends BaseKonfigResource
{
    protected static ?string $model = Konfig::class;

    // Konfigurasi navigasi
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationLabel = 'Konfigurasi Hotel';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $slug = 'pengaturan/konfigurasi';
    protected static ?string $pluralModelLabel = 'Konfigurasi Hotel';
    protected static bool $shouldRegisterNavigation = false;

    // Tidak perlu override getEloquentQuery karena model sudah memiliki global scope

    // Override metode getPages untuk menggunakan halaman khusus KonfigResource
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKonfigs::route('/'),
            'create' => Pages\CreateKonfig::route('/create'),
            'view' => Pages\ViewKonfig::route('/{record}'),
            'edit' => Pages\EditKonfig::route('/{record}/edit'),
        ];
    }
}

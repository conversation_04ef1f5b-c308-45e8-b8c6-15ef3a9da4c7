<table class="w-full border-collapse border min-w-full table-fixed">
    <thead>
        <tr>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Min</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Sen</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Sel</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Rab</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Kam</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Jum</th>
            <th class="p-1 sm:p-2 border text-center bg-gray-50 text-xs sm:text-sm">Sab</th>
        </tr>
    </thead>
    <tbody>
        @php
            $today = Carbon\Carbon::now();
            $date = Carbon\Carbon::createFromDate($tahun, $bulan, 1);
            $daysInMonth = $date->daysInMonth;

            // Menggunakan 0 (Minggu) - 6 (Sabtu)
            $firstDayOfWeek = $date->copy()->startOfMonth()->dayOfWeek;
            $lastDayOfMonth = $date->copy()->endOfMonth()->day;

            $day = 1;
            $nextMonthDay = 1;

            $previousMonth = $date->copy()->subMonth();
            $previousMonthDays = $previousMonth->daysInMonth;
            $previousMonthDay = $previousMonthDays - $firstDayOfWeek + 1;

            if ($firstDayOfWeek == 0) {
                $previousMonthDay = $previousMonthDays - 6;
            }
        @endphp

        @for ($i = 0; $i < 6; $i++)
            <tr>
                @for ($j = 0; $j < 7; $j++)
                    @if ($i === 0 && $j < $firstDayOfWeek)
                        <td class="p-1 sm:p-2 border h-12 sm:h-20 md:h-24 align-top bg-gray-100">
                            <div class="text-gray-400 text-right text-xs sm:text-sm">
                                {{ $previousMonthDay }}  </div>
                            @php $previousMonthDay++; @endphp
                        </td>
                    @elseif ($day > $daysInMonth)
                        <td class="p-1 sm:p-2 border h-12 sm:h-20 md:h-24 align-top bg-gray-100">
                            <div class="text-gray-400 text-right text-xs sm:text-sm">{{ $nextMonthDay }}
                            </div>
                            @php $nextMonthDay++; @endphp
                        </td>
                    @else
                        <td wire:click="pilihHari({{ $day }})"
                            class="p-1 sm:p-2 border h-12 sm:h-20 md:h-24 align-top cursor-pointer hover:bg-biru-300 transition-colors 
{{ $today->day == $day && $today->month == $bulan && $today->year == $tahun ? 'bg-kuning-300' : '' }}
{{ $hariTerpilih == $day ? 'bg-primary-500 scale-95' : '' }} transform transition-all duration-200">
                            <div
                                class="font-medium text-right text-xs sm:text-sm {{ $today->day == $day && $today->month == $bulan && $today->year == $tahun ? 'text-blue-600' : '' }}">
                                {{ $day }}</div>

                                @if (isset($kamarTersedia[$day]))
                                <div class="mt-1 space-y-1">
                                    @foreach ($kamarTersedia[$day] as $kamarInfo)
                                        <div class="p-0.5 sm:p-1 text-xs rounded truncate border bg-abu-500 border-biru-500   relative group overflow-hidden">
                                            <!-- Progress bar background -->
                                            <div class="absolute top-0 left-0 h-full 
                                                {{ $kamarInfo['tersedia'] > 0 ? 'bg-biru-500' : 'bg-merah-500' }}"
                                                style="width: {{ ($kamarInfo['tersedia'] / $kamarInfo['total']) * 100 }}%;"></div>
                                            
                                            <!-- Text content (di atas progress bar) -->
                                            <span class="relative z-10 text-white">
                                                {{ $kamarInfo['tipe_nama'] }}: {{ $kamarInfo['tersedia'] }}/{{ $kamarInfo['total'] }}
                                            </span>
                                            
                                            <div class="absolute hidden group-hover:block bg-gray-800 text-white text-xs rounded p-2 z-20 w-48 bottom-full left-0 mb-1 shadow-lg">
                                                <div class="font-bold">{{ $kamarInfo['tipe_nama'] }}</div>
                                                <div>Total: {{ $kamarInfo['total'] }} kamar</div>
                                                <div>Tersedia: {{ $kamarInfo['tersedia'] }} kamar</div>
                                                <div>Terpakai: {{ $kamarInfo['total'] - $kamarInfo['tersedia'] }} kamar</div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                            @php $day++; @endphp
                        </td>
                    @endif
                @endfor
            </tr>
            @if ($day > $daysInMonth && $i < 5)
                @php break; @endphp
            @endif
        @endfor
    </tbody>
</table>

keterangan : Tipe kamar : (jumlah tersedia / Jumlah total kamar)
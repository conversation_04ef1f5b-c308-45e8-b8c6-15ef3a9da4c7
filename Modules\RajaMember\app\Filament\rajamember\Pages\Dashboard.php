<?php

namespace Modules\RajaMember\Filament\rajamember\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Mo<PERSON>les\RajaMember\Filament\rajamember\Widgets\UserInfoWidget;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    protected static string $view = 'rajamember::filament.pages.dashboard';
    
    protected static ?string $title = 'Dashboard Member';
    
    protected static ?string $navigationLabel = 'Dashboard';
    
    protected static ?int $navigationSort = -10;
    // protected static string $routePath = 'member';


    
    public function getColumns(): int | string | array
    {
        return [
            'md' => 2,
            'xl' => 3,
        ];
    }
    
    public function getWidgets(): array
    {
        return [
            UserInfoWidget::class,
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperToko
 */
class Toko extends Model
{
    protected $fillable = [
        'id',
        'kode',
        'lokasi',
        'sub',
        'jenis',
        'nama',
        'slug',
        'alamat',
        'telpon',
        'email',
        'ket',
        'foto',
        'logo',
        'favicon',
        'kasir',
        'tgl_mulai',
        'tgl_selesai',
        'sewa',
        'user_id',
    ];

    public function getTable()
    {
         return config('tabel.t_toko.nama_tabel', 'toko');
    }

    public function getFillable()
    {
         return config('tabel.t_toko.kolom', []);
    }


public static function info()
{
    $toko = Toko::first();
    return $toko;
}

public function user()
{
    return $this->belongsTo(User::class, 'user_id');
}

}

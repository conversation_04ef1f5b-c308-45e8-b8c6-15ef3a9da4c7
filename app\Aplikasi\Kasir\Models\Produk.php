<?php

namespace App\Aplikasi\Kasir\Models;

use App\Models\Produk as ProdukUtama;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperProduk
 */
class Produk extends ProdukUtama
{
    protected $table = 'produk';



    protected $fillable = [

        'jenis',
        'toko_id',
        'sub',
        'kategori_id',
        'barcode',
        'nama',
        'harga',
        'harga_modal',
        'stok',
        'gambar',
        'ket',
        'fasilitas',
        'spesifikasi',
        'tampil',
        'inivarian',
        'created_at',
        'updated_at',
    ];




    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'PRODUK');
        });

        static::creating(function ($model) {
            $model->jenis = 'PRODUK';
        });
        static::updating(function ($model) {
            $model->jenis = 'PRODUK';
        });
    }

    public function induk(): BelongsTo
    {
        return $this->belongsTo(ProdukUtama::class, 'sub');
    }

    public function varian()
    {
        return $this->hasMany(ProdukUtama::class, 'sub', 'id');
    }

    public function punyaVarian(): bool
    {
        return $this->inivarian && $this->varian()->count() > 0;
    }


    public function kategori(): BelongsTo
    {
        return $this->belongsTo(ProdukKategori::class, 'kategori_id');
    }

    public function getPriceRangeAttribute()
    {
        if ($this->inivarian && $this->varian->count() > 0) {
            return [
                'min' => $this->varian->min('harga'),
                'max' => $this->varian->max('harga')
            ];
        }
        return ['min' => $this->harga, 'max' => $this->harga];
    }

    public function HargaJual()
    {
        return $this->hasMany(HargaJual::class, 'produk_id');
    }

    public function getVarianHargaAttribute()
    {
        return $this->HargaJual()->count() > 0;
    }

}

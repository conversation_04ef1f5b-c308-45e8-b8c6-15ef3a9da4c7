<?php

namespace Modules\RajaCms\Filament\Resources\CmsResource\Pages;

use Modules\RajaCms\Filament\Resources\CmsResource;
use Filament\Resources\Pages\Page;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use App\Facades\Shortcode;
use Illuminate\Support\Str;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

use BezhanSalleh\FilamentShield\Traits\HasPageShield;

class Tema extends Page  


{

    use HasPageShield;
    protected static string $resource = CmsResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';
    protected static ?string $navigationLabel = 'Pengaturan Tema';
    protected static ?string $title = 'Pengaturan Tema';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationGroup = 'Cms';
    protected static string $view = 'pages.tema';
    protected static bool $shouldRegisterNavigation = true;


    public ?array $data = [];
    public string $namaTema = '';
    public string $activeTab = 'daftar-tema';

    // Explorer properties
    public string $currentPath = '';
    public string $currentTheme = '';
    public ?string $fileFilter = null;
    public string $viewMode = 'grid';
 


    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'publish'
        ];
    }

    public function mount(): void
    {
        // Ambil tab dari parameter URL jika ada
        $tab = request()->query('tab');
        if ($tab && in_array($tab, ['daftar-tema', 'explorer', 'buat-tema'])) {
            $this->activeTab = $tab;
        }

        // Initialize Explorer properties
        $this->currentTheme = Config::get('tema.aktif', 'default');
        $this->currentPath = $this->sanitizePath(request()->query('path', ''));
        $this->fileFilter = request()->query('filter');
        $this->viewMode = request()->query('view', 'grid');

        // Validasi tema aktif
        if (!File::exists($this->getThemePath())) {
            $this->currentTheme = 'default';
        }

        $this->form->fill();
    }

    

    public function setActiveTab(string $tab): void
    {
        if (in_array($tab, ['daftar-tema', 'explorer', 'buat-tema'])) {
            $this->activeTab = $tab;

            // Buat URL dengan parameter tab dan pertahankan parameter lain
            $query = request()->query();
            $query['tab'] = $tab;

            $this->redirect(url()->current() . '?' . http_build_query($query));
        }
    }

  

    public function form(Form $form): Form
    {
        // Form tidak lagi digunakan karena tampilan sekarang menggunakan Blade
        return $form->schema([])->statePath('data');
    }

    public function buatTema(): void
    {
        // Validasi
        if (empty($this->data['namaTema'])) {
            Notification::make()
                ->title('Nama tema tidak boleh kosong')
                ->danger()
                ->send();
            return;
        }

        $namaTema = $this->data['namaTema'];

        // Validasi format nama (menggunakan aturan alpha_dash)
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $namaTema)) {
            Notification::make()
                ->title('Format nama tema tidak valid')
                ->body('Nama tema hanya boleh berisi huruf, angka, garis bawah, dan tanda hubung.')
                ->danger()
                ->send();
            return;
        }

        $pathTema = public_path('tema/' . $namaTema);

        // Cek apakah tema sudah ada
        if (File::exists($pathTema)) {
            Notification::make()
                ->title('Tema sudah ada')
                ->danger()
                ->send();
            return;
        }

        // Buat folder tema dan subdirektori yang diperlukan
        File::makeDirectory($pathTema, 0755, true);
        File::makeDirectory($pathTema . '/artikel', 0755, true);
        File::makeDirectory($pathTema . '/layout', 0755, true);
        File::makeDirectory($pathTema . '/modul', 0755, true);

        // Buat file-file tema
        $this->buatFileTema($namaTema);

        // Reset form
        $this->data['namaTema'] = '';

        Notification::make()
            ->title('Tema berhasil dibuat')
            ->success()
            ->send();
    }
    
    public function aktifkanTema(string $tema): void
    {
        // Periksa apakah tema ada
        if (!File::exists(public_path('tema/' . $tema))) {
            Notification::make()
                ->title('Tema tidak ditemukan')
                ->danger()
                ->send();
            return;
        }

        // Update file konfigurasi
        $configPath = config_path('tema.php');
        $configContent = "<?php\n\nreturn [\n    'aktif' => '{$tema}',\n    'path' => 'tema',\n];";
        File::put($configPath, $configContent);

        // Refresh config cache
        Config::set('tema.aktif', $tema);

        $this->form->fill();

        Notification::make()
            ->title('Tema ' . $tema . ' berhasil diaktifkan')
            ->success()
            ->send();
    }

    protected function scanFolder(): array
    {
        $temaPath = public_path('tema');

        if (!File::exists($temaPath)) {
            File::makeDirectory($temaPath, 0755, true);

            // Buat tema default jika tidak ada
            $this->buatFileTema('default');
        }

        $folderTema = File::directories($temaPath);

        return array_map(function ($path) {
            return basename($path);
        }, $folderTema);
    }

    protected function getConfigTema(string $tema): array
    {
        $configPath = public_path('tema/' . $tema . '/config.php');

        if (File::exists($configPath)) {
            $config = include $configPath;
            if (is_array($config)) {
                return $config;
            }
        }

        return [
            'id' => $tema,
            'nama' => $tema,
            'deskripsi' => 'Tidak ada deskripsi',
            'gambar' => '',
        ];
    }

    // ===== EXPLORER METHODS =====

    /**
     * Mendapatkan path lengkap ke direktori tema aktif
     */
    protected function getThemePath(): string
    {
        return public_path('tema/' . $this->currentTheme);
    }

    /**
     * Mendapatkan path lengkap ke direktori saat ini
     */
    protected function getCurrentFullPath(): string
    {
        $basePath = $this->getThemePath();

        if (empty($this->currentPath)) {
            return $basePath;
        }

        return $basePath . '/' . $this->currentPath;
    }



    /**
     * Mendapatkan breadcrumb untuk navigasi
     */
    public function getNavigationBreadcrumb(): array
    {
        $parts = [];
        $path = '';

        // Tambahkan root
        $parts[] = [
            'name' => $this->currentTheme,
            'path' => '',
            'isLast' => empty($this->currentPath)
        ];

        if (!empty($this->currentPath)) {
            $segments = explode('/', $this->currentPath);
            $count = count($segments);

            foreach ($segments as $index => $segment) {
                $path .= ($path ? '/' : '') . $segment;
                $parts[] = [
                    'name' => $segment,
                    'path' => $path,
                    'isLast' => $index === $count - 1
                ];
            }
        }

        return $parts;
    }

    protected function formatFileSize($size)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = 0;

        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }

        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Mendapatkan daftar file dan folder di direktori saat ini
     */
    public function getItems(): array
    {
        // Validasi keamanan path
        if (!$this->isPathSafe()) {
            return [
                'directories' => [],
                'files' => []
            ];
        }

        $currentPath = $this->getCurrentFullPath();

        if (!File::exists($currentPath)) {
            return [
                'directories' => [],
                'files' => []
            ];
        }

        $items = File::files($currentPath);
        $directories = File::directories($currentPath);

        // Format direktori
        $formattedDirectories = [];
        foreach ($directories as $directory) {
            $dirName = basename($directory);
            $relativePath = $this->currentPath
                ? $this->currentPath . '/' . $dirName
                : $dirName;

            $formattedDirectories[] = [
                'name' => $dirName,
                'path' => $relativePath,
                'fullPath' => $directory,
                'isDirectory' => true,
                'modified' => date('d M Y H:i', File::lastModified($directory)),
                'size' => $this->countItems($directory) . ' items',
            ];
        }

        // Format file
        $formattedFiles = [];
        foreach ($items as $item) {
            $fileName = $item->getFilename();
            $extension = strtolower($item->getExtension());

            // Filter berdasarkan ekstensi jika ada
            if ($this->fileFilter) {
                if ($this->fileFilter === 'blade' && !Str::endsWith(strtolower($fileName), '.blade.php')) {
                    continue;
                } elseif ($this->fileFilter !== 'blade' && $extension !== strtolower($this->fileFilter)) {
                    continue;
                }
            }

            $relativePath = $this->currentPath
                ? $this->currentPath . '/' . $fileName
                : $fileName;

            $formattedFiles[] = [
                'name' => $fileName,
                'path' => $relativePath,
                'fullPath' => $item->getPathname(),
                'isDirectory' => false,
                'extension' => $extension,
                'modified' => date('d M Y H:i', $item->getMTime()),
                'size' => $this->formatFileSize($item->getSize()),
                'isEditable' => $this->isFileEditable($fileName),
            ];
        }

        // Urutkan direktori dan file berdasarkan nama
        usort($formattedDirectories, fn($a, $b) => strcmp($a['name'], $b['name']));
        usort($formattedFiles, fn($a, $b) => strcmp($a['name'], $b['name']));

        return [
            'directories' => $formattedDirectories,
            'files' => $formattedFiles
        ];
    }

    /**
     * Menghitung jumlah item dalam direktori
     */
    protected function countItems(string $directory): int
    {
        $fileCount = count(File::files($directory));
        $dirCount = count(File::directories($directory));
        return $fileCount + $dirCount;
    }

    /**
     * Cek apakah file dapat diedit
     */
    protected function isFileEditable(string $fileName): bool
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Cek untuk file .blade.php
        if (Str::endsWith(strtolower($fileName), '.blade.php')) {
            return true;
        }

        $editableExtensions = ['php', 'html', 'css', 'js', 'json', 'txt', 'md'];
        return in_array($extension, $editableExtensions);
    }

    /**
     * Mendapatkan URL untuk mengedit file
     */
    public function getEditUrl(string $path): string
    {
        $fileName = basename($path);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        $fileNameWithoutExt = pathinfo($path, PATHINFO_FILENAME);

        // Daftar ekstensi yang didukung
        $supportedExtensions = ['php', 'js', 'css', 'json', 'txt', 'html', 'md'];

        if (Str::endsWith(strtolower($fileName), '.blade.php')) {
            // Untuk file blade, gunakan tema-editor dengan GrapesJS
            $bladeName = str_replace('.blade.php', '', $fileName);

            // Cek apakah file ada di direktori modul
            if (Str::contains($path, '/modul/')) {
                return url('/admin/cms/modul-editor?nama=' . $bladeName);
            } else {
                return url('/admin/cms/tema-editor?namafile=' . $bladeName);
            }
        } elseif (in_array($extension, $supportedExtensions)) {
            // Untuk file dengan ekstensi yang didukung, gunakan tema-editor dengan Monaco
            // Konversi full path ke relative path dari tema
            $themePath = $this->getThemePath();
            $relativePath = str_replace($themePath . '/', '', $path);
            $relativePath = str_replace($themePath . '\\', '', $relativePath); // Windows compatibility
            $relativePath = str_replace('\\', '/', $relativePath); // Normalize path separators

            return url('/admin/cms/tema-editor?namafile=' . $fileNameWithoutExt . '&type=' . $extension . '&path=' . urlencode($relativePath));
        }

        // Default URL untuk file yang tidak didukung
        return '#';
    }

    /**
     * Mengubah mode tampilan
     */
    public function setViewMode(string $mode): void
    {
        if (in_array($mode, ['grid', 'list'])) {
            $this->viewMode = $mode;

            // Perbarui URL dengan parameter view
            $query = request()->query();
            $query['view'] = $mode;

            $this->redirect(url()->current() . '?' . http_build_query($query));
        }
    }

    /**
     * Mengatur filter file
     */
    public function setFileFilter(?string $filter): void
    {
        $this->fileFilter = $filter;

        // Perbarui URL dengan parameter filter
        $query = request()->query();

        if ($filter) {
            $query['filter'] = $filter;
        } else {
            unset($query['filter']);
        }

        $this->redirect(url()->current() . '?' . http_build_query($query));
    }

    /**
     * Navigasi ke direktori
     */
    public function navigateToDirectory(string $path): void
    {
        $this->currentPath = $path;

        // Perbarui URL dengan path baru
        $query = request()->query();
        $query['path'] = $path;

        $this->redirect(url()->current() . '?' . http_build_query($query));
    }

    /**
     * Kembali ke direktori parent
     */
    public function goBack(): void
    {
        if (empty($this->currentPath)) {
            return;
        }

        $pathParts = explode('/', $this->currentPath);
        array_pop($pathParts);

        $newPath = implode('/', $pathParts);
        $this->navigateToDirectory($newPath);
    }

    /**
     * Mendapatkan statistik direktori
     */
    public function getDirectoryStats(): array
    {
        $items = $this->getItems();

        return [
            'directories' => count($items['directories']),
            'files' => count($items['files']),
            'total' => count($items['directories']) + count($items['files'])
        ];
    }

    /**
     * Sanitasi path untuk keamanan
     */
    protected function sanitizePath(string $path): string
    {
        // Hapus karakter berbahaya
        $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);

        // Hapus karakter yang tidak diinginkan
        $path = preg_replace('/[^a-zA-Z0-9\/_-]/', '', $path);

        // Hapus slash di awal dan akhir
        $path = trim($path, '/\\');

        return $path;
    }

    /**
     * Validasi apakah path aman untuk diakses
     */
    protected function isPathSafe(): bool
    {
        $fullPath = $this->getCurrentFullPath();
        $themePath = $this->getThemePath();

        // Pastikan path masih dalam direktori tema
        return Str::startsWith($fullPath, $themePath);
    }

    protected function buatFileTema(string $tema): void
    {
        $basePath = public_path('tema/' . $tema);

        // Layout.blade.php di root
        $layoutContent = "<!DOCTYPE html>\n<html lang=\"id\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>@yield('judul', 'Website Saya')</title>\n    <meta name=\"description\" content=\"@yield('deskripsi', 'Website Saya')\">\n</head>\n<body>\n    @yield('konten')\n</body>\n</html>";
        File::put($basePath . '/layout.blade.php', $layoutContent);

        // Layout/default.blade.php
        $layoutDefaultContent = "<!DOCTYPE html>\n<html lang=\"id\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>@yield('judul', 'Website Saya')</title>\n    <meta name=\"description\" content=\"@yield('deskripsi', 'Website Saya')\">\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\n</head>\n<body>\n    <header class=\"bg-light py-3\">\n        <div class=\"container\">\n            <h1>Website Saya</h1>\n        </div>\n    </header>\n    <main class=\"container py-4\">\n        @yield('konten')\n    </main>\n    <footer class=\"bg-light py-3\">\n        <div class=\"container text-center\">\n            <p>&copy; " . date('Y') . " Website Saya</p>\n        </div>\n    </footer>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>\n</body>\n</html>";
        File::put($basePath . '/layout/default.blade.php', $layoutDefaultContent);

        // Halaman.blade.php
        $halamanContent = "@extends('tema::layout')\n@section('judul', \$halaman->judul.' - Website Saya')\n@section('deskripsi', Str::limit(strip_tags(\$halaman->isi), 160))\n@section('konten')\n<div>\n    <h1>{{ \$halaman->judul }}</h1>\n    <div>{!! \$halaman->isi !!}</div>\n</div>\n@endsection";
        File::put($basePath . '/halaman.blade.php', $halamanContent);

        // Config.php
        $configContent = "<?php\nreturn [\n    'id' => '{$tema}',\n    'nama' => 'Tema {$tema}',\n    'deskripsi' => 'Tema {$tema} buatan saya',\n    'gambar' => '{$tema}.png',\n];";
        File::put($basePath . '/config.php', $configContent);

        // Beranda.blade.php
        $berandaContent = "@extends('tema::layout')\n@section('judul', 'Beranda - Website Saya')\n@section('deskripsi', 'Halaman utama website saya')\n@section('konten')\n<div>\n    <h1>Beranda</h1>\n    <p>Selamat datang di website saya.</p>\n</div>\n@endsection";
        File::put($basePath . '/beranda.blade.php', $berandaContent);

        // Artikel/daftar.blade.php
        $artikelDaftarContent = "@extends('tema::layout')\n@section('judul', 'Blog - Website Saya')\n@section('deskripsi', 'Daftar artikel di website saya')\n@section('konten')\n<div>\n    <h1>Artikel</h1>\n    <div>\n        @foreach(\$artikels as \$artikel)\n            <div>\n                <h2><a href=\"{{ url('artikel/'.\$artikel->slug) }}\">{{ \$artikel->judul }}</a></h2>\n                <p>{{ Str::limit(strip_tags(\$artikel->isi), 200) }}</p>\n            </div>\n        @endforeach\n    </div>\n</div>\n@endsection";
        File::put($basePath . '/artikel/daftar.blade.php', $artikelDaftarContent);

        // Artikel/default.blade.php
        $artikelDefaultContent = "@extends('tema::layout')\n@section('judul', 'Blog - Website Saya')\n@section('deskripsi', 'Daftar artikel di website saya')\n@section('konten')\n<div>\n    <h1>Kategori Artikel</h1>\n    <div>\n        @foreach(\$kategoris as \$kategori)\n            <div>\n                <h2><a href=\"{{ url('kategori/'.\$kategori->slug) }}\">{{ \$kategori->nama }}</a></h2>\n                <p>{{ \$kategori->deskripsi }}</p>\n            </div>\n        @endforeach\n    </div>\n</div>\n@endsection";
        File::put($basePath . '/artikel/default.blade.php', $artikelDefaultContent);

        // Artikel/detail.blade.php
        $artikelDetailContent = "@extends('tema::layout')\n@section('judul', \$artikel->judul.' - Website Saya')\n@section('deskripsi', Str::limit(strip_tags(\$artikel->isi), 160))\n@section('konten')\n<div>\n    <h1>{{ \$artikel->judul }}</h1>\n    <div>\n        <p>Tanggal: {{ \$artikel->created_at->format('d M Y') }}</p>\n        <div>{!! \$artikel->isi !!}</div>\n    </div>\n</div>\n@endsection";
        File::put($basePath . '/artikel/detail.blade.php', $artikelDetailContent);

        // Modul/header.blade.php
        $modulHeaderContent = "<header class=\"bg-primary text-white py-4 mb-4\">\n    <div class=\"container\">\n        <div class=\"row align-items-center\">\n            <div class=\"col-md-6\">\n                <h1 class=\"mb-0\">{{ \$judul ?? 'Website Saya' }}</h1>\n            </div>\n            <div class=\"col-md-6 text-md-end\">\n                <nav>\n                    <a href=\"{{ url('/') }}\" class=\"text-white me-3\">Beranda</a>\n                    <a href=\"{{ url('/artikel') }}\" class=\"text-white me-3\">Artikel</a>\n                    <a href=\"{{ url('/kontak') }}\" class=\"text-white\">Kontak</a>\n                </nav>\n            </div>\n        </div>\n    </div>\n</header>";
        File::put($basePath . '/modul/header.blade.php', $modulHeaderContent);

        // Modul/footer.blade.php
        $modulFooterContent = "<footer class=\"bg-dark text-white py-4 mt-5\">\n    <div class=\"container\">\n        <div class=\"row\">\n            <div class=\"col-md-6\">\n                <h5>Website Saya</h5>\n                <p>Menyediakan informasi terbaik untuk Anda.</p>\n            </div>\n            <div class=\"col-md-3\">\n                <h5>Tautan</h5>\n                <ul class=\"list-unstyled\">\n                    <li><a href=\"{{ url('/') }}\" class=\"text-white\">Beranda</a></li>\n                    <li><a href=\"{{ url('/artikel') }}\" class=\"text-white\">Artikel</a></li>\n                    <li><a href=\"{{ url('/kontak') }}\" class=\"text-white\">Kontak</a></li>\n                </ul>\n            </div>\n            <div class=\"col-md-3\">\n                <h5>Kontak</h5>\n                <address>\n                    Jl. Contoh No. 123<br>\n                    Kota Contoh, 12345<br>\n                    Indonesia<br>\n                    <a href=\"mailto:<EMAIL>\" class=\"text-white\"><EMAIL></a>\n                </address>\n            </div>\n        </div>\n        <div class=\"row mt-3\">\n            <div class=\"col-12 text-center\">\n                <p class=\"mb-0\">&copy; " . date('Y') . " Website Saya. Hak Cipta Dilindungi.</p>\n            </div>\n        </div>\n    </div>\n</footer>";
        File::put($basePath . '/modul/footer.blade.php', $modulFooterContent);
    }

    /**
     * Override nama permission agar halaman ini memakai permission
     * `view_tema_cms` (prefix resource) alih-alih `page_Tema` default.
     */
    protected static function getPermissionName(): string
    {
        return 'view_tema_cms';
    }
}

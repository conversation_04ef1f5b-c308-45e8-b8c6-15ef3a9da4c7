<x-filament-panels::page>
    {{ $this->table }}

    @php
        $data = $this->getRingkasanData();
    @endphp

    <div class="mt-4 grid grid-cols-1 md:grid-cols-5 gap-4">
        <!-- Statistik Pendapatan -->
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold tracking-tight">Pendapatan Hari Ini</h2>
                <x-heroicon-o-banknotes class="w-8 h-8 text-success-500" />
            </div>
            <div class="mt-2">
                <p class="text-xl font-bold">{{ $data['pendapatan_hari_ini'] }}</p>
            </div>
            <x-slot name="footer">
                <div class="flex justify-end">
                    <x-filament::button 
                        color="gray" 
                        icon="heroicon-m-arrow-right" 
                        icon-position="after"
                        tag="a"
                        href="{{ static::getResource()::getUrl('pendapatan') }}"
                    >
                        Lihat Detail
                    </x-filament::button>
                </div>
            </x-slot>
        </x-filament::section>

        <!-- Statistik Check-in Hari Ini -->
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold tracking-tight">Check-in Hari Ini</h2>
                <x-heroicon-o-arrow-right-circle class="w-8 h-8 text-primary-500" />
            </div>
            <div class="mt-2">
                <p class="text-xl font-bold">{{ $data['check_in_hari_ini'] }}</p>
            </div>
            <x-slot name="footer">
                <div class="flex justify-end">
                    <x-filament::button 
                        color="gray" 
                        icon="heroicon-m-arrow-right" 
                        icon-position="after"
                        tag="a"
                        href="{{ static::getResource()::getUrl('okupansi') }}"
                    >
                        Lihat Detail
                    </x-filament::button>
                </div>
            </x-slot>
        </x-filament::section>

        <!-- Statistik Kamar Terpakai -->
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold tracking-tight">Kamar Terpakai</h2>
                <x-heroicon-o-home class="w-8 h-8 text-warning-500" />
            </div>
            <div class="mt-2">
                <p class="text-xl font-bold">{{ $data['kamar_terpakai'] }}</p>
                <p class="text-sm text-gray-500">Okupansi: {{ $data['persentase_okupansi'] }}</p>
            </div>
            <x-slot name="footer">
                <div class="flex justify-end">
                    <x-filament::button 
                        color="gray" 
                        icon="heroicon-m-arrow-right" 
                        icon-position="after"
                        tag="a"
                        href="{{ static::getResource()::getUrl('okupansi') }}"
                    >
                        Lihat Detail
                    </x-filament::button>
                </div>
            </x-slot>
        </x-filament::section>

        <!-- Statistik Jumlah Tamu -->
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold tracking-tight">Jumlah Tamu</h2>
                <x-heroicon-o-user-group class="w-8 h-8 text-info-500" />
            </div>
            <div class="mt-2">
                <p class="text-xl font-bold">{{ $data['jumlah_tamu'] }}</p>
            </div>
            <x-slot name="footer">
                <div class="flex justify-end">
                    <x-filament::button 
                        color="gray" 
                        icon="heroicon-m-arrow-right" 
                        icon-position="after"
                        tag="a"
                        href="{{ static::getResource()::getUrl('tamu') }}"
                    >
                        Lihat Detail
                    </x-filament::button>
                </div>
            </x-slot>
        </x-filament::section>

        <!-- Navigasi Laporan Lainnya -->
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold tracking-tight">Laporan Lainnya</h2>
                <x-heroicon-o-chart-bar class="w-8 h-8 text-danger-500" />
            </div>
            <div class="mt-2 space-y-2">
                <x-filament::button 
                    size="sm"
                    color="gray" 
                    icon="heroicon-m-currency-dollar" 
                    tag="a"
                    href="{{ static::getResource()::getUrl('transaksi') }}"
                    class="w-full justify-start"
                >
                    Laporan Transaksi
                </x-filament::button>
                <x-filament::button 
                    size="sm"
                    color="gray" 
                    icon="heroicon-m-credit-card" 
                    tag="a"
                    href="{{ static::getResource()::getUrl('pembayaran') }}"
                    class="w-full justify-start"
                >
                    Laporan Pembayaran
                </x-filament::button>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
<?php

namespace Modules\RajaGambar\Services;

use Spatie\Image\Image;
use Spatie\Image\Enums\CropPosition;
use Spatie\Image\Enums\Fit;
use Spatie\Image\Enums\BorderType;
use Spatie\Image\Enums\Orientation;
use Spatie\Image\Enums\FlipDirection;
use Illuminate\Support\Facades\Storage;
use Exception;

class RajaGambarService
{
    protected $image;
    protected $config;

    public function __construct()
    {
        $this->config = config('rajagambar', []);
    }

    /**
     * Load gambar dari path
     */
    public function load(string $path): self
    {
        try {
            $this->image = Image::load($path);
            return $this;
        } catch (Exception $e) {
            throw new Exception("Gagal memuat gambar: " . $e->getMessage());
        }
    }

    /**
     * Load gambar dari URL
     */
    public function loadFromUrl(string $url): self
    {
        try {
            $this->image = Image::load($url);
            return $this;
        } catch (Exception $e) {
            throw new Exception("Gagal memuat gambar dari URL: " . $e->getMessage());
        }
    }

    /**
     * Set width dengan mempertahankan aspect ratio
     */
    public function width(int $width): self
    {
        $this->validateImage();
        $this->image->width($width);
        return $this;
    }

    /**
     * Set height dengan mempertahankan aspect ratio
     */
    public function height(int $height): self
    {
        $this->validateImage();
        $this->image->height($height);
        return $this;
    }

    /**
     * Resize gambar dengan width dan height
     */
    public function resize(int $width, int $height): self
    {
        $this->validateImage();
        $this->image->resize($width, $height);
        return $this;
    }

    /**
     * Fit gambar dalam dimensi tertentu
     */
    public function fit(int $width, int $height, string $fitMethod = null): self
    {
        $this->validateImage();
        
        $fitMethod = $fitMethod ?? $this->config['default_fit_method'] ?? 'contain';
        $fit = $this->getFitEnum($fitMethod);
        
        $this->image->fit($fit, $width, $height);
        return $this;
    }

    /**
     * Crop gambar
     */
    public function crop(int $width, int $height, string $cropMethod = null): self
    {
        $this->validateImage();
        
        $cropMethod = $cropMethod ?? $this->config['default_crop_method'] ?? 'crop-center';
        $cropPosition = $this->getCropPositionEnum($cropMethod);
        
        $this->image->crop($width, $height, $cropPosition);
        return $this;
    }

    /**
     * Focal crop dengan titik fokus dan zoom
     */
    public function focalCrop(int $width, int $height, int $focalX, int $focalY, int $zoom = 1): self
    {
        $this->validateImage();
        
        // Validasi zoom (1-100)
        $zoom = max(1, min(100, $zoom));
        
        $this->image->focalCrop($width, $height, $focalX, $focalY, $zoom);
        return $this;
    }

    /**
     * Manual crop dengan koordinat spesifik
     */
    public function manualCrop(int $startX, int $startY, int $width, int $height): self
    {
        $this->validateImage();
        $this->image->manualCrop($startX, $startY, $width, $height);
        return $this;
    }

    /**
     * Optimize gambar untuk mengurangi ukuran file
     */
    public function optimize(): self
    {
        $this->validateImage();
        $this->image->optimize();
        return $this;
    }

    /**
     * Adjust brightness (-100 to 100)
     */
    public function brightness(int $brightness): self
    {
        $this->validateImage();
        
        // Validasi range brightness
        $brightness = max(-100, min(100, $brightness));
        
        $this->image->brightness($brightness);
        return $this;
    }

    /**
     * Adjust contrast (-100 to 100)
     */
    public function contrast(int $contrast): self
    {
        $this->validateImage();
        
        // Validasi range contrast
        $contrast = max(-100, min(100, $contrast));
        
        $this->image->contrast($contrast);
        return $this;
    }

    /**
     * Adjust gamma (0.1 to 9.99)
     */
    public function gamma(float $gamma): self
    {
        $this->validateImage();
        
        // Validasi range gamma
        $gamma = max(0.1, min(9.99, $gamma));
        
        $this->image->gamma($gamma);
        return $this;
    }

    /**
     * Colorize gambar dengan nilai RGB (-100 to 100 each)
     */
    public function colorize(int $red, int $green, int $blue): self
    {
        $this->validateImage();
        
        // Validasi range RGB
        $red = max(-100, min(100, $red));
        $green = max(-100, min(100, $green));
        $blue = max(-100, min(100, $blue));
        
        $this->image->colorize($red, $green, $blue);
        return $this;
    }

    /**
     * Set background color untuk gambar transparan
     */
    public function background(string $color = null): self
    {
        $this->validateImage();
        
        $color = $color ?? $this->config['default_background'] ?? '#ffffff';
        
        $this->image->background($color);
        return $this;
    }

    /**
     * Tambah border pada gambar
     */
    public function border(int $width = null, string $borderType = null, string $color = null): self
    {
        $this->validateImage();
        
        $width = $width ?? $this->config['border']['width'] ?? 1;
        $borderType = $borderType ?? $this->config['border']['type'] ?? 'overlay';
        $color = $color ?? $this->config['border']['color'] ?? '#000000';
        
        $borderTypeEnum = $this->getBorderTypeEnum($borderType);
        
        $this->image->border($width, $borderTypeEnum, $color);
        return $this;
    }

    /**
     * Rotasi gambar
     */
    public function orientation(string $orientation): self
    {
        $this->validateImage();
        
        $orientationEnum = $this->getOrientationEnum($orientation);
        
        $this->image->orientation($orientationEnum);
        return $this;
    }

    /**
     * Flip gambar (mirror)
     */
    public function flip(string $direction): self
    {
        $this->validateImage();

        $flipEnum = $this->getFlipDirectionEnum($direction);

        $this->image->flip($flipEnum);
        return $this;
    }

    /**
     * Tambah watermark pada gambar
     */
    public function watermark(string $watermarkPath, ?string $position = null, ?int $opacity = null, ?int $padding = null): self
    {
        $this->validateImage();

        $position = $position ?? $this->config['watermark']['position'] ?? 'bottom-right';
        $opacity = $opacity ?? $this->config['watermark']['opacity'] ?? 50;
        $padding = $padding ?? $this->config['watermark']['padding'] ?? 10;

        // Load watermark image
        $watermark = Image::load($watermarkPath);

        // Set opacity
        $watermark->opacity($opacity);

        // Apply watermark based on position
        $this->image->watermark($watermark, $this->getWatermarkPosition($position), $padding);

        return $this;
    }

    /**
     * Tambah text overlay pada gambar
     */
    public function text(string $text, ?string $position = null, ?int $fontSize = null, ?string $color = null, ?int $padding = null, ?string $fontPath = null): self
    {
        $this->validateImage();

        $position = $position ?? $this->config['text']['position'] ?? 'bottom-right';
        $fontSize = $fontSize ?? $this->config['text']['font_size'] ?? 16;
        $color = $color ?? $this->config['text']['color'] ?? '#000000';
        $padding = $padding ?? $this->config['text']['padding'] ?? 10;
        $fontPath = $fontPath ?? $this->config['text']['font_path'] ?? null;

        // Apply text overlay
        $this->image->text($text, $this->getTextPosition($position), $fontSize, $color, $fontPath, $padding);

        return $this;
    }

    /**
     * Simpan gambar ke path
     */
    public function save(string $path = null): string
    {
        $this->validateImage();
        
        if ($path) {
            $this->image->save($path);
            return $path;
        }
        
        // Generate path otomatis jika tidak disediakan
        $disk = $this->config['storage']['disk'] ?? 'public';
        $storagePath = $this->config['storage']['path'] ?? 'rajagambar';
        $filename = uniqid('rajagambar_') . '.jpg';
        $fullPath = $storagePath . '/' . $filename;
        
        // Simpan ke storage
        $tempPath = storage_path('app/temp/' . $filename);
        $this->image->save($tempPath);
        
        // Pindah ke storage yang ditentukan
        Storage::disk($disk)->put($fullPath, file_get_contents($tempPath));
        
        // Hapus file temporary
        unlink($tempPath);
        
        return Storage::disk($disk)->url($fullPath);
    }

    /**
     * Get base64 representation of image
     */
    public function toBase64(): string
    {
        $this->validateImage();
        return $this->image->base64();
    }

    /**
     * Validasi apakah gambar sudah dimuat
     */
    protected function validateImage(): void
    {
        if (!$this->image) {
            throw new Exception("Gambar belum dimuat. Gunakan method load() terlebih dahulu.");
        }
    }

    /**
     * Convert string fit method ke enum
     */
    protected function getFitEnum(string $fitMethod): Fit
    {
        return match($fitMethod) {
            'contain' => Fit::Contain,
            'max' => Fit::Max,
            'fill' => Fit::Fill,
            'stretch' => Fit::Stretch,
            'crop' => Fit::Crop,
            default => Fit::Contain,
        };
    }

    /**
     * Convert string crop method ke enum
     */
    protected function getCropPositionEnum(string $cropMethod): CropPosition
    {
        return match($cropMethod) {
            'crop-top-left' => CropPosition::TopLeft,
            'crop-top' => CropPosition::Top,
            'crop-top-right' => CropPosition::TopRight,
            'crop-left' => CropPosition::Left,
            'crop-center' => CropPosition::Center,
            'crop-right' => CropPosition::Right,
            'crop-bottom-left' => CropPosition::BottomLeft,
            'crop-bottom' => CropPosition::Bottom,
            'crop-bottom-right' => CropPosition::BottomRight,
            default => CropPosition::Center,
        };
    }

    /**
     * Convert string border type ke enum
     */
    protected function getBorderTypeEnum(string $borderType): BorderType
    {
        return match($borderType) {
            'overlay' => BorderType::Overlay,
            'shrink' => BorderType::Shrink,
            'expand' => BorderType::Expand,
            default => BorderType::Overlay,
        };
    }

    /**
     * Convert string orientation ke enum
     */
    protected function getOrientationEnum(string $orientation): Orientation
    {
        return match($orientation) {
            'rotate0' => Orientation::Rotate0,
            'rotate90' => Orientation::Rotate90,
            'rotate180' => Orientation::Rotate180,
            'rotate270' => Orientation::Rotate270,
            default => Orientation::Rotate0,
        };
    }

    /**
     * Convert string flip direction ke enum
     */
    protected function getFlipDirectionEnum(string $direction): FlipDirection
    {
        return match($direction) {
            'horizontal' => FlipDirection::Horizontal,
            'vertical' => FlipDirection::Vertical,
            'both' => FlipDirection::Both,
            default => FlipDirection::Horizontal,
        };
    }

    /**
     * Get watermark position
     */
    protected function getWatermarkPosition(string $position): string
    {
        return match($position) {
            'top-left' => 'top-left',
            'top-right' => 'top-right',
            'bottom-left' => 'bottom-left',
            'bottom-right' => 'bottom-right',
            'center' => 'center',
            default => 'bottom-right',
        };
    }

    /**
     * Get text position
     */
    protected function getTextPosition(string $position): string
    {
        return match($position) {
            'top-left' => 'top-left',
            'top-right' => 'top-right',
            'bottom-left' => 'bottom-left',
            'bottom-right' => 'bottom-right',
            'center' => 'center',
            default => 'bottom-right',
        };
    }
}

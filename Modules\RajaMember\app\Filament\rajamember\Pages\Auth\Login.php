<?php

namespace Modules\RajaMember\Filament\rajamember\Pages\Auth;

use Filament\Pages\Auth\Login as BaseLogin;
use Filament\Forms\Components\Component;
use Filament\Actions\Action;
use Illuminate\Contracts\Support\Htmlable;

class Login extends BaseLogin
{
    public function getTitle(): string|Htmlable
    {
        return 'Login Member';
    }

    public function getHeading(): string|Htmlable
    {
        return 'Selamat Datang Kembali';
    }

    public function getSubheading(): string|Htmlable
    {
        return 'Masuk ke akun member Anda';
    }

    protected function getFormActions(): array
    {
        return [
            $this->getAuthenticateFormAction(),
            $this->getRegisterFormAction(),
        ];
    }

    protected function getRegisterFormAction(): Action
    {
        return Action::make('register')
            ->label('Daftar')
            ->url('/member/register')
            ->color('gray')
            ->outlined()
            ->size('lg');
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }

    public function mount(): void
    {
        parent::mount();
        
        // Auto fill untuk development
        // if (app()->environment('local')) {
        //     $this->form->fill([
        //         'email' => '<EMAIL>',
        //         'password' => 'password',
        //     ]);
        // }
    }
}

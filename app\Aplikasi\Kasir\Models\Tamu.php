<?php

namespace App\Aplikasi\Kasir\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @mixin IdeHelperTamu
 */
class Tamu extends Model
{
    protected $table = 'tamu';
    protected $fillable = [
      'user_id',
        'nama',
       'email',
       'telpon',
       'jenis_identitas',
       'foto_identitas',
         'no_identitas',
    ];

  protected static function boot()
    {
        parent::boot();
      
        static::addGlobalScope('jenis_tamu', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis_tamu', 'KASIR'); 
         });

        static::creating(function ($model)   {   $model->jenis = 'KASIR';   });
        static::updating(function ($model)    {   $model->jenis = 'KASIR';    });
    }


}

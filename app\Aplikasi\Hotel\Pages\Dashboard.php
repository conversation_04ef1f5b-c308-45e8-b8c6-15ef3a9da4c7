<?php

namespace App\Aplikasi\Hotel\Pages;

use App\Aplikasi\Hotel\Widgets\CalendarWidget;
use App\Aplikasi\Hotel\Widgets\KalenderReservasiWidget;
use App\Aplikasi\Hotel\Widgets\OccupancyChart;
use App\Aplikasi\Hotel\Widgets\RecentReservations;
use App\Aplikasi\Hotel\Widgets\RoomStatus;
use App\Aplikasi\Hotel\Widgets\StatsOverview;
use Filament\Actions\Action;
use Filament\Pages\Page;
use Saade\FilamentFullCalendar\Widgets\FullCalendarWidget;

class Dashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static string $view = 'hotel::pages.dashboard-karyawan';

    protected static ?string $title = 'Dashboard Karyawan';

    protected static ?string $navigationLabel = 'Dashboard ';
    protected static bool $shouldRegisterNavigation = false;


    protected function getHeaderActions(): array
    {
        return [
            Action::make('tambah')
                ->url(fn(): string => route('filament.hotel.resources.reservasi.create'))
                ->icon('heroicon-o-plus')
                ->label('Tambah Reservasi')
                // ->color('success')
                ->openUrlInNewTab(),
        ];
    }


    protected function getHeaderWidgets(): array
    {
        return [
            //   StatsOverview::class,
            // ShiftWidget::class,
            // TotalPenjualanShiftWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
           
            KalenderReservasiWidget::make(['columnSpan' => 2]),
           
            // RecentReservations::class,
        ];
    }
 

    public function getHeaderWidgetsColumns(): int|array
    {
        return 1;
    }

    public function getFooterWidgetsColumns(): int|array
    {
        return 1;
    }
}

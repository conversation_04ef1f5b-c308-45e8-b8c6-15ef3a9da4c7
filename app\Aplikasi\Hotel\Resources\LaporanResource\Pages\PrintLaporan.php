<?php

namespace App\Aplikasi\Hotel\Resources\LaporanResource\Pages;

use App\Aplikasi\Hotel\Resources\LaporanResource;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Session;

class PrintLaporan extends Page
{
    // Deklarasikan resource yang terkait dengan halaman ini
    protected static string $resource = LaporanResource::class;
    
    // Konfigurasi navigasi dan judul
    protected static ?string $navigationLabel = 'Print';
    protected static ?string $slug = 'print';
    protected static bool $shouldRegisterNavigation = false; // Sembunyikan dari navigasi
    protected static ?string $navigationIcon = 'heroicon-o-printer';
    protected static ?string $title = 'CETAK LAPORAN';
    
    // Gunakan view custom tanpa layout
    protected static string $view = 'hotel::laporan.cetak';

    /**
     * Menyiapkan data untuk tampilan laporan cetak
     * 
     * @return array
     */
    protected function getViewData(): array
    {
        // Ambil data dari session
        $laporanData = session('laporan_data', []);
        
        // Jika data tidak ada, berikan array default
        if (empty($laporanData)) {
            return [
                'columns' => [],
                'rows' => [],
                'summary' => [],
                'filters' => [
                    'tanggal_mulai' => '-',
                    'tanggal_akhir' => '-',
                    'bulan' => 'Semua Bulan',
                    'tahun' => 'Semua Tahun',
                    'checkin_dari' => '-',
                    'checkin_sampai' => '-',
                    'checkout_dari' => '-',
                    'checkout_sampai' => '-',
                    'status_reservasi' => 'Semua Status',
                    'status_pembayaran' => 'Semua Status',
                    'tamu' => 'Semua Tamu',
                    'petugas' => 'Semua Petugas',
                ],
                'auto_print' => false,
            ];
        }
        
        // Tambahkan flag untuk auto print jika diperlukan
        $laporanData['auto_print'] = false;
        
        return $laporanData;
    }
}
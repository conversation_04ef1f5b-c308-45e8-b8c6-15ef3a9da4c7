<?php

namespace App\Aplikasi\Hotel\Resources\ReservasiResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Table;
use App\Models\Pembayaran;
use App\Models\Reservasi;

class KamarRelationManager extends RelationManager
{
    protected static string $relationship = 'kamar';

    protected static ?string $title = 'Kamar';

    public function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\Select::make('kategori_id')
                ->relationship('tipe', 'nama')
                ->required()
                ->label('Tipe Kamar'),
            Forms\Components\TextInput::make('nama')
                ->required()
                ->maxLength(255)
                ->label('Nama Kamar'),
            Forms\Components\TextInput::make('harga')
                ->required()
                ->numeric()
                ->label('Harga'),
            Forms\Components\TextInput::make('harga_modal')
                ->numeric()
                ->label('Harga Modal'),
            Forms\Components\TextInput::make('stok')
                ->numeric()
                ->default(1)
                ->label('Jumlah Kamar'),
            Forms\Components\FileUpload::make('gambar')
                ->image()
                ->directory('kamar')
                ->label('Gambar Kamar'),
            Forms\Components\Textarea::make('ket')
                ->maxLength(65535)
                ->label('Keterangan'),
            Forms\Components\TagsInput::make('fasilitas')
                ->label('Fasilitas'),
            Forms\Components\Toggle::make('tampil')
                ->default(true)
                ->label('Tampilkan')
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
        ->columns([
            Tables\Columns\TextColumn::make('tipe.nama')
                ->searchable()
                ->sortable()
                ->label('Tipe Kamar'),
            Tables\Columns\TextColumn::make('nama')
                ->searchable()
                ->sortable()
                ->label('Nama Kamar'),
            Tables\Columns\TextColumn::make('harga')
                ->money('IDR')
                ->sortable()
                ->label('Harga'),
            Tables\Columns\ImageColumn::make('gambar')
                ->label('Gambar'),
            Tables\Columns\IconColumn::make('is_available')
                ->boolean()
                ->label('Tersedia'),
            Tables\Columns\ToggleColumn::make('tampil')
                ->label('Tampilkan'),
        ])
        ->filters([
            Tables\Filters\SelectFilter::make('kategori_id')
                ->relationship('tipe', 'nama')
                ->label('Tipe Kamar'),
        ])
        ->actions([
            Tables\Actions\ViewAction::make(),
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
            ]),
        ]);
    }

 
}
<?php

namespace App\Aplikasi\Hotel\Models;

 
use App\Models\Kategori;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model; 

/**
 * @mixin IdeHelperKamarTipe
 */
class KamarTipe extends Kategori
{



    protected static function booted()
    {
        static::addGlobalScope('toko_id', function (Builder $builder) {

            $builder->where('jenis', '<PERSON><PERSON><PERSON>');

        });

        static::creating(function ($model) {
            $model->jenis = 'KAMAR';
        });

        static::updating(function ($model) {
            $model->jenis = 'KAMAR';
        });
    }

    public function kamar()
    {
        return $this->hasMany(Kamar::class, 'id');
    }



    public function getFasilitasAttribute()
    {
        return $this->kamar()->first()?->fasilitas ?? [];
    }


}

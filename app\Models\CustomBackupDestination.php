<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\File;

use <PERSON><PERSON>roRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackup;
use Sushi\Sushi;

/**
 * @property string $path
 * @property string $disk
 * @property string $date
 * @property string $size
 * @property string $type
 * @mixin IdeHelperCustomBackupDestination
 */
class CustomBackupDestination extends Model
{
    use Sushi;

    public function getRows(): array
    {
        $data = [];

        // Dapatkan backup dari Spatie
        foreach (FilamentSpatieLaravelBackup::getDisks() as $disk) {
            $spatieData = FilamentSpatieLaravelBackup::getBackupDestinationData($disk);

            // Tambahkan kolom 'type' ke setiap item
            foreach ($spatieData as $item) {
                $item['type'] = 'full'; // Default tipe untuk backup Spatie
                $data[] = $item;
            }
        }

        // Tambahkan backup per tabel dari direktori storage/app/backups
        $backupsPath = storage_path('app/backups');
        if (File::exists($backupsPath)) {
            $files = File::files($backupsPath);

            foreach ($files as $file) {
                // Hanya tambahkan file ZIP
                if ($file->getExtension() === 'zip') {
                    $path = 'backups/' . $file->getFilename();

                    // Periksa apakah file sudah ada di data
                    $exists = false;
                    foreach ($data as $item) {
                        if ($item['path'] === $path) {
                            $exists = true;
                            break;
                        }
                    }

                    if (!$exists) {
                        // Tentukan tipe backup berdasarkan nama file
                        $type = 'full';
                        if (preg_match('/[a-z_]+\.\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.zip/', $file->getFilename())) {
                            $type = 'table';
                        }

                        $data[] = [
                            'disk' => 'local',
                            'path' => $path,
                            'date' => Carbon::createFromTimestamp($file->getMTime())->format('Y-m-d H:i:s'),
                            'size' => $this->formatSize($file->getSize()),
                            'type' => $type,
                        ];
                    }
                }
            }
        }

        return $data;
    }

    private function formatSize(int $sizeInBytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = $sizeInBytes > 0 ? floor(log($sizeInBytes, 1024)) : 0;

        return number_format($sizeInBytes / pow(1024, $power), 2, '.', ',') . ' ' . $units[$power];
    }
}
